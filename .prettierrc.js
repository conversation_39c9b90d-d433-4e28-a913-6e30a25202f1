/**

 * @see https://prettier.nodejs.cn/docs/en/configuration.html

 * @type {import("prettier").Config}
 */
const config = {
	useTabs: true,
	tabWidth: 2,
	semi: true,
	singleQuote: true,
	jsxSingleQuote: true,
	bracketSpacing: true,
	trailingComma: 'none',
	bracketSameLine: false,
	arrowParens: 'always',
	printWidth: 100,
	endOfLine: 'lf',
	vueIndentScriptAndStyle: true,
	embeddedLanguageFormatting: 'auto',
	singleAttributePerLine: true,
	overrides: [
		{
			files: '*.json',
			options: {
				printWidth: 200
			}
		}
	],
	proseWrap: 'preserve',
	htmlWhitespaceSensitivity: 'ignore'
};

export default config;
