


# node 版本

20.11.0

# 本地开发流程

```
1. git 仓库下载代码
2. yarn install 安装node_modules
3. vite.config.js 配置文件
4. 本地开发和测试环境和生产环境配置文件参考  .env.development 等文件 举例如下
VITE_NODE_ENV = "development" //开发环境
VITE_STPT_SKIP_URL = "https://iemstest.teems.com.cn/api/sso/login" //生态平台单点登录地址
VITE_LOGIN_URL = "https://iemstest.teems.com.cn/#/"  //标准登录页面地址
VITE_API_URL = "https://iemstest.teems.com.cn/api" //api请求地址
5. yarn dev or npm run dev 
```

# 本地开发额外配置

暂无



# 生产环境打包

```
yarn build or npm run build
```

