 .ant-form-item .ant-form-item-label>label {
     color: #092649;
     font-family: "SourceHanSansCN-Regular";
     font-size: 16px;
     //  min-width: 100px;
     margin: 4px 0;
 }
 .ant-form-item-label {
   padding:  0 0 6px !important;
}
 .ant-form-inline {
     .ant-form-item-label {
         position: relative;
         top: -3px;
     }
     
 }
//  水平的form表单的label 的间距 
 .layout_row{
    .ant-form-item-label>label {
        margin: 0 0 !important;
    }
 }

 // 表格的header的类目分界线 需要隐藏
 .ant-table-cell {
     &::before {
         display: none;
     }
 }

 .ant-table {
     color: #092649 !important;
     font-family: SourceHanSansCN-Regular;
     font-weight: 400;
     font-size: 14px;
 }

 .ant-table-thead {
     font-size: 16px !important;
     .ant-table-cell{
        font-family: "SourceHanSansCN-Medium";
     }
 }

 .ant-select-selector {
    border: 1px solid #dde2ed !important;
}
 .ant-pagination {
     font-family: SourceHanSansCN-Regular;
     font-weight: 400;
     font-size: 14px;
     color: #6A7582;

     .ant-pagination-item {
         a {
             font-family: SourceHanSansCN-Regular;
             font-weight: 400;
             font-size: 14px;
             color: #6A7582;
         }
     }

     .ant-pagination-item:not(.ant-pagination-item-active):hover {
         background: #117BFD;
         border-radius: 5px;

         a {
             font-family: SourceHanSansCN-Regular;
             font-weight: 400;
             font-size: 14px;
             color: #FFFFFF;
         }
     }

     .ant-pagination-options {
         .ant-select-selector {
             font-family: SourceHanSansCN-Regular;
             font-weight: 400;
             font-size: 14px;
             color: #6A7582;
         }
     }


     .ant-select-selector {
         height: 36px !important;
         line-height: 34px !important;
         border: 1px solid #dde2ed !important;
     }

     .ant-pagination-item-active {
         background: #117BFD;
         border-radius: 5px;

         a {
             color: #FFFFFF;
         }

         &:hover {
             a {
                 color: #FFFFFF;
             }
         }
     }

     .ant-pagination-options-quick-jumper input {
         font-family: SourceHanSansCN-Regular;
         font-weight: 400;
         font-size: 14px;
         color: #6A7582;
         border-color: #DDE2ED;
     }

     .ant-pagination-next {
         &:hover {
             button {
                 color: #FFFFFF;
             }
         }
     }

     .ant-select {
         position: relative;

         .ant-select-dropdown {
             left: 0 !important;
             top: -156px !important;
         }
     }

 }

 .ant-select {
    position: relative;
    .ant-select-dropdown {
        position: absolute;
        top: 36px !important;
        left: 0 !important;
        .ant-select-item-option-content {
            font-size: 14px !important;
        }
    }
 }


 .ant-select .ant-select-arrow {
     transform: scaleX(1.3);
 }

 .ant-picker-clear {
     font-size: 18px;
 }


 .ant-popconfirm-buttons {
     display: flex;
     flex-direction: row;
     justify-content: center;

     button {

         &:nth-child(1) {
             margin-right: 32px;
         }
     }
 }

 .ant-popover-arrow {
     display: none !important;
 }

 .ant-popover-inner {
     //  width: 380px;
     //  height: 186px;
     //  padding: 40px !important;

     .ant-popover-inner-content {
         height: 100%;
         width: 100%;
         position: relative;
     }

     .ant-popconfirm-message {
         img {
             height: 24px;
             width: 24px;
         }

         .ant-popconfirm-message-title {
             height: 24px !important;
             line-height: 24px;
             font-family: SourceHanSansCN-Regular;
             font-weight: 400;
             font-size: 16px;
             color: #092649;
         }
     }

     .ant-popconfirm-buttons {
         position: absolute;
         bottom: 0;
         width: 100%;
     }
 }


 // 特殊的提示信息
 .unique_form_error_tips {
     .ant-form-item-explain-connected {
         padding-left: 20px;
     }
 }


 //  省市区下拉disabled 
 .ant-tree-select-dropdown .ant-select-tree .ant-select-tree-treenode-disabled .ant-select-tree-node-content-wrapper {
     color: rgba(9, 38, 73, 0.88);
     cursor: pointer;
 }

 // antd 可编辑表格的最后一个cloumn 因为功能原因需要隐藏
 .ant_edit_table_last_cloumn {
     .ant-table-thead {
         tr {
             th {
                 &:last-child {
                     background: transparent;
                     border-bottom: 0px solid transparent;
                 }
             }
         }
     }

     .ant-table-tbody {
         tr {
             height: 68px;

             td {
                 padding: 0 16px;
             }
         }

         // table编辑表格的form提示效果的样式修改
         .ant-col.ant-form-item-control {
             position: relative;
         }

         .ant-form-item-control-input+div {
             position: absolute;
             bottom: -20px;
             left: 0;
             overflow: hidden;
             width: 300px;
             z-index: 999;
         }

         .ant-form-item {
             width: 100%;
         }

     }
 }

 // form表单的样式
 .ant-form-item {
     margin-bottom: 14px !important;
 }

 //  //  switch的颜色设置
 //  .ant-switch {
 //      //  width: 46px !important;
 //      //  height: 26px;

 //      .ant-switch-handle {
 //          //  top: 4px !important;
 //      }

 //  }

 .ant-switch-checked {
     background: rgba(3, 227, 161, 0.2) !important;
     border: 1px solid rgba(3, 227, 161, 1) !important;
     box-sizing: content-box;

     .ant-switch-handle {
         //  top: 3px !important;
         //  inset-inline-start: calc(100% - 24px);

         &::before {
             background: rgba(3, 227, 161, 1) !important;
         }
     }
 }

 .ant-switch:hover:not(.ant-switch-disabled) {
     background: rgba(3, 227, 161, 0.2) !important;
 }


 // 标准按钮的悬停颜色
 .ant-btn-primary:not(:disabled):hover {
     background-color: #2b8efc !important;
 }

 .ant-drawer {
     position: fixed;
     .ant-drawer-title{
        font-size: 18px !important;
     }
     .ant-drawer-header{
        padding: 23px 24px;
     }
     .ant-drawer-close {
         position: absolute;
         right: 0;
         span{
            height: 16px;
            width: 16px;
            display: block;
            background-image: url("/assets/icon/modal_close_1.png");
            background-size: 100%;
            &:hover{
                background-image: url("/assets/icon/modal_close_hover.png");
                
            }
            svg{
                display: none;
            }
         }
     }
 }

 .ant-drawer-body {
     padding: 28px !important;
     padding-top: 16px !important;
 }

 // 上传图片的组件部分修改 
 .ant-upload.ant-upload-select {
     border: 1px solid #E9EDF5 !important;
     background: #F2F8FF !important;
 }

 .ant-upload.ant-upload-select:not(.ant-upload-disabled):hover {
     background: #e8f1fc !important;
 }

 .ant-upload.ant-upload-select-picture-card {
     height: 80px;
     width: 80px;
     box-sizing: border-box;
     border: 1px solid #E9EDF5;
     background-color: #F2F8FF;
     border-radius: 5px;
     margin: 0;
     // copy的暂时留存
 }

 // 上传图片的预览按钮的大小
 .ant-upload-list-item-actions {
     a {
         display: block;
         width: 100%;
         text-align: center;
     }

     // copy的暂时留存
 }

 // 表格嵌套表格的下拉展开关闭的按钮的+号换成三角箭头
 button.ant-table-row-expand-icon {
     border: none !important;
     background-color: transparent !important;

     &::after {
         //  display: none !important;
         display: inline-block !important;
         background-color: transparent !important;
         content: url("/assets/icon/table_arrow.png") !important;
         height: 12px !important;
         width: 7px !important;
         top: 0 !important;
         bottom: 0 !important;

         &:hover {
             content: url("/assets/icon/table_arrow_hover.png") !important;
         }
     }

     &::before {
         display: none !important;
     }

     .ant-table-row-expand-icon-collapsed {
         //  background-image: url("/assets/icon/algorithm.png");
         //  background-image: url("/assets/icon/home_hover.png") !important;


     }

     .ant-table-row-expand-icon-expanded {
         //  background-image: url("/assets/icon/home_hover.png") !important;

     }
 }

 .ant-modal-header {
     border-bottom: 1px solid #DDE2ED;
     height: 42px;
 }

 .ant-modal-footer {
     display: flex;
     flex-direction: row;
     justify-content: space-evenly;
 }

 //  .ant-tooltip-content {
 //      width: 350px;
 //  }

 .ant-form-item-control-input+div {
     width: max-content !important;
 }



 .ant-select-selection-search-input {
     height: 34px !important;
 }

 .ant-select-selection-placeholder {
     // line-height: 34px !important;
 }

 .ant-select-selection-item {
     line-height: 34px !important;
 }

 .ant-form-item-control-input-content {
     height: 36px !important;
     line-height: 36px;
 }

 .ant-picker {
     height: 36px !important;
     padding: 6px 11px 6px !important;
     border: 1px solid #dde2ed !important;
 }

 .ant-btn {
    /* 去掉投影 */
    box-shadow: none !important;
     height: 36px !important;
     padding: 6px 15px !important;
     line-height: 22px !important;
     min-width: 90px;
     font-size: 16px !important;
    font-family: "SourceHanSansCN-Regular";
     img {
         display: inline-block;
         height: 16px;
         width: 16px;
        //  position: relative;
        //  top: 3px;
         margin-right: 5px;
     }
 }
 .ant-btn-default{
    border-color: #dde2ed ;
 }

 .ant-form-item-control-input {
     height: 36px !important;
 }

 .ant-input {
     height: 36px !important;
     border-color: #dde2ed !important;
 }


 .ant-select-selection-placeholder {
     line-height: 34px !important;
 }


 .ant-table-row {
     .ant-table-cell {
         max-height: 60px !important;
        //  padding: 16px 16px !important;
     }
 }



 // 特殊的表格样式 嵌套
 .unique_table {
     .ant-table-expanded-row>.ant-table-cell {
         padding-left: 0 !important;
         padding-right: 0 !important;
         background-color: #FFFFFF !important;

         .ant-table {
             //  margin-inline: 50px 0px !important;
             margin-inline: 0px 0px !important;
             background-color: #f2f8ff !important;
         }
     }

     .ant-table-wrapper tr.ant-table-expanded-row>td {
         background-color: #FFFFFF !important;
     }
 }

 .ant-empty {
     .ant-empty-image {
         display: block;
         //  width: 260px;
         height: 160px;
         margin: 0 auto;
         aspect-ratio: 285/302;
         background-image: url("/assets/icon/nodata.png");
         background-repeat: no-repeat;
         background-position: center;
         background-size: 100% 100%;

         svg {
             display: none;
         }
     }
 }

 .ant-table-wrapper .ant-table-column-sorters {
     justify-content: flex-start !important;

     .ant-table-column-title {
         width: max-content;
     }
 }

 /* 覆盖 Ant Design 的禁用按钮样式 */
.ant-btn-default:disabled {
  background-color: #ffffff !important; /* 自定义背景色 */
  color: rgba(9, 38, 73, 0.528) !important; /* 自定义文字颜色 */
  border-color: rgba(221, 226, 237, 0.6) !important; /* 自定义边框颜色 */
  cursor: not-allowed !important; /* 保持不可点击的鼠标样式 */
}


