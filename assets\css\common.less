html,
body {
    margin: 0;
    padding: 0;
}

html {
    height: 100%;
    width: 100%;
    // min-width: 1400px;
    min-width: 1500px;

}

body {
    height: 100%;
    width: 100%;
}

#app {
    height: 100%;
    width: 100%;
    overflow: hidden;
}

// 全局布局样式  ：
.page_all {
    background-color: rgba(242, 248, 255, 1);
    // 减去了顶部header的高度
    height: calc(100% - 65px);
    display: flex;
    flex-direction: column;
}

.current_page {
    padding-top: 28px;
    box-sizing: border-box;
}

.main_contain {
    padding: 28px;
    box-sizing: border-box;
    flex: 1;
    overflow: hidden;

    .main_contain_scroll {
        height: 100%;
        padding: 28px;
        padding-bottom: 0px;
        box-sizing: border-box;
        overflow-y: auto;
        background-color: rgba(255, 255, 255, 1);
        border-radius: 8px;
        position: relative;
        display: flex;
        flex-direction: column;
    }

    .main_contain_scroll_item_list {
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
        border-radius: 8px;
    }
}

.main_contain_thirdMenu {
    padding: 12px 28px 32px 28px !important;
}


@default_font_color: #092649;

.page_header_title {
    color: @default_font_color;
    font-size: 18px;
    font-family: "SourceHanSansCN-Bold";
    height: 18px;
    line-height: 18px;
    font-weight: bold;
}

// 全局基础样式





// antd-drawer底部的确定取消按钮 布局修改
.drawer_footer {
    display: flex;
    flex-direction: row;
    justify-content: center;

    .ant-space-item {
        margin: 0 16px;
    }
}

// antd中button按钮的样式
.icon_button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;

    img {
        display: block;
        height: 18px;
        width: 18px;
        margin-right: 11px;
    }

    span {
        // display: block;
        // height: 14px;
        // line-height: 14px;
    }

}


// 状态标签
.success_tag {
    background-color: rgba(3, 227, 161, 0.2) !important;
    border: 1px solid #03e3a1 !important;
    color: #07d297 !important;
    width: 46px;
    height: 26px;
    border-radius: 20px;
    line-height: 26px;
    text-align: center;
}

.error_tag {
    background-color: rgba(255, 97, 97, 0.2) !important;
    border: 1px solid #ff6161 !important;
    color: #ff6161 !important;
    width: 46px;
    height: 26px;
    border-radius: 20px;
    line-height: 26px;
    text-align: center;
}

.warning_tag {
    background-color: rgba(253, 148, 25, 0.2) !important;
    border: 1px solid #FD9419 !important;
    color: #FD9419 !important;
    width: 46px;
    height: 26px;
    border-radius: 20px;
    line-height: 26px;
    text-align: center;
}

.prompt_tag {
    background-color: rgba(3,227,161,0.14) !important;
    border: 1px solid #07D297 !important;
    color: #07D297 !important;
    width: 46px;
    height: 26px;
    border-radius: 20px;
    line-height: 26px;
    text-align: center;
}

.warning_color {
    color: #FF6161 !important;
}

.normal_tag {
    background-color: rgba(17, 129, 253, 0.2) !important;
    border: 1px solid #1181FD !important;
    color: #1181FD !important;
    width: 46px;
    height: 26px;
    border-radius: 20px;
    line-height: 26px;
    text-align: center;
}

.disabled_tag {
    background-color: rgba(237, 244, 252, 1) !important;
    border: 1px solid rgba(237, 244, 252, 1) !important;
    color: rgba(237, 244, 252, 1) !important;
    width: 46px;
    height: 26px;
    border-radius: 12px;
    line-height: 26px;
    text-align: center;
}

// a标签禁用颜色
a.disabled {
    font-family: SourceHanSansCN-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #1181FD;
    opacity: 0.6;
}

// 自定义form样式效果 one
.form_self {
    .form_item {
        margin-bottom: 23px;

        .form_label {
            height: 16px;
            line-height: 16px;
            font-family: SourceHanSansCN-Regular;
            font-weight: 400;
            font-size: 16px;
            color: #9CA2AF;
        }

        .form_value {
            margin-top: 16px;
            min-height: 14px;
            line-height: 14px;
            font-family: SourceHanSansCN-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #092649;
        }
    }
}

.form_self_normal {
    margin-top: 26px;

    .form_item {
        margin-bottom: 23px;

        .form_label {
            height: 14px;
            line-height: 14px;
            font-family: SourceHanSansCN-Regular;
            font-weight: 400;
            font-size: 16px;
            color: #092649;
        }

        .form_value {
            margin-top: 16px;

        }

        .require_true {
            display: inline-block;
            color: #ff4d4f;
            font-family: SimSun, sans-serif;
            line-height: 1;
            content: "*";
            font-style: normal;
            margin-right: 3px;
        }
    }
}

.center_line {
    width: 6px;
    height: 1px;
    background: #092649;
    margin: 0 8px;
    display: inline-block;
}



// 三级菜单的展示样式
.third_menu_contain {
    height: 65px;
    background: #ffffff;
    border-radius: 8px;
    box-sizing: border-box;
    margin: 0px 28px;
    padding: 0 28px;
    position: relative;
    display: flex;

    .third_menu_item {
        height: 100%;
        line-height: 65px;
        margin-right: 25px;
        position: relative;
        cursor: pointer;

        .third_menu_name {

            font-family: SourceHanSansCN-Regular;
            font-weight: 400;
            font-size: 18px;
            color: #9ca2af;
        }

        .third_menu_line {
            height: 3px;
            background: #1181fd;
            border-radius: 2px;
            width: 100%;
            display: none;
            position: absolute;
            bottom: 0;
        }
    }

    .third_menu_item.active {
        .third_menu_name {
            font-family: SourceHanSansCN-Regular;
            font-weight: bold;
            color: #1181fd;
        }

        .third_menu_line {
            display: block;
        }
    }

    .third_menu_contain_button_add {
        position: absolute;
        top: 50%;
        right: 32px;
        transform: translate(0%, -50%)
    }
}

// ***********************************全局 滚动条样式的修改************************************** 
::-webkit-scrollbar {
    width: 8px !important;
    height: 4px;
    // background-color: white;
    background-color: transparent;

}

::-webkit-scrollbar-thumb {
    border-radius: 5px !important;
    background: #D4DAE2;

    &:hover {
        background: #a3b1c2;
    }
}

// 全局的加载等待
.full_page_loading {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 9999;
    background-color: rgba(255, 255, 255, 1);

    img {
        position: absolute !important;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}

.ull_page_has_data {
    display: block;
}

.button_icon_type {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 90px;
    height: 36px;
}




.ant-tooltip-inner {
    font-family: SourceHanSansCN-Regular;
    font-weight: 400;
    font-size: 14px;
    color: #092649;
    min-height: unset !important;
}


// 超过两行省略号
.ellipsis_table {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: white-box;
}

.table_header_sort {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    .table_header_sort_icon {
        font-size: 12px;
        margin-left: 5px;
        color: rgba(156, 162, 175, 1);
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

}

// 删除框的样式
.modal_delete {
    .ant-modal-close {
        top: 23px !important;
        right: 32px !important;
    }

    .ant-modal-content {
        padding: 0 !important;
    }

    .ant-modal-header {
        height: 68px !important;
        line-height: 68px;
        margin-bottom: 0 !important;
        border-bottom: 1px solid rgba(221, 226, 237, 1);
    }

    .ant-modal-title {
        height: 68px !important;
        line-height: 68px;
        font-family: SourceHanSansCN-Medium;
        font-weight: 500;
        font-size: 18px;
        color: #092649;
        padding-left: 40px;
    }

    .ant-modal-body {
        height: 160px !important;
    }

    .ant-modal-footer {
        margin-top: 0 !important;
        height: 80px !important;
        display: fex;
        flex-direction: row;
        justify-content: center;
        align-items: center;

        button {
            &:first-child {
                margin-right: 32px;
            }
        }
    }
}

.delete_modal_mess_contain {
    height: 160px;
    border-bottom: 1px solid rgba(233, 237, 245, 1);
    display: flex;
    flex-direction: row;
    // align-items: center;

    justify-content: flex-start;
    padding: 0 57px;
    padding-top: 40px;
    box-sizing: border-box;

    .waring_icon {
        height: 32px;
        width: 32px;
        border-radius: 50%;
        background-image: url('/assets/icon/tips_right.png');
        background-size: cover;
        background-repeat: no-repeat;
        font-size: 16px;
        margin-right: 15px;
    }

    .warning_mess {
        font-family: SourceHanSansCN-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #092649;
        flex: 1;
    }
}

.ant-btn {
    .button_icon_add {
        position: relative;
        top: 2px;
    }
}