import http from '/tool/http.js'


// 根据字典查询 
// 1. 查询元素字典  传参  ElEMENT_TYPE
// 2. 查询 对应元素的设备的 设备类型 传参 元素编码 GF GKB CN 等

export const getDictHttp = (params) => {
    let url = `/api/dict/findSimpleList/${params}`;
    return new Promise((resolve, reject) => {
        http.post(url, {}).then((data) => {
            if (data.code == 200) {
                resolve(data.data)
            }
        }).catch((errorInfo) => {
            reject({
                message: errorInfo
            })
        });
    })
}
// 查询设备类型列表(用于关联设备页面)
export const getDeviceTypeRelAssetSimpleList = (params) => {
    let url = `/api/select/getDeviceTypeRelAssetSimpleList`;
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200) {
                resolve(data.data)
            } else {
                // 空数据
                reject({
                    message: '数据为空'
                })
            }
        }).catch((errorInfo) => {
            reject({
                message: errorInfo
            })
        });
    })
}


// 根据设备类型查询量测列表
export const getDeviceListHttp = (params) => {
    let url = `/api/param/getByDeviceType/${params}`;
    return new Promise((resolve, reject) => {
        http.post(url, {}).then((data) => {
            if (data.code == 200) {
                resolve(data.data)
            }
        }).catch((errorInfo) => {
            reject({
                message: errorInfo
            })
        });
    })
}


// 根据项目ID获取控制器列表  
export const getEageDeviceListHttp = (params) => {
    let url = "/api/edge/findPage";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200) {
                resolve({
                    total: data.data.total,
                    list: data.data.records
                })
            } else {
                // 空数据
            }
        }).catch((errorInfo) => {
            reject({
                message: errorInfo
            })
        });
    })
}

// 查询项目下面边端控制器列表

export const getEdgeSimpleListHttp = (params) => {
    let url = `/api/select/getEdgeSimpleList/${params}`;
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200) {
                resolve(data.data)
            } else {
                reject({
                    message: data.msg
                })
            }
        }).catch((errorInfo) => {
            reject({
                message: errorInfo
            })
        });
    })
}


// /file/getObjectUrl

export const getObjectUrl = (params) => {
    let url = `/api/file/getObjectUrl/`;
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200) {
                resolve(data)
            } else {
                reject({
                    message: data.msg
                })
            }
        }).catch((errorInfo) => {
            reject({
                message: errorInfo
            })
        });
    })
}