// 处理搜索的开始和结束的时间函数
import dayjs from 'dayjs';
import { colorList, hexToRgba, jian_gu } from '/assets/js/echartsConfig.js';
import * as echarts from 'echarts';
import { message } from 'ant-design-vue';

// 传入开始时间和结束时间自动给出对应的开始和结束的具体到秒的值
export const handleTime = (start, end, time_type) => {
	let startTime = '';
	let endTime = '';

	switch (time_type) {
		case 'year':
			// 年展示月的粒度
			startTime = dayjs(start).startOf(time_type).format('YYYY-MM');
			endTime = dayjs(end).endOf(time_type).format('YYYY-MM');
			if (dayjs(endTime).isAfter(dayjs())) {
				// 结束时间如果在当前时间之后则选择当前时间作为结束时间
				endTime = dayjs().format('YYYY-MM');
			}
			break;
		case 'month':
			// 年展示天的粒度
			startTime = dayjs(start).startOf(time_type).format('YYYY-MM-DD');
			endTime = dayjs(end).endOf(time_type).format('YYYY-MM-DD');
			if (dayjs(endTime).isAfter(dayjs())) {
				// 结束时间如果在当前时间之后则选择当前时间作为结束时间
				endTime = dayjs().format('YYYY-MM-DD');
			}
			break;
		case 'day':
			// 年展示15分钟为单位 的粒度
			startTime = dayjs(start).startOf(time_type).format('YYYY-MM-DD HH:mm');
			endTime = dayjs(end).endOf(time_type).format('YYYY-MM-DD HH:mm');
			if (dayjs(endTime).isAfter(dayjs())) {
				// 结束时间如果在当前时间之后则选择当前时间作为结束时间
				endTime = dayjs().format('YYYY-MM-DD HH:mm');
			}
			break;
		default:
			// 默认返回全时间
			startTime = dayjs(start).startOf(time_type).format('YYYY-MM-DD HH:mm:ss');
			endTime = dayjs(end).endOf(time_type).format('YYYY-MM-DD HH:mm:ss');
			if (dayjs(endTime).isAfter(dayjs())) {
				// 结束时间如果在当前时间之后则选择当前时间作为结束时间
				endTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
			}
			break;
	}

	return {
		startTime,
		endTime
	};
};

export const handleTimeParams = (start, end, time_type) => {
	let startTime = '';
	let endTime = '';
	startTime = dayjs(start).startOf(time_type).format('YYYY-MM-DD HH:mm:ss');
	endTime = dayjs(end).endOf(time_type).format('YYYY-MM-DD HH:mm:ss');
	if (dayjs(endTime).isAfter(dayjs())) {
		// 结束时间如果在当前时间之后则选择当前时间作为结束时间
		endTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
	}
	return {
		startTime,
		endTime
	};
};
// 处理line的图表的echarts option的值  -----   双x轴
export const handleLineMapOption = (
	xAxis_data,
	map_data,
	xAxis_data_contrast,
	map_data_contrast,
	unit,
	contract_color
) => {
	// xAxis_data,  x轴数据
	// map_data, y轴数据
	// xAxis_data_contrast, x轴对比数据
	// map_data_contrast, y轴对比数据
	// unit 单位

	let contract_color_current = '#aec8e5';
	// 绘制图表
	let bgColor = 'transparent';
	// 图例的数据整理
	let mapLegendData = [];
	let mapLegendData_contrast = [];
	let legend_all = [];
	// 整理出yData的所有数据
	let yDataList = [];
	let yDataList_contrast = [];
	let max = 0;
	for (let i = 0; i < map_data.length; i++) {
		mapLegendData.push(map_data[i].name);
		yDataList.push(map_data[i].valueList);
		mapLegendData_contrast.push(map_data_contrast[i].name + '对比');
		yDataList_contrast.push(map_data_contrast[i].valueList);
		legend_all.push(map_data[i].name);
		legend_all.push(map_data_contrast[i].name + '对比');
		// 获取y轴的最大值
		let value_list_temp = map_data[i].valueList.concat(map_data_contrast[i].valueList);
		let max_temp = Math.max(...value_list_temp);
		max_temp > max ? (max = max_temp) : null;
	}
	// 遍历产生series的数据
	let seriesList = [];
	for (let i = 0; i < yDataList.length; i++) {
		seriesList.push(
			{
				name: mapLegendData[i],
				// time_unique: xAxis_data[i],
				type: 'line',
				symbol: 'circle',
				smooth: true,
				symbolSize: 6,
				zlevel: 3,
				labelLine: {
					lineStyle: {
						color: colorList[i]
						// shadowBlur: 3,
						// shadowColor: hexToRgba(colorList[i], 0.3),
						// shadowOffsetY: 0
					}
				},

				itemStyle: {
					borderColor: 'rgba(255,255,255,1)',
					borderWidth: 1,
					color: colorList[i]
				},
				areaStyle: {
					color: new echarts.graphic.LinearGradient(
						0,
						0,
						0,
						1,
						[
							{
								offset: 0,
								color: hexToRgba(colorList[i], 0.1)
							},
							{
								offset: 1,
								color: hexToRgba(colorList[i], 0)
							}
						],
						false
					)
					// shadowColor: hexToRgba(colorList[i], 0.1),
					// shadowBlur: 10
				},
				data: yDataList[i],
				markLine: {}
			},
			{
				name: mapLegendData_contrast[i],
				// time_unique: xAxis_data_contrast[i],
				type: 'line',
				symbol: 'circle',
				smooth: true,
				symbolSize: 6,
				// zlevel: 3,
				labelLine: {
					lineStyle: {
						color: hexToRgba(contract_color_current, 1)
						// shadowBlur: 3,
						// shadowColor: hexToRgba(colorList[i], 0.3),
						// shadowOffsetY: 0
					}
				},

				itemStyle: {
					borderColor: 'rgba(255,255,255,1)',
					borderWidth: 1,
					color: hexToRgba(colorList[i], 0.3)
				},
				areaStyle: {
					color: new echarts.graphic.LinearGradient(
						0,
						0,
						0,
						1,
						[
							{
								offset: 0,
								color: hexToRgba(contract_color_current, 0.1)
							},
							{
								offset: 1,
								color: hexToRgba(contract_color_current, 0)
							}
						],
						false
					),
					shadowColor: hexToRgba(contract_color_current, 0.1),
					shadowBlur: 10
				},
				data: yDataList_contrast[i],
				markLine: {}
			}
		);
	}

	let option = {
		backgroundColor: 'transparent',
		color: colorList,
		legend: {
			show: true,
			type: 'scroll',
			center: true,
			top: 0,
			data: legend_all,
			width: 920,
			itemWidth: 24,
			itemGap: 76, //图例之间的距离
			textStyle: {
				color: '#092649'
			}
		},
		// calculable: true,
		tooltip: {
			trigger: 'axis',
			formatter: function (params) {
				let html = '';
				params.forEach((v) => {
					let time = xAxis_data[v.dataIndex];
					if (v.seriesName.includes('对比')) {
						time = xAxis_data_contrast[v.dataIndex];
					}
					let value = v.value;
					// if (value != null) {
					//   value = parseFloat(v.value).toFixed(2)
					// } else {
					//   value = '-'
					// }
					if (value != 0 && !value) {
						value = '-';
					} else {
						value = Number(parseFloat(v.value).toFixed(2));
					}
					html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${v.color};"></span>
                    ${v.seriesName} : ${time} <span style="color:${v.color};font-weight:600;font-size: 14px"> ${value} </span>   ${unit}
                    `;
				});
				return html;
			},
			extraCssText:
				'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;'
			// axisPointer: {
			//     type: 'shadow',
			//     shadowStyle: {
			//         // color: '#ffffff',
			//         shadowColor: 'rgba(225,225,225,1)',
			//         shadowBlur: 5
			//     }
			// }
		},
		dataZoom: [
			{
				// type: 'slider',
				start: 0,
				end: 100,
				bottom: 20,
				height: 30
			}
			// {
			//   start: 0,
			//   end: 100
			// }
		],
		grid: {
			top: 50,
			containLabel: true, //区域是否包含坐标轴标签
			left: 40,
			right: 0,
			bottom: 60
		},
		xAxis: [
			{
				show: true,
				type: 'category',
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: ['transparent'],
						width: 1
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},

				data: xAxis_data
			},
			{
				show: true,
				type: 'category',
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: ['transparent'],
						width: 1
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},

				data: xAxis_data_contrast
			}
		],
		yAxis: [
			{
				type: 'value',
				name: unit,
				max: max,
				offset: -2, //y轴坐标刻度偏移位置
				nameTextStyle: {
					color: '#949aa6',
					fontSize: 14,
					lineHeight: 40,
					fontWeight: 400,
					align: 'left',
					fontFamily: 'shsr',
					padding: [0, 0, 0, -40],
					width: 100
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC',
						width: 1
					}
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: 'transparent'
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},
				axisTick: {
					show: false
				}
			}
		],
		series: seriesList
	};
	return option;
};

// 处理line的图表的echarts option的值  ----- 增加判断是否有对比数据
export const handleLineMapSelection = (
	xAxis_data,
	map_data,
	xAxis_data_contrast,
	map_data_contrast,
	unit,
	contract_color
) => {
	// xAxis_data,  x轴数据
	// map_data, y轴数据
	// xAxis_data_contrast, x轴对比数据
	// map_data_contrast, y轴对比数据
	// unit 单位

	let contract_color_current = '#aec8e5';
	// 绘制图表
	let bgColor = 'transparent';
	// 图例的数据整理
	let mapLegendData = [];
	let mapLegendData_contrast = [];
	let legend_all = [];
	// 整理出yData的所有数据
	let yDataList = [];
	let yDataList_contrast = [];
	let max = 0;

	for (let i = 0; i < map_data.length; i++) {
		mapLegendData.push(map_data[i].name);
		yDataList.push(map_data[i].valueList);
		legend_all.push(map_data[i].name);
		if (map_data_contrast.length > 0) {
			mapLegendData_contrast.push(map_data_contrast[i].name + '对比');
			yDataList_contrast.push(map_data_contrast[i].valueList);
			legend_all.push(map_data_contrast[i].name + '对比');
		}
		// 获取y轴的最大值
		if (map_data_contrast.length > 0) {
			//console.log(map_data[i].valueList, 'map_data[i].valueList');
			//console.log(map_data_contrast[i].valueList, 'map_data_contrast[i].valueList');
			let value_list_temp = map_data[i].valueList.concat(map_data_contrast[i].valueList);
			let max_temp = Math.max(...value_list_temp);
			max_temp > max ? (max = max_temp) : null;
		} else {
			let value_list_temp = map_data[i].valueList;
			let max_temp = Math.max(...value_list_temp);
			max_temp > max ? (max = max_temp) : null;
		}
	}

	// 遍历产生series的数据
	let seriesList = [];

	for (let i = 0; i < yDataList.length; i++) {
		if (map_data_contrast.length > 0) {
			seriesList.push(
				{
					name: mapLegendData[i],
					// time_unique: xAxis_data[i],
					type: 'line',
					symbol: 'circle',
					smooth: true,
					symbolSize: 6,
					zlevel: 3,
					labelLine: {
						lineStyle: {
							color: colorList[i]
							// shadowBlur: 3,
							// shadowColor: hexToRgba(colorList[i], 0.3),
							// shadowOffsetY: 0
						}
					},

					itemStyle: {
						borderColor: 'rgba(255,255,255,1)',
						borderWidth: 1,
						color: colorList[i]
					},
					areaStyle: {
						color: new echarts.graphic.LinearGradient(
							0,
							0,
							0,
							1,
							[
								{
									offset: 0,
									color: hexToRgba(colorList[i], 0.1)
								},
								{
									offset: 1,
									color: hexToRgba(colorList[i], 0)
								}
							],
							false
						)
						// shadowColor: hexToRgba(colorList[i], 0.1),
						// shadowBlur: 10
					},
					data: yDataList[i],
					markLine: {}
				},
				{
					name: mapLegendData_contrast[i],
					// time_unique: xAxis_data_contrast[i],
					type: 'line',
					symbol: 'circle',
					smooth: true,
					symbolSize: 6,
					// zlevel: 3,
					labelLine: {
						lineStyle: {
							color: hexToRgba(contract_color_current, 1)
							// shadowBlur: 3,
							// shadowColor: hexToRgba(colorList[i], 0.3),
							// shadowOffsetY: 0
						}
					},

					itemStyle: {
						borderColor: 'rgba(255,255,255,1)',
						borderWidth: 1,
						color: hexToRgba(colorList[i], 0.3)
					},
					areaStyle: {
						color: new echarts.graphic.LinearGradient(
							0,
							0,
							0,
							1,
							[
								{
									offset: 0,
									color: hexToRgba(contract_color_current, 0.1)
								},
								{
									offset: 1,
									color: hexToRgba(contract_color_current, 0)
								}
							],
							false
						),
						shadowColor: hexToRgba(contract_color_current, 0.1),
						shadowBlur: 10
					},
					data: yDataList_contrast[i],
					markLine: {}
				}
			);
		} else {
			seriesList.push({
				name: mapLegendData[i],
				// time_unique: xAxis_data[i],
				type: 'line',
				symbol: 'circle',
				smooth: true,
				symbolSize: 6,
				zlevel: 3,
				labelLine: {
					lineStyle: {
						color: colorList[i]
						// shadowBlur: 3,
						// shadowColor: hexToRgba(colorList[i], 0.3),
						// shadowOffsetY: 0
					}
				},

				itemStyle: {
					borderColor: 'rgba(255,255,255,1)',
					borderWidth: 1,
					color: colorList[i]
				},
				areaStyle: {
					color: new echarts.graphic.LinearGradient(
						0,
						0,
						0,
						1,
						[
							{
								offset: 0,
								color: hexToRgba(colorList[i], 0.1)
							},
							{
								offset: 1,
								color: hexToRgba(colorList[i], 0)
							}
						],
						false
					)
					// shadowColor: hexToRgba(colorList[i], 0.1),
					// shadowBlur: 10
				},
				data: yDataList[i],
				markLine: {}
			});
		}
	}

	let option = {
		backgroundColor: 'transparent',
		color: colorList,
		legend: {
			show: true,
			type: 'scroll',
			center: true,
			top: 0,
			data: legend_all,
			width: 920,
			itemWidth: 24,
			itemGap: 76, //图例之间的距离
			textStyle: {
				color: '#092649'
			}
		},
		// calculable: true,
		tooltip: {
			trigger: 'axis',
			formatter: function (params) {
				let html = '';
				params.forEach((v) => {
					let time = xAxis_data[v.dataIndex];
					if (v.seriesName.includes('对比')) {
						time = xAxis_data_contrast[v.dataIndex];
					}
					let value = v.value;
					// if (value != null) {
					//   value = parseFloat(v.value).toFixed(2)
					// } else {
					//   value = '-'
					// }
					if (value != 0 && !value) {
						value = '-';
					} else {
						value = Number(parseFloat(v.value).toFixed(2));
					}
					html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${v.color};"></span>
                    ${v.seriesName} : ${time} <span style="color:${v.color};font-weight:600;font-size: 14px"> ${value} </span>   ${unit}
                    `;
				});
				return html;
			},
			extraCssText:
				'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;'
			// axisPointer: {
			//     type: 'shadow',
			//     shadowStyle: {
			//         // color: '#ffffff',
			//         shadowColor: 'rgba(225,225,225,1)',
			//         shadowBlur: 5
			//     }
			// }
		},
		dataZoom: [
			{
				// type: 'slider',
				start: 0,
				end: 100,
				bottom: 20,
				height: 30
			}
			// {
			//   start: 0,
			//   end: 100
			// }
		],
		grid: {
			top: 50,
			containLabel: true, //区域是否包含坐标轴标签
			left: 40,
			right: 22,
			bottom: 60
		},
		xAxis: [
			{
				show: true,
				type: 'category',
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: ['transparent'],
						width: 1
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},

				data: xAxis_data
			},
			{
				show: true,
				type: 'category',
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: ['transparent'],
						width: 1
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},

				data: xAxis_data_contrast
			}
		],
		yAxis: [
			{
				type: 'value',
				name: unit,
				max: max,
				offset: -2, //y轴坐标刻度偏移位置
				nameTextStyle: {
					color: '#949aa6',
					fontSize: 14,
					lineHeight: 40,
					fontWeight: 400,
					align: 'left',
					fontFamily: 'shsr',
					padding: [0, 0, 0, -40],
					width: 100
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC',
						width: 1
					}
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: 'transparent'
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},
				axisTick: {
					show: false
				}
			}
		],
		series: seriesList
	};
	return option;
};

// 处理line的图表的echarts option的值  -----   双x轴且无对比数据
export const handleNoContrastLineMapOption = (xAxis_data, map_data, unit, contract_color) => {
	// xAxis_data,  x轴数据
	// map_data, y轴数据
	// 图例的数据整理
	let mapLegendData = [];
	let legend_all = [];
	// 整理出yData的所有数据
	let yDataList = [];
	let max = 0;
	for (let i = 0; i < map_data.length; i++) {
		mapLegendData.push(map_data[i].name);
		yDataList.push(map_data[i].valueList);
		// mapLegendData_contrast.push(map_data_contrast[i].name + '对比')
		// yDataList_contrast.push(map_data_contrast[i].valueList)
		legend_all.push(map_data[i].name);
		// legend_all.push(map_data_contrast[i].name + '对比')
		// 获取y轴的最大值
		let value_list_temp = map_data[i].valueList;
		let max_temp = Math.max(...value_list_temp);
		max_temp > max ? (max = max_temp) : null;
	}

	// 遍历产生series的数据
	let seriesList = [];
	for (let i = 0; i < yDataList.length; i++) {
		seriesList.push({
			name: mapLegendData[i],
			// time_unique: xAxis_data[i],
			type: 'line',
			symbol: 'circle',
			smooth: true,
			symbolSize: 6,
			zlevel: 3,
			labelLine: {
				lineStyle: {
					color: colorList[i]
					// shadowBlur: 3,
					// shadowColor: hexToRgba(colorList[i], 0.3),
					// shadowOffsetY: 0
				}
			},

			itemStyle: {
				borderColor: 'rgba(255,255,255,1)',
				borderWidth: 1,
				color: colorList[i]
			},
			areaStyle: {
				color: new echarts.graphic.LinearGradient(
					0,
					0,
					0,
					1,
					[
						{
							offset: 0,
							color: hexToRgba(colorList[i], 0.1)
						},
						{
							offset: 1,
							color: hexToRgba(colorList[i], 0)
						}
					],
					false
				)
				// shadowColor: hexToRgba(colorList[i], 0.1),
				// shadowBlur: 10
			},
			data: yDataList[i],
			markLine: {}
		});
	}

	let option = {
		backgroundColor: 'transparent',
		color: colorList,
		legend: {
			show: true,
			type: 'scroll',
			center: true,
			top: 0,
			data: legend_all,
			width: 920,
			itemWidth: 24,
			itemGap: 76, //图例之间的距离
			textStyle: {
				color: '#092649'
			}
		},
		// calculable: true,
		tooltip: {
			trigger: 'axis',
			formatter: function (params) {
				let html = '';
				params.forEach((v) => {
					let time = xAxis_data[v.dataIndex];
					let value = v.value;
					if (value != 0 && !value) {
						value = '-';
					} else {
						value = Number(parseFloat(v.value).toFixed(2));
					}
					html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${v.color};"></span>
                    ${v.seriesName} : ${time} <span style="color:${v.color};font-weight:600;font-size: 14px"> ${value} </span>   ${unit}
                    `;
				});
				return html;
			},
			extraCssText:
				'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;'
		},
		dataZoom: [
			{
				// type: 'slider',
				start: 0,
				end: 100,
				bottom: 20,
				height: 30
			}
			// {
			//   start: 0,
			//   end: 100
			// }
		],
		grid: {
			top: 50,
			containLabel: true, //区域是否包含坐标轴标签
			left: 40,
			right: 0,
			bottom: 60
		},
		xAxis: [
			{
				show: true,
				type: 'category',
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: ['transparent'],
						width: 1
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},

				data: xAxis_data
			}
		],
		yAxis: [
			{
				type: 'value',
				name: unit,
				max: max,
				offset: -2, //y轴坐标刻度偏移位置
				nameTextStyle: {
					color: '#949aa6',
					fontSize: 14,
					lineHeight: 40,
					fontWeight: 400,
					align: 'left',
					fontFamily: 'shsr',
					padding: [0, 0, 0, -40],
					width: 100
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC',
						width: 1
					}
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: 'transparent'
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},
				axisTick: {
					show: false
				}
			}
		],
		series: seriesList
	};
	//console.log(seriesList);
	return option;
};

// 处理line的图表的echarts option的值  -----  单x轴
export const handleLineMapOptionSingal = (xAxis_data, map_data, unit, colors) => {
	// xAxis_data,  x轴数据
	// map_data, y轴数据
	// unit 单位
	// 绘制图表
	let bgColor = 'transparent';
	// 图例的数据整理
	let mapLegendData = [];
	let legend_all = [];
	// 整理出yData的所有数据
	let yDataList = [];
	let max = 0;
	for (let i = 0; i < map_data.length; i++) {
		mapLegendData.push(map_data[i].name);
		yDataList.push(map_data[i].valueList);
		legend_all.push(map_data[i].name);
		// 获取y轴的最大值
		let value_list_temp = map_data[i].valueList;
		let max_temp = Math.max(...value_list_temp);
		max_temp > max ? (max = max_temp) : null;
	}
	// 遍历产生series的数据
	let seriesList = [];
	let color = colorList;
	if (colors) {
		color = colors;
		console.log(color, '有颜色');
	}
	for (let i = 0; i < yDataList.length; i++) {
		seriesList.push({
			name: mapLegendData[i],
			type: 'line',
			symbol: 'circle',
			smooth: true,
			symbolSize: 6,
			// zlevel: 3,
			labelLine: {
				lineStyle: {
					color: color[i]
					// shadowBlur: 3,
					// shadowColor: hexToRgba(colorList[i], 0.3),
					// shadowOffsetY: 0
				}
			},

			itemStyle: {
				borderColor: 'rgba(255,255,255,1)',
				borderWidth: 1
			},
			areaStyle: {
				color: new echarts.graphic.LinearGradient(
					0,
					0,
					0,
					1,
					[
						{
							offset: 0,
							color: hexToRgba(color[i], 0.1)
						},
						{
							offset: 1,
							color: hexToRgba(color[i], 0)
						}
					],
					false
				),
				shadowColor: hexToRgba(color[i], 0.1),
				shadowBlur: 1
			},
			data: yDataList[i],
			markLine: {}
		});
	}

	// max = Math.ceil(max);
	let option = {
		backgroundColor: 'transparent',
		color: color,
		legend: {
			show: true,
			type: 'scroll',
			center: true,
			top: 0,
			data: legend_all,
			width: 920,
			itemWidth: 24,
			itemGap: 76, //图例之间的距离
			textStyle: {
				color: '#092649'
			}
		},
		// calculable: true,
		tooltip: {
			trigger: 'axis',
			formatter: function (params) {
				let html = '';
				params.forEach((v) => {
					let value = v.value;
					// if (value != null) {
					//   value = parseFloat(v.value).toFixed(2)
					// } else {
					//   value = '-'
					// }
					if (value != 0 && !value) {
						value = '-';
					} else {
						value = Number(parseFloat(v.value).toFixed(2));
					}
					html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${v.color};"></span>
                    ${v.seriesName} : ${v.name} <span style="color:${v.color};font-weight:600;font-size: 14px"> ${value} </span>   ${unit}
                    `;
				});
				return html;
			},
			extraCssText:
				'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;'
			// axisPointer: {
			//     type: 'shadow',
			//     shadowStyle: {
			//         // color: '#ffffff',
			//         shadowColor: 'rgba(225,225,225,1)',
			//         shadowBlur: 5
			//     }
			// }
		},
		dataZoom: [
			{
				start: 0,
				end: 100,
				bottom: 20,
				height: 30
			}
			// {
			//   start: 0,
			//   end: 100
			// }
		],
		grid: {
			top: 50,
			containLabel: true, //区域是否包含坐标轴标签
			left: 40,
			right: 22,
			bottom: 60
		},
		xAxis: [
			{
				show: true,
				type: 'category',
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: ['transparent'],
						width: 1
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},

				data: xAxis_data
			}
		],
		yAxis: [
			{
				type: 'value',
				name: unit,
				nameLocation: 'end',
				num: max,
				offset: -2, //y轴坐标刻度偏移位置
				nameTextStyle: {
					color: '#949aa6',
					fontSize: 14,
					lineHeight: 40,
					fontWeight: 400,
					align: 'left',
					fontFamily: 'shsr',
					padding: [0, 0, 0, -40],
					width: 100
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC',
						width: 1
					}
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: 'transparent'
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},
				axisTick: {
					show: false
				}
			}
		],
		series: seriesList
	};

	return option;
};

// 处理ehcarts 的坐标轴的展示时间,如果是同样的年份,则只展示月和天  如果不同的年份则展示年月日
export const handleXAxisTime = (data, date_type) => {
	let x_axis = [];
	let time_all = [];
	// let time_year = []
	let format_value = 'MM-DD HH:mm';
	if (date_type) {
		switch (date_type) {
			case 'year':
				// 年 按照 年内月展示   需要处理为月份的
				format_value = 'YYYY-MM';
				break;
			case 'month':
				// 月
				format_value = 'MM-DD';
				break;
			case 'date':
				// 日 按照 月内日展示
				format_value = 'MM-DD HH:mm';
				break;
			case 'time':
				// 时间按照 时 分展示
				format_value = 'HH:mm';
				break;
			default:
				format_value = 'YYYY-MM-DD HH:mm';
				break;
		}
	}

	for (let i = 0; i < data.length; i++) {
		let item = data[i];
		// 仅执行一次就行
		if (item.timePointer) {
			time_all.push(dayjs(item.timePointer).format(format_value));
		} else {
			time_all.push('');
		}
	}
	// time_year = [...new Set(time_year)]
	// if (time_year.length > 1) {
	//   x_axis = time_all
	// } else {
	//   x_axis = time_h_m_s
	// }
	x_axis = time_all;
	return x_axis;
};

// 处理echarts line 的x轴和y轴的展示数据的格式
export const init_x_y = (data, date_type) => {
	if (!data[0].detailList) {
		// message.error("缺少数据")
		return;
	}
	let x_axis = [];
	let y_axis = [];
	for (let j = 0; j < data.length; j++) {
		let elementCodeDesc = data[j].elementCodeDesc;
		let valueList = [];
		if (j == 0 && data[0].detailList.length > 0) {
			// 仅执行一次就行
			x_axis = handleXAxisTime(data[0].detailList, date_type);
		}
		for (let i = 0; i < data[j].detailList.length; i++) {
			// valueList.push(Math.ceil(200 * Math.random()))  data[j].detailList
			valueList.push(data[j].detailList[i].value);
		}

		y_axis.push({
			name: elementCodeDesc,
			valueList: valueList
		});
	}
	return {
		x_axis,
		y_axis
	};
};

// 处理echarts line 的x轴和y轴的展示数据的格式
export const init_x_y_device = (data) => {
	if (!data[0].deviceList) {
		// message.error("缺少数据")
		return;
	}
	let x_axis = [];
	let y_axis = [];
	let temp_data = data[0].deviceList;

	for (let j = 0; j < temp_data.length; j++) {
		let elementCodeDesc = temp_data[j].deviceName + '#SOC';
		let valueList = [];
		if (j == 0 && temp_data[0].detailList.length > 0) {
			// 仅执行一次就行
			x_axis = handleXAxisTime(temp_data[0].detailList);
		}
		for (let i = 0; i < temp_data[j].detailList.length; i++) {
			valueList.push(temp_data[j].detailList[i].value);
		}
		y_axis.push({
			name: elementCodeDesc,
			valueList: valueList
		});
	}
	return {
		x_axis,
		y_axis
	};
};

// 处理bar的图表的echarts option的值-----   双x轴
export const handleBarMapOption = (
	xaxis_data_echarts_bar,
	xaxis_data_echarts_bar_constract,
	yaxis_data_echarts_bar,
	yaxis_data_echarts_bar_constract,
	unit_echarts_bar,
	color,
	contract_color
) => {
	// xaxis_data_echarts_bar,  x轴
	// xaxis_data_echarts_bar_constract, 对比的x轴
	// yaxis_data_echarts_bar, y轴数据
	// yaxis_data_echarts_bar_constract,  对比y轴数据
	// unit_echarts_bar  单位

	let legend_data = [];
	let series_list = [];
	let color_1 = null;
	let color_2 = null;
	let contract_color_current = '#cadbee';

	for (let j = 0; j < yaxis_data_echarts_bar.length; j++) {
		if (color) {
			color_1 = hexToRgba(color, 1);
			color_2 = hexToRgba(color, 0.6);
		} else {
			color_1 = hexToRgba(colorList[j], 1);
			color_2 = hexToRgba(colorList[j], 0.6);
		}
		if (contract_color) {
			color_2 = contract_color;
		} else {
			color_2 = contract_color_current;
		}
		legend_data.push(yaxis_data_echarts_bar[j].name);
		legend_data.push(yaxis_data_echarts_bar_constract[j].name);
		series_list.push(
			{
				name: yaxis_data_echarts_bar[j].name,
				data: JSON.parse(JSON.stringify(yaxis_data_echarts_bar[j].value_list)),
				type: 'bar',
				itemStyle: {
					color: color_1
				},
				// barWidth: 18,
				barMaxWidth: 18,
				barMinWidth: 6,
				emphasis: {
					disabled: true,
					focus: 'none'
				}
				// xAxisIndex: 0,
			},
			{
				name: yaxis_data_echarts_bar_constract[j].name,
				data: JSON.parse(JSON.stringify(yaxis_data_echarts_bar_constract[j].value_list)),
				type: 'bar',
				itemStyle: {
					color: color_2
				},
				//   barWidth: 18,
				barMaxWidth: 18,
				barMinWidth: 6,
				emphasis: {
					disabled: true,
					focus: 'none'
				}
				// xAxisIndex: 1,
			}
		);
	}

	let option = {
		backgroundColor: 'transparent',
		// color: colorList,
		legend: {
			show: true,
			type: 'scroll',
			center: true,
			top: 0,
			width: 920,
			data: legend_data,
			itemWidth: 24,
			itemGap: 80, //图例之间的距离
			align: 'auto',
			textStyle: {
				color: '#092649'
			}
		},
		dataZoom: [
			{
				start: 0,
				end: 100,
				bottom: 20,
				height: 30
			}
			// {
			//   start: 0,
			//   end: 100
			// }
		],
		tooltip: {
			trigger: 'axis',
			formatter: function (params) {
				let html = '';
				params.forEach((v) => {
					//console.log(v.color);
					let time = xaxis_data_echarts_bar[v.dataIndex];
					if (v.seriesName.includes('对比')) {
						time = xaxis_data_echarts_bar_constract[v.dataIndex];
					}
					let value = v.value;
					// if (value != 0 && !value) {
					//   value = '-'
					// }
					if (value != 0 && !value) {
						value = '-';
					} else {
						value = Number(parseFloat(v.value).toFixed(2));
					}
					html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${v.color};"></span>
                    ${v.seriesName} : ${time} <span style="color:${v.color};font-weight:600;font-size: 14px"> ${value} </span>   ${unit_echarts_bar}
                    `;
				});
				return html;
			},
			extraCssText:
				'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;'
		},
		grid: {
			top: 50,
			containLabel: true, //区域是否包含坐标轴标签
			left: 40,
			right: 0,
			bottom: 60
		},
		xAxis: [
			{
				show: true,
				type: 'category',
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: ['transparent'],
						width: 1
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},

				data: xaxis_data_echarts_bar
			},
			// 对比的值坐标轴
			{
				show: true,
				// gridIndex: 1,
				type: 'category',
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: ['transparent'],
						width: 1
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},

				data: xaxis_data_echarts_bar_constract
			}
		],
		yAxis: [
			{
				type: 'value',
				name: unit_echarts_bar,
				offset: -2, //y轴坐标刻度偏移位置
				nameTextStyle: {
					color: '#9CA2AF',
					fontSize: 14,
					lineHeight: 40,
					fontWeight: 400,
					align: 'left',
					fontFamily: 'shsr',
					padding: [0, 0, 0, -40],
					width: 100
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC',
						width: 1
					}
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: 'transparent'
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},
				axisTick: {
					show: false
				}
			}
		],
		series: series_list
	};

	return option;
};

// 处理bar的图表的echarts option的值-----   双x轴 新增判断是否有对比数据，传入的颜色为数组

export const handleBarMapSelection = (
	xaxis_data_echarts_bar,
	xaxis_data_echarts_bar_constract,
	yaxis_data_echarts_bar,
	yaxis_data_echarts_bar_constract,
	unit_echarts_bar,
	colors,
	contract_color
) => {
	// xaxis_data_echarts_bar,  x轴
	// xaxis_data_echarts_bar_constract, 对比的x轴
	// yaxis_data_echarts_bar, y轴数据
	// yaxis_data_echarts_bar_constract,  对比y轴数据
	// unit_echarts_bar  单位

	let legend_data = [];
	let series_list = [];
	let color_1 = null;
	let color_2 = null;
	//console.log(yaxis_data_echarts_bar_constract, 'yaxis_data_echarts_bar_constract');

	for (let j = 0; j < yaxis_data_echarts_bar.length; j++) {
		if (colors && colors.length > 0) {
			color_1 = hexToRgba(colors[j], 1);
			color_2 = hexToRgba(colors[j], 0.5);
		} else {
			color_1 = hexToRgba(colorList[j], 1);
			color_2 = hexToRgba(colorList[j], 0.5);
		}
		if (yaxis_data_echarts_bar_constract.length > 0) {
			legend_data.push(yaxis_data_echarts_bar[j].name);
			legend_data.push(yaxis_data_echarts_bar_constract[j].name);
			series_list.push(
				{
					name: yaxis_data_echarts_bar[j].name,
					data: JSON.parse(JSON.stringify(yaxis_data_echarts_bar[j].value_list)),
					type: 'bar',
					itemStyle: {
						color: color_1
					},
					// barWidth: 18,
					barMaxWidth: 18,
					barMinWidth: 6,
					emphasis: {
						disabled: true,
						focus: 'none'
					}
					// xAxisIndex: 0,
				},
				{
					name: yaxis_data_echarts_bar_constract[j].name,
					data: JSON.parse(JSON.stringify(yaxis_data_echarts_bar_constract[j].value_list)),
					type: 'bar',
					itemStyle: {
						color: color_2
					},
					//   barWidth: 18,
					barMaxWidth: 18,
					barMinWidth: 6,
					emphasis: {
						disabled: true,
						focus: 'none'
					}
					// xAxisIndex: 1,
				}
			);
		} else {
			legend_data.push(yaxis_data_echarts_bar[j].name);
			series_list.push({
				name: yaxis_data_echarts_bar[j].name,
				data: JSON.parse(JSON.stringify(yaxis_data_echarts_bar[j].value_list)),
				type: 'bar',
				itemStyle: {
					color: color_1
				},
				// barWidth: 18,
				barMaxWidth: 18,
				barMinWidth: 6,
				emphasis: {
					disabled: true,
					focus: 'none'
				}
				// xAxisIndex: 0,
			});
		}
	}

	let myXAxis = [];
	if (yaxis_data_echarts_bar_constract.length > 0) {
		myXAxis = [
			{
				show: true,
				type: 'category',
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: ['transparent'],
						width: 1
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},

				data: xaxis_data_echarts_bar
			},
			// 对比的值坐标轴
			{
				show: true,
				// gridIndex: 1,
				type: 'category',
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: ['transparent'],
						width: 1
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},

				data: xaxis_data_echarts_bar_constract
			}
		];
	} else {
		myXAxis = [
			{
				show: true,
				type: 'category',
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: ['transparent'],
						width: 1
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},

				data: xaxis_data_echarts_bar
			}
		];
	}

	let option = {
		backgroundColor: 'transparent',
		// color: colorList,
		legend: {
			show: true,
			type: 'scroll',
			center: true,
			top: 0,
			width: 920,
			data: legend_data,
			itemWidth: 12,
			itemHeight: 12,
			itemGap: 80, //图例之间的距离
			align: 'auto',
			textStyle: {
				color: '#092649'
				// fontSize: 14
			}
		},
		dataZoom: [
			{
				start: 0,
				end: 100,
				bottom: 20,
				height: 30
			}
			// {
			//   start: 0,
			//   end: 100
			// }
		],
		tooltip: {
			trigger: 'axis',
			formatter: function (params) {
				let html = '';
				params.forEach((v) => {
					//console.log(v.color);
					let time = xaxis_data_echarts_bar[v.dataIndex];
					if (v.seriesName.includes('对比')) {
						time = xaxis_data_echarts_bar_constract[v.dataIndex];
					}
					//console.log(time, 'timetimetime');

					let value = v.value;
					// if (value != 0 && !value) {
					//   value = '-'
					// }
					if (value != 0 && !value) {
						value = '-';
					} else {
						value = Number(parseFloat(v.value).toFixed(2));
					}
					html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${v.color};"></span>
                    ${v.seriesName} : ${time} <span style="color:${v.color};font-weight:600;font-size: 14px"> ${value} </span>   ${unit_echarts_bar}
                    `;
				});
				return html;
			},
			extraCssText:
				'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;'
		},
		grid: {
			top: 50,
			containLabel: true, //区域是否包含坐标轴标签
			left: 40,
			right: 0,
			bottom: 60
		},
		xAxis: myXAxis,
		yAxis: [
			{
				type: 'value',
				name: unit_echarts_bar,
				offset: -2, //y轴坐标刻度偏移位置
				nameTextStyle: {
					color: '#949aa6',
					fontSize: 14,
					lineHeight: 40,
					fontWeight: 400,
					align: 'left',
					fontFamily: 'shsr',
					padding: [0, 0, 0, -40],
					width: 100
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC',
						width: 1
					}
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: 'transparent'
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},
				axisTick: {
					show: false
				}
			}
		],
		series: series_list
	};

	return option;
};
// 处理bar的图表的echarts option的值-----   单x轴
export const handleBarMapOptionSingle = (
	xaxis_data_echarts_bar,
	xaxis_data_echarts_bar_constract,
	yaxis_data_echarts_bar,
	yaxis_data_echarts_bar_constract,
	unit_echarts_bar,
	color,
	contract_color
) => {
	// xaxis_data_echarts_bar,  x轴
	// xaxis_data_echarts_bar_constract, 对比的x轴
	// yaxis_data_echarts_bar, y轴数据
	// yaxis_data_echarts_bar_constract,  对比y轴数据
	// unit_echarts_bar  单位
	let legend_data = [];
	let series_list = [];
	let color_1 = null;
	let color_2 = null;
	let contract_color_current = '#cadbee';

	for (let j = 0; j < yaxis_data_echarts_bar.length; j++) {
		if (color) {
			color_1 = hexToRgba(color, 1);
			color_2 = hexToRgba(color, 0.6);
		} else {
			color_1 = hexToRgba(colorList[j], 1);
			color_2 = hexToRgba(colorList[j], 0.6);
		}
		if (contract_color) {
			color_2 = contract_color;
		} else {
			color_2 = contract_color_current;
		}
		legend_data.push(yaxis_data_echarts_bar[j].name);
		legend_data.push(yaxis_data_echarts_bar_constract[j].name);
		series_list.push(
			{
				name: yaxis_data_echarts_bar[j].name,
				data: JSON.parse(JSON.stringify(yaxis_data_echarts_bar[j].value_list)),
				type: 'bar',
				itemStyle: {
					color: color_1
				},
				barWidth: 18,
				emphasis: {
					disabled: true,
					focus: 'none'
				}
				// xAxisIndex: 0,
			},
			{
				name: yaxis_data_echarts_bar_constract[j].name,
				data: JSON.parse(JSON.stringify(yaxis_data_echarts_bar_constract[j].value_list)),
				type: 'bar',
				itemStyle: {
					color: color_2
				},
				barWidth: 18,
				emphasis: {
					disabled: true,
					focus: 'none'
				}
				// xAxisIndex: 1,
			}
		);
	}

	let option = {
		backgroundColor: 'transparent',
		// color: colorList,
		legend: {
			show: true,
			type: 'scroll',
			center: true,
			top: 0,
			width: 920,
			data: legend_data,
			itemWidth: 24,
			itemGap: 80, //图例之间的距离
			align: 'auto',
			textStyle: {
				color: '#092649'
			}
		},
		dataZoom: [
			{
				start: 0,
				end: 100,
				bottom: 20,
				height: 30
			}
			// {
			//   start: 0,
			//   end: 100
			// }
		],
		tooltip: {
			trigger: 'axis',
			formatter: function (params) {
				let html = '';
				params.forEach((v) => {
					let value = v.value;
					// if (value != 0 && !value) {
					//   value = '-'
					// }
					if (value != 0 && !value) {
						value = '-';
					} else {
						value = Number(parseFloat(v.value).toFixed(2));
					}
					html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${v.color};"></span>
                    ${v.seriesName} : ${v.name} <span style="color:${v.color};font-weight:600;font-size: 14px"> ${value} </span>   ${unit_echarts_bar}
                    `;
				});
				return html;
			},
			extraCssText:
				'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;'
		},
		grid: {
			top: 50,
			containLabel: true, //区域是否包含坐标轴标签
			left: 40,
			right: 0,
			bottom: 60
		},
		xAxis: [
			{
				show: true,
				type: 'category',
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: ['transparent'],
						width: 1
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},

				data: xaxis_data_echarts_bar
			},
			// 对比的值坐标轴
			{
				show: true,
				// gridIndex: 1,
				type: 'category',
				axisTick: {
					show: false
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC'
					}
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: ['transparent'],
						width: 1
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},

				data: xaxis_data_echarts_bar_constract
			}
		],
		yAxis: [
			{
				type: 'value',
				name: unit_echarts_bar,
				offset: -2, //y轴坐标刻度偏移位置
				nameTextStyle: {
					color: '#949aa6',
					fontSize: 14,
					lineHeight: 40,
					fontWeight: 400,
					align: 'left',
					fontFamily: 'shsr',
					padding: [0, 0, 0, -40],
					width: 100
				},
				splitLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: '#DFE3EC',
						width: 1
					}
				},
				axisLine: {
					show: true,
					lineStyle: {
						type: 'dashed',
						color: 'transparent'
					}
				},
				axisLabel: {
					inside: false,
					color: '#949aa6',
					fontWeight: '100',
					fontSize: '14',
					fontFamily: 'shsr',
					lineHeight: 14,
					margin: 10
				},
				axisTick: {
					show: false
				}
			}
		],
		series: series_list
	};

	return option;
};

// 对坐标轴系的最大值进行处理 美观显示
export const handleMax = (value) => {
	// 取第一个数值
	let before = String(value).split('.')[0];
	let length = String(before).length;
	let length_result = Math.pow(10, length - 1);
	let first_string = String(value).slice(0, 1);
	let number_first = Number(first_string) + 1;
	return Number(number_first) * length_result;
};

export const handleEchartsLineBg = (data, xaxis_data) => {
	// 处理背景色的数据
	if (data.length == 0) {
		let markArea = {};
		return markArea;
	}
	// 当前阶段最大的时间
	let current_max_time = xaxis_data[xaxis_data.length - 1];
	//console.log(current_max_time, 'current_max_time');

	// let all_data = data[0].detailList;
	// //console.log(all_data, 'all_data111111111111111');
	// //console.log(xaxis_data, 'xaxis_data222222222222222');
	// let result_data = []
	// 循环x轴的所有时间段获取所有的是时间值
	let all_x = [];
	xaxis_data.forEach((item) => {
		if (item.length > 5) {
			let result = item.split(' ')[0];
			all_x.push(result);
		}
	});

	all_x = [...new Set(all_x)];
	//console.log(all_x, 'all_x3333333333');

	// 创建一个对象来存储按月份分类的日期
	const groupedByMonth = {};

	all_x.forEach((date) => {
		// 提取月份部分（假设日期格式为MM-DD）
		const month = date.split('-')[0];
		// 如果该月份还没有对应的数组，则创建一个
		if (!groupedByMonth[month]) {
			groupedByMonth[month] = [];
		}
		// 将日期添加到对应月份的数组中
		groupedByMonth[month].push(date);
	});
	const diffmonth = Object.values(groupedByMonth);

	let result = [];
	for (let k = 0; k < data.length; k++) {
		let all_data = data[k].detailList;
		for (let i = 0; i < all_data.length; i++) {
			let item_i = all_data[i];
			// let current_arr = []

			for (let j = 0; j < item_i.timeRangeList.length; j++) {
				let item_j = item_i.timeRangeList[j];
				let color_jiangu = null;
				color_jiangu = jian_gu[item_i.rateType - 1].color_1;

				if (all_x.length > 0) {
					// 带有年月日的数据
					for (let j = 0; j < diffmonth[k].length; j++) {
						let temp_data = [];
						const element = diffmonth[k][j];
						let time_end = element + ' ' + item_j.endTime;
						let time_start = element + ' ' + item_j.startTime;
						if (item_j.endTime == '23:59') {
							time_end = dayjs(time_end).add(1, 'day').startOf('day').format('MM-DD HH:mm');
						}
						// 尖峰平谷的结束时间和当前最大时间对比
						if (dayjs(current_max_time).isBefore(dayjs(time_end))) {
							// 尖峰平谷的时间超过了最大的时间范围 则使用最大的时间范围
							time_end = current_max_time;
						}

						// start
						temp_data.push({
							xAxis: time_start
						});
						// jieshu
						temp_data.push({
							xAxis: time_end,
							itemStyle: { color: color_jiangu },
							// name: item_i.rateTypeDesc,
							name: ''
						});

						if (temp_data.length > 0) {
							result.push(temp_data);
						}
					}
				}
			}
		}
	}
	//console.log(result, 'result');
	let markArea = {
		tooltip: { show: false },
		silent: true,
		label: { position: 'insideLeft' },
		data: result
	};
	return markArea;
};

export const menuTabChange = (first_tab_index, second_tab_index) => {
	// 记录跳转的菜单列表

	let current_menu_localstorage = localStorage.getItem('current_menu_localstorage');
	if (current_menu_localstorage) {
		current_menu_localstorage = JSON.parse(current_menu_localstorage);
	} else {
		current_menu_localstorage = {
			all_menu: [],
			current_menu: [],
			current_skip_url: '',
			skip_url_list: []
		};
	}

	//菜单状态和菜单跳转
	// 全部清除状态
	let current_menu_list = current_menu_localstorage.all_menu;
	current_menu_list.forEach((item) => {
		item.active = 0;
		if (item.menuList && item.menuList.length > 0) {
			item.menuList.forEach((item1) => {
				item1.active = 0;
			});
		}
	});
	let current_skip_url = '';
	let skip_url_list = current_menu_localstorage.skip_url_list;

	// 一级菜单选中
	current_menu_list[first_tab_index].active = 1;
	// 判断二级菜单
	if (second_tab_index) {
		// 二级菜单切换
		current_menu_list[first_tab_index].menuList[second_tab_index].active = 1;
		current_skip_url = current_menu_list[first_tab_index].menuList[second_tab_index].menuUrl;
	} else {
		// 只含一级菜单切换
		if (
			Object.hasOwn(current_menu_list[first_tab_index], 'menuList') &&
			current_menu_list[first_tab_index].menuList.length > 0
		) {
			// 存在子菜单则选择子菜单的第一个值
			current_menu_list[first_tab_index].menuList[0].active = 1;
			current_skip_url = current_menu_list[first_tab_index].menuList[0].menuUrl;
		} else {
			// 不存在子菜单则选择一级菜单
			current_skip_url = current_menu_list[first_tab_index].menuUrl;
		}
	}
	let current_menu = {
		first: first_tab_index,
		second: second_tab_index
	};
	if (current_skip_url) {
		skip_url_list.push(current_skip_url);
	}
	current_menu_localstorage = {
		all_menu: current_menu_list,
		current_menu,
		current_skip_url,
		skip_url_list
	};
	return current_menu_localstorage;
};

export const moneyNumberHandle = (num) => {
	// 确保输入是数字
	if (typeof num !== 'number') {
		return num;
	}
	if (isNaN(num)) {
		return '-';
	}
	num = parseFloat(num).toFixed(2);

	// 将数字转换为字符串
	let numStr = num.toString();

	// 使用正则表达式在千分位添加逗号
	// 匹配从字符串末尾开始的每三个字符（不包括小数点后的部分）
	// 并用逗号替换它们之间的位置
	let numWithComma = numStr.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

	// 如果数字是小数,并且没有手动添加小数点,则需要添加一个
	// 这里假设原始数字已经是正确的格式（即没有额外的零或小数点后的多余字符）
	if (numStr.includes('.') && !numWithComma.includes('.')) {
		let decimalIndex = numStr.indexOf('.');
		numWithComma = numWithComma.slice(0, decimalIndex) + '.' + numWithComma.slice(decimalIndex);
	}

	return numWithComma;
};

export const timeRange = (start, end, time_type) => {
	// 天的时间限制在30 month 12  year 5
	switch (time_type) {
		case 'date':
			if (end.diff(start, 'days') >= 30) {
				return {
					status: false,
					message: '最长可选的时间范围为30天,请修改!'
				};
			} else {
				return {
					status: true
				};
			}
		case 'month':
			if (end.diff(start, 'months') >= 12) {
				return {
					status: false,
					message: '最长可选的时间范围为12月,请修改!'
				};
			} else {
				return {
					status: true
				};
			}
		case 'year':
			if (end.diff(start, 'years') >= 5) {
				return {
					status: false,
					message: '最长可选的时间范围为5年,请修改!'
				};
			} else {
				return {
					status: true
				};
			}
		default:
			if (end.diff(start, 'days') >= 30) {
				return {
					status: false,
					message: '最长可选的时间范围为30天,请修改!'
				};
			} else {
				return {
					status: true
				};
			}
	}
};
