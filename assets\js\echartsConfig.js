// map  ehcarts  的默认的配置颜色的数组
// 1  #1181FD  rgba(17, 129, 253, 1)
// 2  #03E3A1  rgba(3, 227, 161, 1)
// 3  #7C8DFF  rgba(124, 141, 255, 1)
// 4  #2DCDFC  rgba(45, 205, 252, 1)
//  为避免出现 数据的条目数多于颜色条目的报错  目前进行颜色重复赋值 
export const colorList = [
    "#1181FD",
    "#03E3A1",
    "#7C8DFF",
    "#2DCDFC",
    // 增加重复的
    "#1181FD",
    "#03E3A1",
    "#7C8DFF",
    "#2DCDFC",
];



export const colorList_mix = [
    "#1181FD",
    "rgba(17, 129, 253, 0.6)",
    "#03E3A1",
    "rgba(3, 227, 161, 0.6)",
    "#7C8DFF",
    "rgba(124, 141, 255, 0.6)",
    "#2DCDFC",
    "rgba(45, 205, 252, 0.6)",
    // 增加重复的
    "#1181FD",
    "rgba(17, 129, 253, 0.6)",
    "#03E3A1",
    "rgba(3, 227, 161, 0.6)",
    "#7C8DFF",
    "rgba(124, 141, 255, 0.6)",
    "#2DCDFC",
    "rgba(45, 205, 252, 0.6)",
]


// 尖峰平谷的配色
// 尖   #FF6161  rgba(255, 97, 97, 1) 
// 峰   #FD9419  rgba(253, 148, 25, 1)  
// 平   #1181FD  rgba(17, 129, 253, 1)
// 谷   #03E3A1  rgba(3, 227, 161, 1)
// 深   #99B3CF  rgba(153, 179, 207, 1)
export const jian_gu = [
    {
        line_type: '1',
        color: "rgba(255, 97, 97, 1)",
        color_1: "rgba(255, 97, 97, 0.1)"
    },
    {
        line_type: '2',
        color: "rgba(253, 148, 25, 1)",
        color_1: "rgba(253, 148, 25, 0.1)"
    }, {
        line_type: '3',
        color: "rgba(17, 129, 253, 1)",
        color_1: "rgba(17, 129, 253, 0.1)"
    }, {
        line_type: '4',
        color: "rgba(3, 227, 161, 1)",
        color_1: "rgba(3, 227, 161, 0.1)"
    }, {
        line_type: '5',
        color: "rgba(153, 179, 207, 1)",
        color_1: "rgba(153, 179, 207, 0.1)"
    }
]



// 把hex颜色转换为rgba颜色的方式
export const hexToRgba = (hex, opacity) => {
    let rgbaColor = "";
    let reg = /^#[\da-f]{6}$/i;
    if (reg.test(hex)) {
        rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
            "0x" + hex.slice(3, 5)
        )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
    }
    return rgbaColor;
}

