//  项目户号
export const projectNoValidate = (params) => {
	const regex = /^[A-Za-z0-9]{0,64}$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
};

//  项目地址
export const projectLocationValidate = (params) => {
	const regex = /^[\u4e00-\u9fa5A-Za-z0-9]{0,100}$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
};
// 控制器名称
export const controlDeviceName = (params) => {
	const regex = /^[\u4e00-\u9fa5A-Za-z0-9!@#$%^&*()-_+=\`~\[\]\{\}\|;:'",.<>/?]{0,64}$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
};

// 平台设备编码
export const controlDeviceCode = (params) => {
	const regex = /^[\u4e00-\u9fa5A-Za-z0-9-_]{0,64}$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
};

// 控制器名id
export const controlDeviceId = (params) => {
	const regex = /^[A-Za-z0-9-_]{0,64}$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
};

// 平台资产ID
export const platId = (params) => {
	const regex = /^[A-Za-z0-9-_]{0,64}$/;
	return regex.test(params);
};
// 纬度验证
export const latTest = (lat) => {
	const latitudeRegex = /^(-?90(\.0{1,6})?|-?8\d(\.\d{1,6})?|-?\d{1,2}(\.\d{1,6})?)$/;
	return new Promise((resolve, reject) => {
		// 校验纬度

		if (lat && latitudeRegex.test(lat) && (lat > -90.0 || lat < 90.0)) {
			resolve();
		} else {
			reject();
		}
	});
};
// 经度验证
export const longTest = (lng) => {
	const longitudeRegex = /^(-?1[0-7]\d(\.\d{1,6})?|-?\d{1,2}(\.\d{1,6})?|180\.0{1,6})$/;
	return new Promise((resolve, reject) => {
		// 校验经度
		if (lng && longitudeRegex.test(lng) && (lng > -180.0 || lng < 180.0)) {
			resolve();
		} else {
			reject();
		}
	});
};

// 国网关口表
export const countryTableDevice = (params) => {
	const regex = /^[A-Za-z0-9]{0,64}$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
};

// 元素关联设备名称校验 form表单
export const elementConnectDeviceNameForm = (params) => {
	const regex = /^[\u4e00-\u9fa5A-Za-z0-9]{0,16}$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
};
// 元素关联设备名称校验
export const elementConnectDeviceName = (params) => {
	const regex = /^[\u4e00-\u9fa5A-Za-z0-9_]{1,64}$/;
	if (regex.test(params)) {
		return true;
	} else {
		return false;
	}
};

// 元素关联设备名称校验 form表单
export const elementConnectDeviceCodeForm = (params) => {
	const regex = /^[A-Za-z0-9-_]{0,64}$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
};
// 元素关联设备名称校验
export const elementConnectDeviceCode = (params) => {
	const regex = /^[A-Za-z0-9-_]{0,64}$/;
	if (regex.test(params)) {
		return true;
	} else {
		return false;
	}
};

// /^\d+(\.\d+)?$/; 小数

// 判断是否为数字
export const isNumberForm = (params) => {
	const regex = /^\d+(\.\d{0,2})?$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
};
export const isNumberFormPositive = (params) => {
	const regex = /^\d+?$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
};
export const isNumber = (params) => {
	const regex = /^\d+$/;
	if (regex.test(params)) {
		return true;
	} else {
		return false;
	}
};
export const isNumberPositive = (params) => {
	const regex = /^\d+$/;
	if (regex.test(params)) {
		return true;
	} else {
		return false;
	}
};

// 电价配置 小数点后支持六位

export const elePriceForm = (params) => {
	const regex = /^\d+(\.\d{0,6})?$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
};

// 月费最大需量 小数点后支持两位
export const eleDemandMaxForm = (params) => {
	const regex = /^\d+(\.\d{0,2})?$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
};

// 至少一位中英文数字
export const chEnNum = (params) => {
	if (params == '') {
		return Promise.resolve();
	} else {
		const regex = /^[\u4e00-\u9fa5A-Za-z0-9]{1,}$/;
		return new Promise((resolve, reject) => {
			if (regex.test(params)) {
				resolve();
			} else {
				reject();
			}
		});
	}
};

// 整数
export const positiveInteger = (params) => {
	const regex = /^[0-9]{1,}$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
};

// 规则名称
export const ruleName = (params) => {
    if (params == '') {
		return Promise.resolve();
	} else {
	const regex = /^[\u4e00-\u9fa5A-Za-z0-9]{1,64}$/;
	return new Promise((resolve, reject) => {
		if (regex.test(params)) {
			resolve();
		} else {
			reject();
		}
	});
}
};

// 电价配置 小数点后支持六位

export const NumberFloat2 = (params) => {
	const regex = /^\d+(\.\d{0,2})?$/;
	if (regex.test(params)) {
		return true;
	} else {
		return false;
	}
};
