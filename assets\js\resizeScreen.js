// 实现页面的 125% 和 100% 的自适应的代码
// 默认的宽度和高度
// 1920 945    100%屏幕情况下的尺寸比例
let body_dom = document.getElementsByTagName('body')[0];
body_dom.style.overflow = 'hidden';
// body_dom.setAttribute('style', 'overflow:hidden;');
let containerDom = document.getElementById('app');
containerDom.style.height = '945px';
containerDom.style.width = '1920px';
// body_dom.style.height = "945px"
// body_dom.style.width = "1920px"
// containerDom.setAttribute('style', 'width: 1920px; height: 945px;');
const getScale = () => {
	const [width, height] = [1920, 945];
	let widthScale = window.innerWidth / width;
	let heightScale = window.innerHeight / height;
	return [widthScale, heightScale];
};

// 处理所有Ant Design下拉框的缩放
const applyDropdownScale = () => {
	const [widthScale, heightScale] = getScale();
	const dropdowns = document.querySelectorAll('.ant-select-dropdown');
	console.log('找到下拉框数量:', dropdowns.length, '缩放比例:', widthScale, heightScale);
	dropdowns.forEach((dropdown, index) => {
		// 使用更强制的方法
		dropdown.style.setProperty('transform', `scale(${widthScale}, ${heightScale})`, 'important');
		dropdown.style.setProperty('transform-origin', 'left top', 'important');
		dropdown.style.setProperty('z-index', '9999', 'important');

		// 同时处理下拉框内的所有元素
		const items = dropdown.querySelectorAll('.ant-select-item');
		items.forEach((item) => {
			item.style.setProperty('font-size', `${16 * widthScale}px`, 'important');
			item.style.setProperty('line-height', `${32 * heightScale}px`, 'important');
		});

		console.log(`下拉框${index + 1}已应用缩放:`, dropdown.style.transform);
	});
};

const applyScale = () => {
	const [widthScale, heightScale] = getScale();
	containerDom.style.transform = `scale(${widthScale}, ${heightScale})`;
	containerDom.style.transformOrigin = 'left top';

	// 处理Ant Design下拉框的缩放
	applyDropdownScale();
};

// 监听DOM变化，确保新创建的下拉框也能被缩放
const observer = new MutationObserver((mutations) => {
	mutations.forEach((mutation) => {
		if (mutation.type === 'childList') {
			mutation.addedNodes.forEach((node) => {
				if (
					node.nodeType === 1 &&
					node.classList &&
					node.classList.contains('ant-select-dropdown')
				) {
					// 延迟一点时间确保下拉框完全渲染
					setTimeout(() => {
						applyDropdownScale();
					}, 10);
				}
			});
		}
	});
});

// 开始监听body下的DOM变化
observer.observe(document.body, {
	childList: true,
	subtree: true
});

// 初始化缩放
applyScale();

// 监听窗口大小变化
window.addEventListener('resize', applyScale);

// 定期检查下拉框（防止某些情况下MutationObserver没有捕获到）
setInterval(() => {
	applyDropdownScale();
}, 1000);

// 监听点击事件，当点击select时立即检查下拉框
document.addEventListener('click', (e) => {
	if (e.target.closest('.ant-select')) {
		setTimeout(() => {
			applyDropdownScale();
		}, 50);
	}
});
