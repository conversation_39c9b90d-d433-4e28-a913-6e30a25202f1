// 配置全局的主题色和主题样式
export const themeSelf = {
    token: {//默认配置
        colorPrimary: '#1081FD', //基础配色 
        colorPrimaryHover: "#1181FD",// 主色梯度下的悬浮态，使用频率很高
        colorPrimaryTextActive: "#1181FD",//
        colorTextPlaceholder: "#BBC1CD",// placeholder的显示颜色 
        colorTextBase: '#092649', //用于派生文本色梯度的基础变量
        colorTextSecondary: "#092649",//作为第二梯度的文本色，一般用在不那么需要强化文本颜色的场景，例如 Label 文本、Menu 的文本选中态等场景
        colorTextTertiary:"#092649",//  第三级文本色一般用于描述性文本，例如表单的中的补充说明文本、列表的描述性文本等场景。
        colorTextLabel:"#092649",  //"#092649"
        colorTextDescription:"#dde2ed", 
        colorTextDisabled: "#BBC1CD",//举例form无数据
        colorTextHeading: "#092649",
        colorTextLightSolid: "#FFFFFF",//  button 等里面的文字的颜色
        borderRadius: 5, //基础组件的圆角大小，例如按钮、输入框、卡片等
        colorError: '#ff4d4f', //失败按钮、错误状态提示（Result）组件
        colorSuccess: '#52c41a', // 用于表示操作成功的 Token 序列，如 Result、Progress 等组件会使用该组梯度变量。
        colorWarning: '#faad14', // 用于表示操作警告的 Token 序列，如 Notification、 Alert等警告类组件或 Input 输入类等组件会使用该组梯度变量。
        controlHeight: 36,//Ant Design 中按钮和输入框等基础控件的高度
        fontFamily: 'SourceHanSansCN-Regular',// 字体库
        fontSize: 14,// 设计系统中使用最广泛的字体大小，文本梯度也将基于该字号进行派生。
        lineType: 'solid',//用于控制组件边框、分割线等的样式，默认是实线
        lineWidth: 1,//用于控制组件边框、分割线等的宽度
        colorLink: "#1181FD",// 
        colorLinkHover: "#1181FD",
        colorFillQuaternary: "#F2F8FF", //最弱一级的填充色，适用于不易引起注意的色块，例如斑马纹、区分边界的色块等。
        colorFillSecondary: "#dde2ed",// 二级填充色可以较为明显地勾勒出元素形体，如 Rate、Skeleton 等。也可以作为三级填充色的 Hover 状态，如 Button 分页的按钮hover 等。
        colorFillTertiary:  "#F2F8FF", // input 等 disable的填充色
    },
    //   全局定向修改
    components: {
        Button: {
            colorPrimary: '#1081FD',
        },
    },

}


// 非全局，定点修改
export const second_menu_tabs = {
    colorText: "#9CA2AF",
}