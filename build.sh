#!/bin/sh

IMAGE_NAME="10.115.14.103:5000/iems/iems-web-nginx"
TAG="latest"

# 配置 Docker 登录信息
DOCKER_REGISTRY="http://10.115.14.103:5000"
DOCKER_USERNAME="admin"
DOCKER_PASSWORD="123456"

# Docker 登录
echo "Logging into Docker registry..."
sudo docker login -u "$DOCKER_USERNAME" -p "$DOCKER_PASSWORD" "$DOCKER_REGISTRY"
if [ $? -ne 0 ]; then
  echo "Docker login failed."
  exit 1
fi
echo "Docker login successful."


echo "start building ..."
sudo cp deploy/Dockerfile Dockerfile
sudo docker build -t $IMAGE_NAME":"$TAG .
echo "build" $IMAGE_NAME":"$TAG "success ..."
FILE_PATH="Dockerfile"
if [ -e "$FILE_PATH" ]; then
    sudo rm "$FILE_PATH"
    echo "File '$FILE_PATH' has been Clear."
else
    echo "Done."
fi

echo "start push image ..."

sudo docker push $IMAGE_NAME":"$TAG

echo "push image success ..."
