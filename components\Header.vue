<template>
	<div class="header_top">
		<div class="header_left">
			<div :class="['company_icon_name', hide_status ? 'hide_status' : '']">
				<div class="company_icon"></div>
				<!-- <img @click="skipStpt" class="company_icon" src="/assets/icon/gh_logo.png" alt=""> -->
				<!-- <div class="divied_line"></div>
        <div class="company_name">微网星</div> -->
			</div>

			<div :class="['back_home', hide_status ? 'hide_status' : '']">
				<div
					class="back"
					@click="backUrl"
				></div>
				<div
					class="home"
					@click="skipHome"
				></div>
			</div>

			<div
				:class="['menu_list', hide_status ? 'hide_status' : '']"
			>
				<div
					:class="['menu_item', item.active == 1 ? 'active' : '']"
					v-for="(item, index) in menu_list"
					:key="index"
					@click="tabChange(index, null)"
				>
					<div class="menu_item_contain">
						<div :class="['menu_icon', item.menuImage]"></div>
						<div class="menu_name">{{ item.menuName }}</div>
					</div>
					<div class="menu_second_bg">
						<div
							class="second_menu"
							v-if="item.menuList.length > 0 ? true : false"
						>
							<div
								:class="['second_menu_item', item1.active == 1 ? 'active' : '']"
								v-for="(item1, index1) in item.menuList"
								:key="index1"
								@click.stop="tabChange(index, index1)"
							>
								{{ item1.menuName }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- <DesktopOutlined
			style="font-size: 30px; margin-right: 20px"
			@click="enterBigScreen"
		/> -->
		<div style="margin-right: 20px">
			<a-tooltip placement="topLeft">
				<template #title>
					<span>{{ project_mess_title }}</span>
				</template>
				<a-select
					class="project_tree"
					v-model:value="project_mess_id"
					@change="changeProject"
				>
					<a-select-option
						v-for="project in project_list"
						:key="project.projectId"
						:value="project.projectId"
					>
						{{ project.projectName }}
					</a-select-option>
				</a-select>
			</a-tooltip>
			<!-- <img src="/assets/icon/table_arrow.png" alt="#" /> -->
		</div>
		<!-- <ProjectSelect
      v-if="showHideModalStatus"
      @showHideModal="showHideModal"
      :showHideModalStatus="showHideModalStatus"
    ></ProjectSelect> -->

		<div
			class="handle_bigScreen"
			@click="enterBigScreen"
		></div>

		<div :class="['handle_user', hide_status ? 'hide_status' : '']">
			<div
				:class="[hide_status ? 'handle_header' : 'handle_header_close']"
				@click="handleHeader"
			></div>
			<div
				class="user_mess"
				@click="showPersonlTab"
			></div>
		</div>

		<div
			class="outsideClick"
			@click="showPersonlTab"
			v-if="personlTab"
		></div>
		<div
			class="personl_tab"
			v-show="personlTab"
		>
			<div
				class="personal_tab_list"
				style="margin: 0 10px; cursor: pointer"
				@click="loginOutFn"
			>
				<span>退出登录</span>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { useRouter, useRoute } from 'vue-router';
	import { onMounted, ref, watch } from 'vue';
	// import ProjectSelect from "/components/ProjectSelect.vue";
	import { projectChangeMenu } from '/pages/login/tool.js';
	import { message } from 'ant-design-vue';
	import { DesktopOutlined } from '@ant-design/icons-vue';
	import { logout, logout1 } from '/pages/login/loginHttp.js';
	import { menuTabChange } from '/assets/js/commonTool.js';
	import { getAuthProjects, projectidGetMenuHttp } from '/components/commonApi.js';
	import { eventBus } from '/assets/js/eventBus'; // 导入事件总线实例
	const route = useRoute();
	const menu_list = ref([]);

	const router = useRouter();
	// 菜单的切换
	const tabChange = (first_tab_index, second_tab_index) => {
		console.log(
			first_tab_index,
			second_tab_index,
			'second_tab_indexsecond_tab_indexsecond_tab_index'
		);
		let result = menuTabChange(first_tab_index, second_tab_index);
		menu_list.value = result.all_menu;
		// localStorage.setItem('current_menu_localstorage', JSON.stringify(result));
		if (result.current_skip_url == null) {
			// window.history.go(0);
			window.location.href = '/#/404';
			// router.push('/404');
		} else {
			localStorage.setItem('current_menu_localstorage', JSON.stringify(result));
			window.location.href = result.current_skip_url;
			// window.history.go(0);
		}

		// // 记录跳转的菜单列表
		// let current_menu_localstorage = localStorage.getItem('current_menu_localstorage')
		// if (current_menu_localstorage) {
		//   current_menu_localstorage = JSON.parse(current_menu_localstorage)
		// } else {
		//   current_menu_localstorage = {
		//     all_menu: null,
		//     current_menu: null,
		//     current_skip_url: null,
		//     skip_url_list: []
		//   }
		// }

		//菜单状态和菜单跳转
		// let temp = JSON.parse(JSON.stringify(menu_list.value))
		// temp.forEach((item) => {
		//   item.active = 0
		//   if (item.menuList && item.menuList.length > 0) {
		//     item.menuList.forEach((item1) => {
		//       item1.active = 0
		//     })
		//   }
		// })
		// menu_list.value = temp
		// let current_skip_url = null

		// let skip_url_list = current_menu_localstorage.skip_url_list

		// menu_list.value[first_tab_index].active = 1
		// if (second_tab_index) {
		//   // 二级菜单切换
		//   menu_list.value[first_tab_index].menuList[second_tab_index].active = 1
		//   current_skip_url = menu_list.value[first_tab_index].menuList[second_tab_index].menuUrl
		// } else {
		//   // 一级菜单切换
		//   if (
		//     Object.hasOwn(menu_list.value[first_tab_index], 'menuList') &&
		//     menu_list.value[first_tab_index].menuList.length > 0
		//   ) {
		//     // 存在子菜单则选择子菜单的第一个值
		//     menu_list.value[first_tab_index].menuList[0].active = 1
		//     current_skip_url = menu_list.value[first_tab_index].menuList[0].menuUrl
		//   } else {
		//     // 不存在子菜单则选择一级菜单
		//     current_skip_url = menu_list.value[first_tab_index].menuUrl
		//   }
		// }
		// let current_menu = {
		//   first: first_tab_index,
		//   second: second_tab_index
		// }
		// current_menu.first = first_tab_index
		// current_menu.second = second_tab_index

		// if (current_skip_url) {
		//   skip_url_list.push(current_skip_url)
		//   current_menu_localstorage = {
		//     all_menu: temp,
		//     current_menu,
		//     current_skip_url,
		//     skip_url_list
		//   }
		//   localStorage.setItem('current_menu_localstorage', JSON.stringify(current_menu_localstorage))
		//   window.location.href = current_skip_url
		// }
	};

	// 处理顶部header
	const hide_status = ref(false);
	const handleHeader = () => {
		hide_status.value = !hide_status.value;
	};

	const project_mess_title = ref();
	const project_mess_id = ref();
	const getCurrentProjectTitle = () => {
		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			project_mess_title.value = project_mess.project_title;
			project_mess_id.value = project_mess.project_id;
		} else {
			message.error('未选择项目，请重新选择！');
		}
	};

	const project_list = ref([]);
	const getProjectList = async () => {
		project_list.value = await getAuthProjects();
	};

	// 切换项目
	const changeProject = (value) => {
		let project_id = value;
		let project_title = project_list.value.find(
			(project) => project.projectId === value
		)?.projectName;
		let collectionInterval = project_list.value.find(
			(project) => project.projectId === value
		)?.collectionInterval;
		let project_bigscreenUrl = project_list.value.find(
			(project) => project.projectId === value
		)?.bigscreenUrl;
		localStorage.setItem(
			'project_mess',
			JSON.stringify({
				project_id,
				project_title,
				project_bigscreenUrl,
				collectionInterval
			})
		);
		// 修改了项目选择 清空菜单的存储
		getMenuFn(project_id);
	};

	// 根据项目查询并获取菜单权限
	const getMenuFn = (params) => {
		projectidGetMenuHttp(params)
			.then((data) => {
				// projectChangeMenu(menu_list.value)
				let current_menu_localstorage = projectChangeMenu(data);
				// debugger;
				let menuList = current_menu_localstorage.all_menu;
				let oldMenuList = JSON.parse(localStorage.getItem('current_menu_localstorage'));
				let oldUrl = oldMenuList.current_skip_url;
				const result = findMenuUrl(menuList, oldUrl);
				if (result) {
					// menu_list.value = current_menu_localstorage.all_menu;
					localStorage.setItem(
						'current_menu_localstorage',
						JSON.stringify(current_menu_localstorage)
					);
					let res = menuTabChange(result.first, result.second);
					localStorage.setItem('current_menu_localstorage', JSON.stringify(res));
					window.history.go(0);
					window.location.href = res.current_skip_url;
				} else {
					// menu_list.value = current_menu_localstorage.all_menu;
					localStorage.setItem(
						'current_menu_localstorage',
						JSON.stringify(current_menu_localstorage)
					);
					window.history.go(0);
					window.location.href = current_menu_localstorage.current_skip_url;
				}
				// localStorage.setItem('current_menu_localstorage', JSON.stringify(current_menu_localstorage))
				// 跳转到选中的菜单
				// if (window.location.pathname == '/pages/homeIndex/') {
				//   // 首页切换刷新
				//   window.history.go(0)
				// } else {
				//   window.location.href = current_menu_localstorage.current_skip_url
				// }
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	// 获取当前菜单坐标
	const findMenuUrl = (data, targetUrl) => {
		for (let i = 0; i < data.length; i++) {
			const menuList = data[i].menuList;
			for (let j = 0; j < menuList.length; j++) {
				if (menuList[j].menuUrl === targetUrl) {
					return { first: i, second: j };
				}
			}
		}
		return null;
	};

	// 获取当前一级菜单坐标
	const findMenuFirst = (data, targetUrl) => {
		for (let i = 0; i < data.length; i++) {
			if (data[i].menuUrl) {
				if (targetUrl.includes(data[i].menuUrl)) {
					return { first: i, second: null };
				}
			} else {
				const menuList = data[i].menuList;
				for (let j = 0; j < menuList.length; j++) {
					if (targetUrl.includes(menuList[j].menuUrl)) {
						return { first: i, second: j };
					}
				}
			}
		}
		return null;
	};

	const current_project_tree = ref([]);
	const getCurrentProjectTree = () => {
		let project_tree = localStorage.getItem('project_tree');
		if (project_tree) {
			project_tree = JSON.parse(project_tree);
			current_project_tree.value = project_tree;
		} else {
			message.error('项目结构树不存在，请选择项目！');
		}
	};

	// 项目树结构的弹框的展示和隐藏
	// const showHideModalStatus = ref(false);
	// const showHideModal = () => {
	//   showHideModalStatus.value = !showHideModalStatus.value;
	// };

	// 跳转生态平台
	const skipStpt = () => {
		window.location.href = 'https://bi-proxy.eco.teems.com.cn/nsbb/gather/66a83a99845c72000885e560';
	};

	onMounted(() => {
		// window.addEventListener('popstate', () => {
		// 	const currentUrl = window.location.href;
		// 	console.log('当前地址:', currentUrl);
		// });
		eventBus.on('menu-change', (newValue) => {
			let result = menuTabChange(newValue[0], newValue[1]);
			menu_list.value = result.all_menu;
			localStorage.setItem('current_menu_localstorage', JSON.stringify(result));
			window.location.href = newValue[2];
		});

		// 获取项目列表
		getProjectList();
		// getCurrentProjectTree();
		// 获取项目的title
		getCurrentProjectTitle();

		// 获取菜单的选中状态显示
		let current_menu_localstorage = localStorage.getItem('current_menu_localstorage');

		current_menu_localstorage = JSON.parse(current_menu_localstorage);
		if (current_menu_localstorage) {
			// 存在菜单记录表示已经选择过    项目和菜单
			let temp = current_menu_localstorage.all_menu;
			let { first, second } = current_menu_localstorage.current_menu;
			temp.forEach((item) => {
				item.active = 0;
			});
			temp[first].active = 1;
			second ? (temp[first].menuList[second].active = 1) : null;

			menu_list.value = temp;
		} else {
			// 没有选择过项目和菜单  是否跳转登录页，后续根据单点改变地址
			// window.location.href = '/'
		}
	});

	// 退出
	const personlTab = ref(false);
	const showPersonlTab = () => {
		personlTab.value = !personlTab.value;
	};

	const loginOutFn = () => {
		showPersonlTab();

		let stpt = localStorage.getItem('stpt');
		message.error('登录状态已失效！');
		if (stpt && stpt == 1) {
			logout().then((data) => {
				window.location.href = data;
			});
		} else {
			logout1().then(() => {
				window.location.href = 'https://iems.teems.com.cn/#/';
			});
		}
	};

	// ---------------------------------------------------跳转首页---------------------------
	const skipHome = () => {
		// window.location.href = '/pages/homeIndex/#/'

		eventBus.emit('menu-change', [0, 0, '/pages/homeIndex/#/']); // 触发事件
	};

	// 返回上一个页面
	const backUrl = () => {
		window.history.go(-1);
		// 暂留还需要处理返回后的跳转状态

		// let current_menu_localstorage = localStorage.getItem('current_menu_localstorage')
		// let skip_url_list = []
		// if (current_menu_localstorage) {
		//   current_menu_localstorage = JSON.parse(current_menu_localstorage)

		//   skip_url_list = current_menu_localstorage.skip_url_list
		//   let skip_url = ''
		//   if (skip_url_list.length == 0) {
		//     return
		//   } else if (skip_url_list.length == 1) {
		//     skip_url = skip_url_list.pop()
		//   } else if (skip_url_list.length > 1) {
		//     // 最后一个为当前页面的url去除
		//     skip_url_list.pop()
		//     // 倒数第二个的url是需要跳转的，也需要去除
		//     skip_url = skip_url_list.pop()
		//   }
		//   current_menu_localstorage.skip_url_list = skip_url_list
		//   localStorage.setItem('current_menu_localstorage', JSON.stringify(current_menu_localstorage))
		//   window.location.href = skip_url
		// }
	};

	let project_mess = JSON.parse(localStorage.getItem('project_mess'));
	// console.log(project_mess.project_bigscreenUrl, 'project_bigscreenUrl');
	let bigscreenUrl = null;
	if (project_mess.project_bigscreenUrl) {
		bigscreenUrl = project_mess.project_bigscreenUrl;
		console.log(bigscreenUrl, 'bigscreenUrl');
	}

	const enterBigScreen = () => {
		if (!bigscreenUrl) {
			message.error('未配置大屏地址');
			return;
		}
		window.open(bigscreenUrl, '_blank');
	};


	// watch(
	// 	() => route,
	// 	(newRoute, oldRoute) => {
	// 		if (menu_list.value.length > 0) {
	// 			const { pathname, search, hash } = window.location;
	// 			const res = findMenuFirst(menu_list.value, pathname + search + hash);
	// 			console.log(menu_list.value, 'menu_list.value');
	// 			console.log(pathname + search + hash, 'pathname + search + hash');
	// 			console.log(res, 'res');
	// 			let result = menuTabChange(res.first, res.second);
	// 			menu_list.value = result.all_menu;
	// 			if (result.current_skip_url == null) {
	// 				window.location.href = '/#/404';
	// 			} else {
	// 				localStorage.setItem('current_menu_localstorage', JSON.stringify(result));
	// 				window.location.href = result.current_skip_url;
	// 			}
	// 			// menu_list.value.forEach((item) => {
	// 			// 	item.active = 0;
	// 			// });
	// 			// menu_list.value[result.first].active = 1;
	// 		}
	// 	},
	// 	{ deep: true, immediate: true }
	// );

	// watch(
	// 	() => route,
	// 	(newRoute, oldRoute) => {
	// 		if (menu_list.value.length > 0) {
	// 			const { pathname, search, hash } = window.location;
	// 			console.log(pathname + search + hash, 'window.location.href');

	// 			const result = findMenuFirst(menu_list.value, pathname + search + hash);
	// 			console.log(result, 'qqqqqqqqqqqqqqqqqqqqq');
	// 			if (result) {
	// 				// console.log(result);
	// 				// let res = menuTabChange(result.first, result.second);
	// 				// localStorage.setItem('current_menu_localstorage', JSON.stringify(res));
	// 				// console.log(res, 'redssss');
	// 				// window.location.href = res.current_skip_url;
	// 				let current_menu_localstorage = localStorage.getItem('current_menu_localstorage');
	// 				current_menu_localstorage = JSON.parse(current_menu_localstorage);
	// 				if (current_menu_localstorage) {
	// 					let temp = current_menu_localstorage.all_menu;
	// 					temp.forEach((item) => {
	// 						item.active = 0;
	// 					});
	// 					temp[result.first].active = 1;

	// 					menu_list.value = temp;
	// 					console.log(123456778);
	// 					// location.reload(true);
	// 				}
	// 				console.log(menu_list.value, 'menu_list.value菜单');
	// 			}
	// 		}
	// 		// window.history.go(0);
	// 	},
	// 	{ deep: true, immediate: true }
	// );
</script>

<style lang="less" scoped>
	.header_top {
		height: 65px;
		width: 100%;
		background-color: #ffffff;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		// padding: 0 31px;
		padding: 0 28px;
		position: relative;
		box-sizing: border-box;

		.header_left {
			flex: 1;
			height: 100%;
			// display: flex;
			position: relative;
		}

		.handle_bigScreen {
			cursor: pointer;
			margin-right: 24px;
			height: 18px;
			width: 18px;
			background-image: url('/assets/icon/bigScreen.png');
			background-repeat: no-repeat;
			background-size: 100%;
		}

		.handle_user {
			width: calc(1.25vw + 55px);
			height: 100%;
			display: flex;
			flex-direction: row;
			align-items: center;
			background-color: #ffffff;

			.handle_header {
				cursor: pointer;
				height: 18px;
				width: 18px;
				background-image: url('/assets/icon/close_header.png');
				background-repeat: no-repeat;
				background-size: 100%;
			}

			.handle_header_close {
				cursor: pointer;
				height: 18px;
				width: 18px;
				background-image: url('/assets/icon/close_map.png');
				background-repeat: no-repeat;
				background-size: 100%;
			}

			.user_mess {
				height: 37px;
				width: 37px;
				// margin-left: 24px;
				margin-left: 1.25vw;
				background-image: url('/assets/icon/default_user.png');
				background-repeat: no-repeat;
				background-size: 100%;
			}
		}

		// .handle_user.hide_status {
		//   justify-content: end;
		//   .user_mess {
		//     display: none;
		//   }
		// }
	}

	.company_icon {
		height: 34px;
		width: 99px;
		// aspect-ratio: 149/50;
		display: inline-block;
		position: relative;
		top: 0px;
		cursor: pointer;
		background-image: url('/assets/icon/gh_logo.png');
		background-repeat: no-repeat;
		background-size: 100%;
	}

	.company_name {
		width: 126px;
		height: 18px;
		line-height: 18px;
		font-family: SourceHanSansCN-Regular;
		font-weight: 400;
		font-size: 18px;
		color: #092649;
	}

	.company_icon_name {
		position: absolute;
		top: 15px;
		left: 0;
		// width: 274px;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.company_icon_name.hide_status {
		display: none;
	}

	.divied_line {
		height: 17px;
		width: 1px;
		background-color: #5f6368;
		opacity: 0.2;
		margin: 0 10px;
	}

	.back_home {
		position: absolute;
		top: 9px;
		left: 200px;
		width: 108px;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		// margin-left: 46px;
		// margin-left: 2.4vw;
		transition: all 1s ease-in-out;

		.back,
		.home {
			height: 47px;
			width: 46px;
			cursor: pointer;
			position: relative;
			top: 4px;
		}

		.back {
			background-image: url('/assets/icon/back_normal.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;

			&:hover {
				background-image: url('/assets/icon/back_hover.png');
			}
		}

		.home {
			background-image: url('/assets/icon/home_normal.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;

			&:hover {
				background-image: url('/assets/icon/home_hover.png');
			}
		}
	}

	.back_home.hide_status {
		// margin-left: 0vw;
		left: 0;
	}

	.menu_list {
		position: absolute;
		top: 11px;
		left: 320px;
		display: flex;
		flex-direction: row;
		align-items: center;
		margin-left: 1vw;

		.menu_item {
			// padding: 0 17px;
			position: relative;
			cursor: pointer;
			margin-left: 20px;

			.menu_item_contain {
				position: relative;
				padding: 0 0.9vw;
				display: flex;
				flex-direction: row;
				align-items: center;
				height: 44px;
				// height: 4.6vh;
			}

			.menu_name {
				margin-left: 10px;
				font-family: SourceHanSansCN-Regular;
				font-weight: 400;
				font-size: 16px;
				color: #092649;
				line-height: 50px;
				height: 18px;
				line-height: 18px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.menu_icon {
				height: 18px;
				width: 18px;
				background-repeat: no-repeat;
				background-size: 100% 100%;
			}

			.menu_icon_home {
				background-image: url('/assets/icon/home.png');
			}

			.menu_icon_watch {
				background-image: url('/assets/icon/watch.png');
			}

			.menu_icon_income {
				background-image: url('/assets/icon/income.png');
			}

			.menu_icon_algorithm {
				background-image: url('/assets/icon/algorithm.png');
			}

			.menu_icon_report {
				background-image: url('/assets/icon/report.png');
			}

			.menu_icon_set {
				background-image: url('/assets/icon/set.png');
			}

			.menu_second_bg {
				display: none;
				position: absolute;
				top: 100%;
				left: 50%;
				z-index: 999;
				font-size: 16px;
				transform: translate(-50%, 0%);
				padding-top: 15px;
			}

			.second_menu {
				//height: 100px;
				width: 146px;
				background: #ffffff;
				box-shadow: 0px 6px 21px 0px rgba(12, 44, 109, 0.27);
				border-radius: 8px;
				padding: 8px 0;

				.second_menu_item {
					width: 100%;
					height: 36px;
					background-color: #ffffff;
					text-align: left;
					line-height: 36px;
					padding: 0 16px;
					box-sizing: border-box;

					&:hover {
						background: #edf4fc;
					}
				}

				.second_menu_item.active {
					background: #edf4fc;
				}
			}

			&:hover {
				background-color: #edf4fc;
				border-radius: 30px;

				.menu_second_bg {
					display: block;
				}
			}
		}

		.menu_item.active {
			background-color: #1181fd;
			border-radius: 30px;

			.menu_name {
				color: #ffffff;
			}

			.menu_icon_home {
				background-image: url('/assets/icon/home_active.png');
			}

			.menu_icon_watch {
				background-image: url('/assets/icon/watch_active.png');
			}

			.menu_icon_income {
				background-image: url('/assets/icon/income_active.png');
			}

			.menu_icon_algorithm {
				background-image: url('/assets/icon/algorithm_active.png');
			}

			.menu_icon_report {
				background-image: url('/assets/icon/report_active.png');
			}

			.menu_icon_set {
				background-image: url('/assets/icon/set_active.png');
			}
		}
	}

	.menu_list.hide_status {
		display: none;
	}

	// .project_tree {
	//   height: 36px !important;
	//   width: 170px !important;
	//   // box-sizing: border-box !important;
	//   // border: 1px solid #dde2ed !important;
	//   border-radius: 30px !important;
	//   margin-right: 20px !important;
	//   line-height: 36px !important;

	// }
	:deep(.ant-select-selector) {
		width: 170px !important;
		height: 36px !important;
		box-sizing: border-box !important;
		border: 1px solid #dde2ed !important;
		border-radius: 30px;
		line-height: 36px !important;
	}

	// .project_tree.show_modal {
	//   img {
	//     position: absolute;
	//     right: 12px;
	//     top: 12px;
	//     transform: rotateZ(-90deg);
	//     width: 7px;
	//     display: block;
	//     height: 12px;
	//   }
	// }

	.outsideClick {
		height: 100%;
		width: 100%;
		position: fixed;
		top: 0;
		right: 0;
		// background-color: transparent;
		z-index: 8;
	}

	.personl_tab {
		position: absolute;
		// bottom: -180px;
		top: 58px;
		right: 0px;
		background-color: #ffffff;
		width: 198px;
		// height: 174px;
		border-radius: 5px;
		z-index: 999;
		padding-top: 16px;
		padding-bottom: 6px;
		box-shadow: 1px 1px 20px rgba(12, 44, 109, 0.27);

		.personal_tab_list {
			cursor: pointer;
			margin: 0 !important;
			padding: 0 23px !important;
			height: 36px;
			text-align: center;
			width: 100%;
			position: relative;

			&:hover {
				background-color: #f2f8ff;
			}

			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;
		}
	}
</style>
<style lang="less"></style>
