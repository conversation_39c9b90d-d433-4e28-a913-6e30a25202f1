<template>
	<div class="third_menu_contain">
		<slot name="contact"></slot>
		<div
			:class="['third_menu_item', currentThirdMenu == item.route_name ? 'active' : '']"
			@click="menuChange(item)"
			v-for="(item, index) in props.third_menu"
			:key="index"
		>
			<span class="third_menu_name">{{ item.name }}</span>
			<span class="third_menu_line"></span>
		</div>
	</div>
</template>

<script setup>
	import { onMounted, ref, watch } from 'vue';
	import { useRouter, useRoute } from 'vue-router';

	const props = defineProps({
		third_menu: {
			type: Object,
			default: null
		}
	});

	const router = useRouter();
	// 菜单切换
	const currentThirdMenu = ref(props.third_menu[0].route_name);
	const menuChange = (item) => {
		currentThirdMenu.value = item.route_name;
		router.push(item.url);
	};
	onMounted(() => {
		let route = useRoute();
		currentThirdMenu.value = route.name;
	});

	const route = useRoute();

	watch(
		() => route,
		(newRoute, oldRoute) => {
			console.log('Route changed:', newRoute.fullPath);
			// if (newRoute.fullPath == '/analysis/analysis_all') {
				currentThirdMenu.value = newRoute.name;
			// }
		},
		{ deep: true, immediate: true }
	);
</script>
<style lang="less" scoped>
	.third_menu_contain {
		height: 74px;
		// height: 6.9vh;
		background: #ffffff;
		border-radius: 8px;
		box-sizing: border-box;
		margin: 0px 32px;
		padding: 0 32px;
		display: flex;
		position: relative;
		.third_menu_item {
			height: 100%;
			// line-height: 80px;
			// line-height: 6.9vh;
			line-height: 74px;
			margin-right: 25px;
			position: relative;
			cursor: pointer;
			.third_menu_name {
				font-family: SourceHanSansCN-Regular;

				font-weight: 400;
				font-size: 18px;
				color: #9ca2af;
			}
			.third_menu_line {
				height: 3px;
				background: #1181fd;
				border-radius: 2px;
				width: 100%;
				display: none;
				position: absolute;
				bottom: 0;
			}
		}
		.third_menu_item.active {
			.third_menu_name {
				font-family: SourceHanSansCN-Regular;
				font-weight: bold;
				color: #1181fd;
			}
			.third_menu_line {
				display: block;
			}
		}
	}
</style>
