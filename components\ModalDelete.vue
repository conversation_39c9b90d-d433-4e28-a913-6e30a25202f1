<template>
  <div class="modal_delete">
    <a-modal
      v-model:open="showHide"
      title="提示"
      :getContainer="false"
      :confirmLoading="confirm_loading"
      @ok="deleteConfirm"
    >
      <div class="delete_modal_mess_contain">
        <div class="waring_icon"></div>
        <span class="warning_mess">{{ props.tips }}</span>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
const props = defineProps({
  tips: {
    type: String,
    default: '删除后将不可恢复，确定删除该数据吗？'
  }
})
const confirm_loading = ref(false)
// 是否显示删除的弹框
const showHide = ref(false)
const emit = defineEmits(['confirmFn'])
// 点击取消
const showHideDeleteModal = () => {
  showHide.value = !showHide.value
}
// 点击确认
const deleteConfirm = () => {
  if (!confirm_loading.value) {
    emit('deleteConfirm')
  }
  showHide.value = !showHide.value
}
const changeLoading = () => {
  confirm_loading.value = !confirm_loading.value
}
// 结束所有的modal 状态
const endDeleteModal = () => {
  showHide.value = false
}

onMounted(() => {})

defineExpose({
  showHideDeleteModal,
  deleteConfirm,
  endDeleteModal,
  changeLoading
})
</script>

<style lang="less" scoped></style>
