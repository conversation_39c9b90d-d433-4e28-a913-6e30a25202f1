<template>
  <div class="pagination">
    <a-pagination
      :showTotal="
        (total) => {
          return '共' + total + '条'
        }
      "
      v-model:current="currentPage"
      show-quick-jumper
      showSizeChanger
      :total="props.totalNum"
      @change="changePage"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  totalNum: {
    type: Number,
    default: 0
  }
})
const emit = defineEmits(['changePageSize'])


const currentPage = ref(1) // 当前页
const currentPageSize = ref(10) // 每页数据量

const changePage = (page, pageSize) => {
  
  if (!(currentPageSize.value === pageSize)) {
    // 重置page
    currentPageSize.value = pageSize
    currentPage.value = 1
  }

  let currentPageMess = {
    currentPage: currentPage.value,
    currentPageSize: currentPageSize.value
  }
  // 收集修改后的数据 传给父组件进行请求
  // emit('changePageSize')
  emit('changePageSize',currentPageMess)
}

const reset = ()=>{
  currentPage.value = 1;
  currentPageSize.value = 10;
}

defineExpose({
  reset,
})
</script>

<style lang="less" scoped>
.pagination {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-top: 28px;
  margin-bottom: 28px;
}
</style>
