<template>
  <!-- 项目树的选择 -->
  <a-modal
    v-model:open="modal_project_open"
    title="选择项目"
    forceRender
    :footer="null"
    :maskClosable="false"
  >
    <a-tree
      :tree-data="treeData"
      v-model:selectedKeys="selectedKeys"
      :multiple="false"
      :expanded-keys="expandedKeys"
      @expand="handleExpand"
      @select="select"
      :virtual="false"
      height="400"
    >
    </a-tree>
  </a-modal>
</template>

<script setup>
import { onMounted, ref, watch, watchEffect } from 'vue'
import { getAllAreaHttp, projectidGetMenuHttp } from '/components/commonApi.js'
import { projectChangeMenu } from '/pages/login/tool.js'
import { message } from 'ant-design-vue'

const props = defineProps({
  showHideModalStatus: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['showHideModal'])

//项目的选择
const modal_project_open = ref(false)

// 默认菜单
const menu_list = ref([
  // {
  //   name: '首页',
  //   menuImage: 'menu_icon_home',
  //   active: 0,
  //   menuUrl: '/pages/homeIndex/#/'
  // },
  // {
  //   name: '运行监控',
  //   menuImage: 'menu_icon_home',
  //   active: 0,
  //   children: [
  //     {
  //       name: '运行分析',
  //       active: 0,
  //       menuUrl: '/pages/operationMonitoring/#/analysis'
  //     },
  //     {
  //       name: '设备管理',
  //       active: 0,
  //       menuUrl: '/pages/operationMonitoring/#/deviceControl'
  //     },
  //     {
  //       name: '异常管理',
  //       active: 0,
  //       menuUrl: '/pages/operationMonitoring/#/unNormalControl'
  //     }
  //   ]
  // },
  // {
  //   name: '收益分析',
  //   menuImage: 'menu_icon_income',
  //   active: 0,
  //   children: [
  //     {
  //       name: '收益总览',
  //       active: 0,
  //       menuUrl: '/pages/incomeAnalysis/#/incomeAnalysis_all'
  //     }
  //   ]
  // },
  // {
  //   name: '运营中心',
  //   menuImage: 'menu_icon_report',
  //   active: 0,
  //   children: [
  //     {
  //       name: '综合报告',
  //       active: 0
  //     }
  //   ]
  // },
  // {
  //   name: '配置管理',
  //   menuImage: 'menu_icon_set',
  //   active: 0,
  //   children: [
  //     {
  //       name: '算法管理',
  //       active: 0
  //     },
  //     {
  //       name: '项目配置',
  //       active: 0,
  //       menuUrl: '/pages/configurationManagement/#/'
  //     },
  //     {
  //       name: '系统配置',
  //       active: 0
  //     }
  //   ]
  // }
])
// 树结构的数据
const treeData = ref([])
const expandedKeys = ref([])
const selectedKeys = ref([])

const handleExpand = (keys, { expanded, node }) => {
  // 只展开当前选中的部分
  if (expanded) {
    // 表示需要展开
    let result = node.key.split('-')
    let key_list = [node.key]
    if (result.length > 1) {
      key_list.push(result[0])
    }
    expandedKeys.value = key_list
  } else {
    // 关闭当前的
    expandedKeys.value = []
  }
}

const select = (selectedKeys, e) => {

  // 区域 园区 和项目的id 综合  e.node.title
  let project_id = null
  let project_title = null
  let area_park_pro = e.node.key.split('-')
  if (area_park_pro.length == 1) {
    message.warning('暂无区域切换！')
    return
  } else if (area_park_pro.length == 2) {

    message.warning('暂无园区切换！')
    return
  } else if (area_park_pro.length == 3) {
    project_id = area_park_pro.pop()
  }
  project_title = e.node.title
  //   存储project_id
  localStorage.setItem(
    'project_mess',
    JSON.stringify({
      project_id,
      project_title
    })
  )
  // 修改了项目选择 清空菜单的存储
  getMenuFn(project_id)
}

// 根据项目查询并获取菜单权限
const getMenuFn = (params) => {
  projectidGetMenuHttp(params)
    .then((data) => {
      // projectChangeMenu(menu_list.value)
      let current_menu_localstorage = projectChangeMenu(data)
      menu_list.value = current_menu_localstorage.all_menu
      localStorage.setItem('current_menu_localstorage', JSON.stringify(current_menu_localstorage))
      // 跳转到选中的菜单
      emit('showHideModal', 1)
      if (window.location.pathname == '/pages/homeIndex/') {
        // 首页切换刷新
        window.history.go(0)
      } else {
        window.location.href = current_menu_localstorage.current_skip_url
      }
    })
    .catch((error) => {
      message.error(error.message)
      setTimeout(() => {
        message.destroy()
      }, 1500)
    })
}

// // 切换项目后对页面菜单进行重置
// const projectChangeMenu = (current) => {
//   let first = 0
//   let second = null
//   // 全部设置为未选中状态并初始化赋值
//   let current_skip_url = null
//   let skip_url_list = []
//   for (let i = 0; i < current.length; i++) {
//     let item = current[i]
//     item.active = 0
//     // 默认选中第一个
//     i == 0 ? (item.active = 1) : null
//     if (Object.hasOwn(item, 'children')) {
//       second = 0
//       for (let j = 0; j < item.children.length; j++) {
//         let item1 = item[j]
//         item1.active = 0
//         // 默认选中第一个
//         j == 0 ? (item1.active = 1) : null
//       }
//     }
//   }
//   current_skip_url = current[0].menuUrl
//   // 存在二级菜单则跳转二级菜单的url
//   second ? (current_skip_url = current[0].children[0].menuUrl) : null
//   skip_url_list.push(current_skip_url)
//   menu_list.value = current
//   // 当前跳转的路径链接
//   // 存储选中菜单信息
//   let current_menu_localstorage = {
//     all_menu: current,
//     current_menu: {
//       // 当前菜单标记
//       first,
//       second
//     },
//     current_skip_url,
//     skip_url_list
//   }

//   localStorage.setItem('current_menu_localstorage', JSON.stringify(current_menu_localstorage))
//   // 跳转到选中的菜单
//   emit('showHideModal', 1)
//   window.location.href = current_skip_url
// }

watchEffect(() => {
  modal_project_open.value = props.showHideModalStatus
})

onMounted(() => {
  let project_tree = localStorage.getItem('project_tree')
  if (project_tree) {
    // 有数据直接使
    project_tree = JSON.parse(project_tree)
    treeData.value = project_tree
  } else {
    getAllAreaHttp({ proShow: 1 }).then((data) => {
      let all_list = []
      data.forEach((item) => {
        let { regionName: title, regionId: key } = item

        let temp_park_list = []
        item.parkList.forEach((item1) => {
          let { parkId: key_park, parkName: title } = item1
          let temp_project_list = []
          item1.proList.forEach((item2) => {
            let { projectId: key_pro, projectName: title } = item2
            temp_project_list.push({
              title,
              key: key + '-' + key_park + '-' + key_pro
            })
          })
          temp_park_list.push({
            title,
            key: key + '-' + key_park,
            children: temp_project_list
          })
        })
        all_list.push({
          title,
          key: key + '',
          children: temp_park_list
        })
      })
      treeData.value = all_list
      // 获取到的项目结构保存
      localStorage.setItem('project_tree', JSON.stringify(all_list))
      return all_list
    })
    //   .then((data) => {
    //     // 找到默认项目id
    //     let getProjectId = function (current_data) {
    //       for (let index = 0; index < current_data.length; index++) {
    //         let temp = current_data[index]
    //         if (Object.hasOwn(temp, 'children') && temp.children.length > 0) {
    //           for (let index1 = 0; index1 < temp.children.length; index1++) {
    //             let temp1 = temp.children[index1]
    //             if (Object.hasOwn(temp1, 'children') && temp1.children.length > 0) {
    //               return temp1.children[0].key
    //             }
    //           }
    //         }
    //       }
    //     }
    //     let result = getProjectId(data)

    //   })
  }
})
</script>
<style lang="less" scoped></style>
