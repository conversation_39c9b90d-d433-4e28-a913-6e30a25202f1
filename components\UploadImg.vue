<template>
  <a-upload
    :multiple="props.multiple"
    v-model:file-list="fileList"
    action="#"
    list-type="picture-card"
    @preview="handlePreview"
    @change="handleChange"
    :customRequest="customRequest"
    @remove="removeImg"
    :maxCount="props.imgNumber"
  >
    <div v-if="fileList.length < props.imgNumber">
      <img class="img_add_icon" src="/assets/icon/upload.png" alt="上传" />
    </div>
  </a-upload>
  <a-modal
    :open="previewVisible"
    :title="previewTitle"
    :footer="null"
    @cancel="handleCancel"
    :maskClosable="false"
  >
    <img alt="example" style="width: 100%" :src="previewImage" />
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { ref, watchEffect } from 'vue'
import { UploadImgHttp } from './upload.js'

const props = defineProps({
  imgNumber: {
    type: Number,
    default: 1
  },
  multiple: {
    type: Boolean,
    default: false
  },
  defaultImg: {
    type: Object,
    default: null
  }
})
const fileList = ref([])
const imgList = ref([])

watchEffect(() => {
  let tempData = []
  // 判断是否传过来的图片是空的还是默认的

  if (props.defaultImg.path) {
    tempData.push({
      name: props.defaultImg.name,
      path: props.defaultImg.path,
      uid: props.defaultImg.uid,
      url: props.defaultImg.url
    })
    fileList.value = tempData
  }
})

const getBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = (error) => reject({ message: error })
  })
}

const previewVisible = ref(false)
const previewImage = ref('')
const previewTitle = ref('')

// const beforeUpload = (file, fileList) => {


//     if (file.size > 2 * 1024 * 1024) {
//         message.error("请选择2M一下的图片！")
//         return new Promise(() => {
//             reject()
//         })
//     }
//     let type = true;
//     // 判断文件类型
//     fileList.forEach((item, index) => {
//         if (item.type != "image/png" && item.type != "image/jpg") {
//             type = false
//         }
//     })
//     if (!type) {
//         message.error("请选择png或者jpg类型图片！")
//         return new Promise(() => {
//             reject()
//         })
//     }
//     // 判断文件长度
//     if (fileList.length > props.imgNumber) {
//         message.error(`最多上传${props.imgNumber}张图片！`)
//         return new Promise(() => {
//             reject()
//         })
//     } else {
//         return new Promise(() => {
//             resolve()
//         })
//     }
// }

const handleCancel = () => {
  previewVisible.value = false
  previewTitle.value = ''
}
const handlePreview = async (file) => {
  if (!file.url && !file.preview) {
    file.preview = await getBase64(file.originFileObj)
  }
  previewImage.value = file.url || file.preview
  previewVisible.value = true
  previewTitle.value = file.name || file.url.substring(file.url.lastIndexOf('/') + 1)
}

const emit = defineEmits(['upDone'])

// 自定义上传的操作
const imgNum = ref(0)
const customRequest = async (option) => {

  console.log(fileList,'fileList');
  console.log(option,'option');
  
  
 
  // 数量限制
  if (imgNum.value >= props.imgNumber) {
    message.error(`最多上传${props.imgNumber}张图片！`)
    return
  }

  // 判断文件类型
  if (
    option.file.type != 'image/png' &&
    option.file.type != 'image/jpg' &&
    option.file.type != 'image/jpeg'
  ) {
    message.error('请选择png或者jpg、jpeg类型图片！')
   
    fileList.value.pop()

    return
  }
  // 大小限制
  let file = option.file
  if (file.size > 1 * 1024 * 1024) {
    fileList.value.pop()
    message.error('请选择1M以下的图片！')
    return
  }

  let tempFormData = new FormData() // 声明formData数据类型
  tempFormData.append('file', option.file)
  let params = {
    sourceType: 2,
    formData: tempFormData
  }
  let imgMess = await UploadImgHttp(params)

  imgNum.value = imgNum.value + 1

  let imgageData = {
    uid: option.file.uid,
    data: imgMess.data
    // name: imgMess.name,
    // path: imgMess.path,
    // url: imgMess.url
  }
  imgList.value.push(imgageData)
  let tempData = JSON.parse(JSON.stringify(imgList.value))

  option.onSuccess(imgageData, option.file)
  // 调用父组件方法
  emit('upDone', tempData)
}

const loading = ref(false)
const handleChange = (info) => {
  if (info.file.status === 'uploading') {
    loading.value = true
    return
  }
  if (info.file.status === 'done') {
    // Get this url from response in real world.
    loading.value = false

  }
  if (info.file.status === 'error') {
    loading.value = false
    message.error('upload error')
  }
}

const removeImg = (file) => {

  let result = imgList.value.filter((item) => item.uid !== file.uid)
  imgList.value = result
  imgNum.value = imgNum.value - 1

  // return
  emit('upDone', JSON.parse(JSON.stringify(imgList.value)))
}

const submit = () => {

  imgNum.value = 0
  return imgList.value
}

const reset = () => {
  // 重置上传组件的各个值为初始值
  imgList.value = []
  imgNum.value = 0
  fileList.value = []
}

defineExpose({
  submit,
  reset
})
</script>

<style lang="less" scoped>
.img_add_icon {
  height: 16px;
  width: 16px;
}

.img_contain {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;

  .img_upload {
    height: 80px;
    width: 80px;
    border: 1px solid #e9edf5;

    background-color: #f2f8ff;
    position: relative;
    border-radius: 5px;
    position: relative;
    overflow: hidden;

    img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  .img_upload_list {
    height: 80px;
    width: 80px;
    margin-right: 20px;
    border: 1px solid #e9edf5;
    background-color: #f2f8ff;
    border-radius: 5px;

    .img_show {
      display: block;
      height: 100%;
      width: 100%;
    }
  }

  .upload_img_input {
    visibility: hidden;
    width: 0 !important;
    height: 0 !important;
  }
}
</style>
