import http from '/tool/http.js'

// 获取所有区域数据
export const getAllAreaHttp = (params) => {
    let url = "/api/basic/set/treeAll";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {

            if (data.code == 200 && data.dataList.length > 0) {

                resolve(data.dataList)
            } 
            // else {
            //     // 空数据
            // }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}

// 获取登录用户菜单权限
export const getUserInfo = (params) => {
    let url = "/api/user/getUserInfo";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {

            if (data.code == 200 ) {
                resolve(data.data)
            } 
            // else {
            //     // 空数据
            // }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}

// 根据项目id获取项目菜单
export const projectidGetMenuHttp = (params) => {
    let url = `/api/menu/getPermisson/${params}`;
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200 && data.data.menuList.length > 0) {
                resolve(data.data.menuList)
            } 
            // else {
            //     // 空数据
            //     reject("菜单为空，请核对")
            // }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}

//获取用户有权限的项目列表
export const getAuthProjects = () => {
    let url = '/api/basic/set/getAuthProjects';
    return new Promise((resolve, reject) => {
        http.post(url).then((data) => {
            if (data.code == 200) {
                resolve(data.dataList)
            } 
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}

