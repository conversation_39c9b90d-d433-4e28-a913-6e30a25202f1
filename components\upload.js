import http from '/tool/http.js'
export const UploadImgHttp = async (paramsData) => {
    let url = `/api/file/imageUpload?sourceType=${paramsData.sourceType}`;
    const data = await http.post(url, paramsData.formData);
    return data
}

// 上传拓扑图
export const ProTopologyImageUpload = async (formData) => {
    let url = '/api/file/ProTopologyImageUpload';
    const data = await http.post(url, formData);
    return data
}




