upstream api_server  {
    server *************:8005;
}
# HTTP Server
server {
    # Port to listen on, can also be set in IP:PORT format
    listen  80;

    location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
     }

    location ^~/api{
       client_max_body_size 200m;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_buffering off;
       rewrite ^/api/(.*)$ /$1 break;
       proxy_pass http://api_server;
    }

}
