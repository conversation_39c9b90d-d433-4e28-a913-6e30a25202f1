{"name": "gh-iems", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@vitejs/plugin-vue": "^5.2.1", "ant-design-vue": "^4.1.1", "axios": "^1.6.7", "dayjs": "^1.11.10", "echarts": "^5.4.3", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "less": "^4.2.0", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "moment": "^2.30.1", "pinia": "^2.1.7", "pinia-plugin-persist": "^1.0.0", "postcss-px-to-viewport-8-plugin": "^1.2.5", "prettier": "^3.4.2", "prettier-plugin-vue": "^1.1.6", "typescript": "^5.5.4", "typescript-eslint": "^8.21.0", "unplugin-vue-components": "^0.26.0", "vite": "^6.1.0", "vue": "^3.5.13", "vue-cropper": "^1.1.4", "vue-router": "^4.2.5"}, "devDependencies": {"@eslint/js": "^9.19.0", "eslint": "^9.19.0", "eslint-plugin-vue": "^9.32.0", "globals": "^15.14.0"}}