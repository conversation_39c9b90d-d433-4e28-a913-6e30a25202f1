import { createRouter, createWebHashHistory } from 'vue-router'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'algorithm',
      component: () => import('../views/AlgorithmContain.vue'),
      children: [
        {
          path: 'algorithm_package',
          name: 'algorithm_package',
          component: () => import('../views/AlgorithmPackage.vue'),
        },
        {
          path: 'algorithm_simulation',
          name: 'algorithm_simulation',
          component: () => import('../views/AlgorithmSimulation.vue')
        },
        
        
      ]
    },
    
  ]
})

export default router