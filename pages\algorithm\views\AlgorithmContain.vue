<template>
  <div class="page_all AlgorithmContain">
    <MenuThird :third_menu="third_menu">
      <template #contact>
        <a-popover class="contact_popover"  trigger="click" placement="leftBottom">
          <template #content>
            <div class="contact_mess_all" >
              <div class="contact_mess">
              <img src="/assets/icon/service_phone.png" alt="#" />
              <div class="right_mess">
                <span>联系电话</span>
                <span>4000-000-000</span>
              </div>
            </div>
            <div class="contact_mess">
              <img src="/assets/icon/service_before.png" alt="#" />
              <div class="right_mess">
                <span>售前咨询及解决方案</span>
                <span>为您提供售前购买咨询、解决方案推</span>
                <span>荐等1V1服务，助您使用无忧！</span>
              </div>
            </div>
            <div class="contact_mess">
              <img src="/assets/icon/service_time.png" alt="#" />
              <div class="right_mess">
                <span>服务时间</span>
                <span>周一至周五：08:30-18:30； </span>
                <span>周六周日及法定节日：09:00-17:00</span>
              </div>
            </div>
            </div>
          </template>
          <div class="contact_us">
            <a-button type="primary" class="button_icon_type">
              联系我们
              <template #icon>
                <div class="contact"></div>
              </template>
            </a-button>
          </div>
        </a-popover>
      </template>
    </MenuThird>
    <RouterView></RouterView>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import MenuThird from '/components/MenuThird.vue'
const third_menu = ref([
  {
    name: '算法套餐',
    route_name: 'algorithm_package',
    url: '/algorithm_package'
  },
  {
    name: '算法仿真',
    route_name: 'algorithm_simulation',
    url: '/algorithm_simulation'
  }
])
</script>
<style lang="less" scoped>
.AlgorithmContain {
  padding-top: 32px;
  box-sizing: border-box;
  box-sizing: border-box;
}
// .contact_popover {
//   .ant-popover-arrow {
//     display: block !important;
//   }
// }
.contact_mess_all{
  height: 388px;
  width: 410px;
  padding: 30px;
  box-sizing: border-box;
}
.contact_mess {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin-bottom: 20px;
  img {
  width: 30px;
  height: 30px;
  margin-right: 25px;
}
}

.right_mess {
  span {
    display: block;
    font-family: SourceHanSansCN-Regular;
    font-weight: 400;
    font-size: 18px;
    height: 18px;
    line-height: 18px;
    color: #6a7582;
    margin-bottom: 17px;
    &:nth-child(1) {
      height: 20px;
      line-height: 20px;
      font-family: SourceHanSansCN-Bold;
      font-weight: bold;
      font-size: 20px;
      color: #092649;
      
    }
  }
}

.button_icon_type {
  font-family: SourceHanSansCN-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  width: 123px !important;
  .contact {
    background-image: url('/assets/icon/contact.png');
    background-repeat: no-repeat;
    height: 18px;
    width: 18px;
  }
}
.contact_us {
  position: absolute;
  right: 30px;
  top: 50%;
  transform: translate(0, -50%);
}
</style>
