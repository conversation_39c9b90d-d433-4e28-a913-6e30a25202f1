<template>
  <div class="main_contain main_contain_thirdMenu">
    <div class="main_contain_scroll">
      <div class="algorithm_item">
        <div class="cover_item"></div>
        <div class="title">
          <span>当前算法</span>
          <div class="control_list">
            <a-button class="button_icon_type" type="primary">
              下发
              <template #icon>
                <div class="xiafa"></div>
              </template>
            </a-button>
            <a-button class="button_icon_type" type="primary">
              解锁
              <template #icon>
                <div class="jiesuo"></div>
              </template>
            </a-button>
          </div>
        </div>
        <div class="item_all">
          <div class="item_list">
            <div class="bg_img"></div>
            <div class="bg_img2"></div>
            <div class="item_list_name">
              峰谷套利
              <span class="normal_tag">边端</span>
            </div>
            <div class="item_version">版本：v3.0</div>
          </div>
          <div class="item_list">
            <div class="bg_img2"></div>
            <div class="item_list_name">
              光伏处理预测
              <span class="normal_tag">云端</span>
            </div>
            <div class="item_version">版本：v3.0</div>
          </div>
          <div class="item_list">
            <div class="bg_img2"></div>
            <div class="item_list_name">
              负荷预测
              <span class="normal_tag">云端</span>
            </div>
            <div class="item_version">版本：v3.0</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup></script>
<style lang="less" scoped>
.algorithm_item {
  position: relative;
  height: 360px;
  width: 100%;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #dde2ed;
  // box-shadow: 0 0 0 #dde2ed;
  box-sizing: border-box;
  &:hover {
    border-radius: 8px;
    border: 1px solid #1181fd;
    
  }
  padding: 32px;
  .title {
    font-family: SourceHanSansCN-Medium;
    font-weight: 500;
    font-size: 24px;
    color: #092649;
    height: 36px;
    line-height: 36px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
  }
  .item_all {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .item_list {
      height: 210px;
      width: calc(33% - 33px);
      border-radius: 8px;
      position: relative;
      padding: 55px 34px;
      box-sizing: border-box;
      .bg_img {
        height: 40px;
        width: 50px;
        border: 1px solid red;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 1;
      }
      .bg_img2 {
        height: 72px;
        width: 72px;
        position: absolute;
        top: 50%;
        right: 7%;
        transform: translate(0, -50%);
      }
      &:nth-child(1) {
        background: linear-gradient(90deg, rgba(17, 129, 253, 0.18), rgba(17, 129, 253, 0.08));
        background-image: url('/assets/icon//algorithm_1.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        .bg_img2 {
          background-image: url('/assets/icon/fg_icon.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
      }
      &:nth-child(2) {
        background: linear-gradient(90deg, rgba(3, 227, 161, 0.18), rgba(3, 227, 161, 0.08));
        background-image: url('/assets/icon//algorithm_2.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        .bg_img2 {
          background-image: url('/assets/icon/gf_icon.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
      }
      &:nth-child(3) {
        background: linear-gradient(90deg, rgba(121, 133, 252, 0.18), rgba(121, 133, 252, 0.08));
        background-image: url('/assets/icon//algorithm_3.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        .bg_img2 {
          background-image: url('/assets/icon/fh_icon.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
        }
      }
      .item_list_name {
        width: max-content;
        font-family: SourceHanSansCN-Medium;
        font-weight: 500;
        font-size: 30px;
        line-height: 30px;
        height: 30px;
        color: #092649;
        span {
          margin-left: 10px;
          float: right;
          display: block;
          width: 50px !important;
          height: 30px !important;
          line-height: 30px;
          border-radius: 20px;
          font-family: SourceHanSansCN-Regular;
          font-weight: 400;
          font-size: 16px !important;
          color: #1181fd;
        }
      }
      .item_version {
        font-family: SourceHanSansCN-Regular;
        font-weight: 400;
        font-size: 20px;
        color: #9ca2af;
        margin-top: 46px;
      }
    }
  }
}
.cover_item{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9;
}
.button_icon_type {
  font-family: SourceHanSansCN-Regular;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  .xiafa {
    background-image: url('/assets/icon/xiafa.png');
    background-repeat: no-repeat;
    height: 18px;
    width: 18px;
  }
  .jiesuo {
    background-image: url('/assets/icon/lock.png');
    background-repeat: no-repeat;
    height: 18px;
    width: 18px;
  }
}
</style>
