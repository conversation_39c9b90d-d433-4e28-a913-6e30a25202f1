<template>
  <div class="main_contain main_contain_thirdMenu">
    <div class="main_contain_scroll">
      <a-form
        :model="form_state_echarts_line"
        name="horizontal_login"
        layout="inline"
        autocomplete="off"
      >
        <a-form-item label="时间选择" name="time">
          <a-range-picker
            :status="time_status_echarts_line ? 'error' : ''"
            :locale="zhCN"
            :allowClear="false"
            v-model:value="form_state_echarts_line.time"
            style="width: 300px; margin-right: 15px"
          >
            <template #suffixIcon>
              <img style="width: 18px; height: 18px" src="/assets/icon/clock.png" alt="#" />
            </template>
          </a-range-picker>
        </a-form-item>
      </a-form>

      <div class="suanfa">
        <div class="ai_bg_all">
          <div class="ai_bg_all_title">当前算法套餐</div>
          <div class="ai_bg_all_mess">
            <span class="mess_title">综合收益</span>
            <span class="mess_num"
              >122,495
              <i class="unit">元</i>
              <!-- <span class="percent"
                >1.3 <i>%</i>
                <span class="up"></span>
                <span class="down"></span>
              </span> -->
            </span>
          </div>
          <div class="ai_bg_all_mess">
            <span class="mess_title">综合IRR</span>
            <span class="mess_num"
              >4.65
              <i class="unit">%</i>
              <!-- <span class="percent"
                >1.3 <i>%</i>
                <span class="up"></span>
                <span class="down"></span>
              </span> -->
            </span>
          </div>
        </div>
        <div class="ai_bg_all_button">
          <span>开始仿真</span>
        </div>
        <div class="ai_bg_all">
          <div class="ai_bg_all_title">当前算法套餐</div>
          <div class="ai_bg_all_mess">
            <span class="mess_title">综合收益</span>
            <span class="mess_num"
              >122,495
              <i class="unit">元</i>
              <span class="percent"
                >1.3 <i>%</i>
                <span class="up"></span>
                <span class="down"></span>
              </span>
            </span>
          </div>
          <div class="ai_bg_all_mess">
            <span class="mess_title">综合IRR</span>
            <span class="mess_num"
              >4.65
              <i class="unit">%</i>
              <span class="percent"
                >1.3 <i>%</i>
                <span class="up"></span>
                <span class="down"></span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import dayjs from 'dayjs'
// 时间参数
const form_state_echarts_line = reactive({
  time: [dayjs().startOf('day'), dayjs()]
})
</script>
<style lang="less" scoped>
.main_contain_scroll{
  position: relative;
}
.suanfa {
  width: 100%;
  height: 340px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  font-size: 15px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  .ai_bg_all {
    padding: 35px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .ai_bg_all_title {
      font-family: SourceHanSansCN-Medium;
      font-weight: 500;
      font-size: 24px;
      color: #fefeff;
      height: 24px;
      line-height: 24px;
    }
    .ai_bg_all_mess {
      .mess_title {
        font-family: SourceHanSansCN-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        opacity: 0.7;
        display: block;
        margin-bottom: 10px;
      }
      .mess_num {
        font-family: D-DIN-PRO-Bold;
        font-weight: bold;
        font-size: 40px;
        color: #ffffff;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: bottom;

        .unit {
          font-family: SourceHanSansCN-Regular;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          opacity: 0.7;
          font-style: normal;
        }
        .percent {
          font-family: D-DIN-PRO-Bold;
          font-weight: bold;
          font-size: 24px;
          color: #ffffff;
          margin-left: 37px;
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          align-items: center;
          i {
            font-family: D-DIN-PRO-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            font-style: normal;
          }
          .up {
            display: block;
            background-image: url('/assets/icon/up_percent.png');
            background-repeat: no-repeat;
            width: 10px;
            height: 14px;
          }
          .down {
            display: block;
            background-image: url('/assets/icon/down_percent.png');
            background-repeat: no-repeat;
            width: 10px;
            height: 14px;
          }
        }
      }
    }
    &:nth-child(1) {
      // width: 630px;
      width: 33%;
      aspect-ratio: 63/34;
      background: linear-gradient(45deg, #0978f2, #3b97fd);
      border-radius: 8px;
      background-image: url('/assets/icon/ai_bg1.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
    &:nth-child(3) {
      // width: 630px;
      width: 33%;
      aspect-ratio: 63/34;
      background: linear-gradient(45deg, #4f64f2, #8897fc);
      border-radius: 8px;
      background-image: url('/assets/icon/ai_bg2.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
}
.ai_bg_all_button {
  font-family: SourceHanSansCN-Regular;
  font-weight: 400;
  font-size: 20px;
  line-height: 46px;
  text-align: center;
  color: #ffffff;
  width: 130px;
  height: 46px;
  background: #03e3a1;
  background-image: url('/assets/icon/ai_button.png');
  background-repeat: no-repeat;
  border-radius: 5px;
  cursor: pointer;
  user-select: none;
  margin: 0 111px;
  span {
    width: 100%;
  }
  &:hover {
    background-image: url('/assets/icon/ai_button_hover.png');
    background-repeat: no-repeat;
  }
}
</style>
