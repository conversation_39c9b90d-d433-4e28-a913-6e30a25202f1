import { createRouter, createWebHashHistory } from 'vue-router'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'default',
      // component: () => import('../views/DeviceConfiguration.vue'),
      redirect:'/projectDetail'
    },
    {
      path: '/projectDetail', //项目信息
      name: 'projectDetail',
      component: () => import('../views/ProjectDetail.vue')
    },
    {
      path: '/deviceConfiguration', //项目设备
      name: 'deviceConfiguration',
      component: () => import('../views/DeviceConfiguration.vue')
    },
    // 路由的配置需要在
    {
      path: '/electricPrice',// 电价配置
      name: 'ElectricPrice',
      component: () => import('../views/ElectricPrice.vue')
    },
    {
      path: '/policyConfiguration',// 策略配置
      name: 'PolicyConfiguration',
      component: () => import('../views/policyConfiguration.vue')
    },
    
  ]
})

export default router