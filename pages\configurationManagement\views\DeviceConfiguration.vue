<template>
	<div class="page_all current_page">
		<!-- 页面主要内容 -->
		<div class="third_menu_contain">
			<div
				:class="['third_menu_item', currentThirdMenu == index ? 'active' : '']"
				@click="menuChange(item, index)"
				v-for="(item, index) in third_menu_list"
				:key="index"
			>
				<span class="third_menu_name">{{ item.name }}</span>
				<span class="third_menu_line"></span>
			</div>
			<a-button
				class="third_menu_contain_button_add"
				style="float: right"
				type="primary"
				@click="showEageDrawer"
			>
				<template #icon>
					<img
						class="button_icon_add"
						src="/assets/icon/add.png"
						alt=""
					/>
				</template>
				新增
			</a-button>
		</div>
		<div class="main_contain main_contain_thirdMenu">
			<div class="main_contain_scroll">
				<!-- style="min-height: 400px" -->
				<a-table
					class="unique_table"
					:columns="edge_columns"
					:data-source="edge_device_data"
					:bordered="false"
					:pagination="false"
					@expand="expandFirst"
					:expandedRowKeys="expandedRowKeys"
				>
					<template #bodyCell="{ column, record }">
						<template v-if="column.key === 'control'">
							<span style="display: flex; flex-direction: row">
								<a @click="openElementDrawer(record)">新增元素</a>
								<span style="margin: 0 11px"></span>
								<a @click="editEdgeDeviceItem(record)">编辑</a>
								<span style="margin: 0 11px"></span>

								<a
									href="javascript:void(0)"
									@click="showDeleteEdge(record)"
								>
									删除
								</a>
								<DeleteModal
									ref="deleteModal"
									@deleteConfirm="deleteDeviceListItem"
								></DeleteModal>
								<!-- </a-popconfirm> -->
							</span>
						</template>
						<template v-if="column.key === 'controType'">
							{{ record[column.key] == '1' ? '边缘控制器' : record[column.key] }}
						</template>
						<template v-if="column.key === 'controName'">
							<a-tooltip
								placement="topLeft"
								:title="record[column.key]"
							>
								<div
									class="text_elipsis"
									style="
										width: 260px;
										overflow: hidden;
										text-overflow: ellipsis;
										white-space: nowrap;
									"
								>
									{{ record[column.key] }}
								</div>
							</a-tooltip>
						</template>
					</template>

					<template
						class="aaaaaa"
						#expandedRowRender="{ record }"
					>
						<a-table
							:columns="device_element_columns"
							:data-source="device_element_data"
							:bordered="false"
							:pagination="false"
						>
							<template #bodyCell="{ column, record }">
								<template v-if="column.key === 'control'">
									<span style="display: flex; flex-direction: row">
										<a @click="addConnectDevice(record, column.key)">关联设备</a>
										<span style="margin: 0 11px"></span>
										<a @click="editElementItem(record, column)">编辑</a>
										<span style="margin: 0 11px"></span>
										<a
											@click="showDeleteElement"
											href="javascript:void(0)"
										>
											删除
										</a>
										<DeleteModal
											ref="deleteModal_element"
											@deleteConfirm="deleteElement(record)"
										></DeleteModal>
									</span>
								</template>
								<template v-if="column.key === 'capacity_show' && !record.capacityTwo">
									{{ record.capacity + record.unit }}
								</template>
								<template v-if="column.key === 'capacity_show' && record.capacityTwo">
									{{ record.capacity + record.unit + '/' + record.capacityTwo + record.unit1 }}
								</template>
							</template>
						</a-table>
					</template>
				</a-table>

				<Pagination
					@changePageSize="changePageSize"
					:totalNum="totalNum"
				></Pagination>
			</div>
		</div>
		<!-- 边缘控制器 -->
		<a-drawer
			v-model:open="edge_open"
			class="custom-class"
			root-class-name="root-class-name"
			:closable="true"
			:getContainer="false"
			:title="drawerTitle"
			placement="right"
			width="37.5%"
			@close="closeEdgeDrawer"
		>
			<a-form
				id="edge_control_device"
				ref="edge_control_device"
				:model="form_state_edge_device"
				name="basic"
				:label-col="{ span: 24 }"
				:wrapper-col="{ span: 24 }"
				autocomplete="off"
				labelAlign="left"
				layout="horizontal"
			>
				<a-form-item
					label="控制器名称"
					name="controName"
					:rules="[
						{ required: true, message: '请输入控制器名称' },
						{
							required: true,
							validator: (rule, value) => {
								return controlDeviceName(value);
							},
							message: '不支持空格，最多输入64个字符'
						}
					]"
				>
					<a-input
						autocomplete="off"
						placeholder="请输入控制器名称"
						v-model:value="form_state_edge_device.controName"
					/>
				</a-form-item>

				<a-form-item
					label="控制器设备编码(生态平台产品ID-设备名称)"
					name="controCode"
					:rules="[
						{ required: true, message: '请输入控制器设备编码' },
						{
							required: true,
							validator: (rule, value) => {
								return controlDeviceId(value);
							},
							message: '控制器设备编码只支持英文数字_-，最多64个字符'
						}
					]"
				>
					<a-input
						autocomplete="off"
						placeholder="请输入控制器设备编码"
						v-model:value="form_state_edge_device.controCode"
					/>
				</a-form-item>

				<a-form-item
					label="国网关口表"
					name="gridTableName"
					:rules="[
						{ required: true, message: '请输入国网关口表' },
						{
							required: true,
							validator: (rule, value) => {
								return countryTableDevice(value);
							},
							message: '国网关口表只支持英文数字，最多64个字符'
						}
					]"
				>
					<a-input
						autocomplete="off"
						placeholder="请输入国网关口表"
						v-model:value="form_state_edge_device.gridTableName"
					/>
				</a-form-item>
			</a-form>
			<div class="form_self_normal">
				<div class="form_item">
					<div
						class="form_label"
						style="margin-top: 20px"
					>
						设备类型资产
					</div>
					<div class="form_value form_table_edit1 rule_setting_drawer">
						<div
							class="form_table_edit_item"
							v-for="(item, index) in columns_rule"
							:key="index"
						>
							<a-form
								:id="'form' + index"
								layout="inline"
								:model="item"
								style="width: 100%"
							>
								<a-form-item
									name="deviceType"
									style="width: 200px; min-width: 90px"
								>
									<a-select
										ref="select"
										v-model:value="item.deviceType"
										:options="device_type_list"
										placeholder="请选择设备类型"
										:getPopupContainer="(e) => e.parentNode"
									></a-select>
								</a-form-item>
								<a-form-item
									name="nodeId"
									style="width: 100%; min-width: 90px"
								>
									<a-input
										autocomplete="off"
										placeholder="请填写平台资产ID"
										v-model:value="item.nodeId"
									/>
								</a-form-item>
								<a-form-item name="control">
									<div class="add_delete_table">
										<div class="add_delete_table_item">
											<div
												@click="add_line"
												class="add_delete_button add_table"
											></div>
											<div
												@click="delete_line(index)"
												class="add_delete_button delete_table"
											></div>
										</div>
									</div>
								</a-form-item>
							</a-form>
						</div>
					</div>
				</div>
			</div>

			<template #footer>
				<a-space
					class="drawer_footer"
					align="center"
				>
					<a-button @click="cancleEdgeControlDevice">取消</a-button>
					<a-button
						:loading="upLoad_edge_control_loading"
						@click="upLoadEdgeControlDeviceForm"
						type="primary"
					>
						确定
					</a-button>
				</a-space>
			</template>
		</a-drawer>

		<!-- 新增  编辑 边缘控制器的元素类型 -->
		<a-drawer
			v-model:open="device_element_open"
			class="custom-class"
			root-class-name="root-class-name"
			:closable="true"
			:getContainer="false"
			:title="drawer_device_element_title"
			placement="right"
			width="38.5%"
			@close="elmentDrawerClose"
		>
			<a-form
				ref="form_state_device_element"
				:model="form_state_element_device"
				name="form_state_device_element"
				:label-col="{ span: 24 }"
				:wrapper-col="{ span: 24 }"
				autocomplete="off"
				labelAlign="left"
				layout="horizontal"
			>
				<a-form-item
					label="控制器设备编码"
					name="controCode"
					:rules="[{ required: true }]"
				>
					<a-input
						autocomplete="off"
						disabled
						v-model:value="form_state_element_device.controCode"
					/>
				</a-form-item>

				<a-form-item
					label="元素类型"
					name="elementCode"
					:rules="[{ required: true, message: '元素类型不能为空' }]"
				>
					<a-select
						v-model:value="form_state_element_device.elementCode"
						:disabled="element_id ? true : false"
						placeholder="请选择对应的设备元素类型"
						:getPopupContainer="(e) => e.parentNode"
						@select="elementCodeSelect"
					>
						<a-select-option
							v-for="(itemSelect, indexSelect) in device_element_list"
							:key="indexSelect"
							:value="itemSelect.value"
							:disabled="itemSelect.status ? false : true"
						>
							{{ itemSelect.label }}
						</a-select-option>
					</a-select>
				</a-form-item>

				<a-row v-if="form_state_element_device.elementCode == 'CN' ? true : false">
					<a-col style="width: 50%">
						<a-form-item
							label="储能功率"
							name="capacity"
							:rules="[
								{
									required: true,
									message: '储能功率只支持小数点后两位数字',
									validator: (rule, value) => {
										return isNumberForm(value);
									}
								}
							]"
						>
							<a-input
								autocomplete="off"
								placeholder="请输入储能功率"
								style="width: 190px"
								v-model:value="form_state_element_device.capacity"
							/>
							<span style="margin-left: 10px">kW</span>
						</a-form-item>
					</a-col>
					<a-col style="width: 50%; padding-top: 44px">
						<a-form-item
							name="capacityTwo"
							:rules="[
								{
									required: true,
									message: '储能容量只支持小数点后两位数字',
									validator: (rule, value) => {
										return isNumberForm(value);
									}
								}
							]"
						>
							<a-input
								autocomplete="off"
								style="width: 190px"
								v-model:value="form_state_element_device.capacityTwo"
								placeholder="请输入储能容量"
							/>
							<span style="margin-left: 10px">kWh</span>
						</a-form-item>
					</a-col>
				</a-row>

				<a-form-item
					label="光伏容量"
					name="capacity"
					v-if="form_state_element_device.elementCode == 'GF' ? true : false"
					:rules="[
						{
							required: true,
							message: '光伏容量只支持小数点后两位数字',
							validator: (rule, value) => {
								return isNumberForm(value);
							}
						}
					]"
				>
					<a-input
						autocomplete="off"
						placeholder="请输入光伏容量"
						style="width: 190px"
						v-model:value="form_state_element_device.capacity"
					/>
					<span style="margin-left: 10px">kWp</span>
				</a-form-item>

				<a-form-item
					label="充电桩容量"
					name="capacity"
					v-if="form_state_element_device.elementCode == 'CDZ' ? true : false"
					:rules="[
						{
							required: true,
							message: '充电桩容量只支持小数点后两位数字',
							validator: (rule, value) => {
								return isNumberForm(value);
							}
						}
					]"
				>
					<a-input
						autocomplete="off"
						placeholder="请输入充电桩容量"
						style="width: 190px"
						v-model:value="form_state_element_device.capacity"
					/>
					<span style="margin-left: 10px">kWh</span>
				</a-form-item>
			</a-form>

			<template #footer>
				<a-space
					class="drawer_footer"
					align="center"
				>
					<a-button @click="cancelElement">取消</a-button>
					<a-button
						:loading="element_loading"
						@click="addEditElement"
						type="primary"
					>
						确定
					</a-button>
				</a-space>
			</template>
		</a-drawer>

		<!-- 关联设备的抽屉  -->
		<a-drawer
			v-model:open="connect_device_open"
			class="custom-class"
			root-class-name="root-class-name"
			:closable="true"
			:getContainer="false"
			:title="connect_device_title"
			placement="right"
			width="70%"
		>
			<a-form
				id="different_device_detail"
				ref="different_device_detail"
				:model="connect_device_formstate"
				name="different_device_detail"
				:label-col="{ span: 24 }"
				:wrapper-col="{ span: 24 }"
				autocomplete="off"
				labelAlign="left"
				layout="horizontal"
			>
				<a-form-item
					label="控制器设备编码"
					name="type"
				>
					<a-input
						autocomplete="off"
						disabled
						v-model:value="connect_device_formstate.controCode"
					/>
				</a-form-item>

				<a-row>
					<a-col
						style="width: 50%"
						v-if="isNumber(connect_device_formstate.capacity) ? true : false"
					>
						<a-form-item
							:label="connect_device_formstate.elementName + '容量'"
							name="capacity"
							:rules="[
								{
									required: true,
									message: '请输入容量值',
									validator: (rule, value) => {
										return isNumberForm(value);
									}
								}
							]"
						>
							<a-input
								autocomplete="off"
								placeholder="请输入容量"
								style="width: 190px"
								disabled
								v-model:value="connect_device_formstate.capacity"
							/>
							<span
								v-if="connect_device_formstate.elementCode == 'GF'"
								style="margin-left: 10px"
							>
								kWp
							</span>
							<span
								v-if="connect_device_formstate.elementCode == 'CDZ'"
								style="margin-left: 10px"
							>
								kWh
							</span>
							<span
								v-if="connect_device_formstate.elementCode == 'CN'"
								style="margin-left: 10px"
							>
								kW
							</span>
						</a-form-item>
					</a-col>
					<a-col
						style="width: 50%; padding-top: 44px"
						v-if="connect_device_formstate.capacityTwo ? true : false"
					>
						<a-form-item
							name="capacityTwo"
							:rules="[
								{
									required: true,
									message: '请输入容量值',
									validator: (rule, value) => {
										return isNumberForm(value);
									}
								}
							]"
						>
							<a-input
								autocomplete="off"
								style="width: 190px"
								disabled
								v-model:value="connect_device_formstate.capacityTwo"
								placeholder="请输入容量"
							/>
							<span style="margin-left: 10px">
								{{ connect_device_formstate.elementCode == 'CN' ? 'kWh' : 'kW' }}
							</span>
						</a-form-item>
					</a-col>
				</a-row>

				<!-- 关联设备的表格 -->

				<div class="form_self_normal">
					<div class="form_item">
						<div class="form_label">关联设备</div>
						<div class="form_value form_table_edit">
							<a-table
								class="ant_edit_table_last_cloumn"
								:columns="columns_connect_device"
								:data-source="connect_device_table"
								:pagination="false"
							>
								<template #headerCell="{ column }">
									<template v-if="column.key === 'control'">
										<div class="add_delete_table">
											<div class="add_delete_table_item">
												<div
													v-if="connect_device_table.length > 0 ? false : true"
													class="add_delete_button add_table"
													@click="addTableLine"
												></div>
											</div>
										</div>
									</template>
									<template v-if="column.key === 'deviceType'">
										<a-tooltip placement="right">
											<template #title>
												<span>对应设备类型需要在控制器编辑页面进行维护</span>
											</template>
											{{ column.title }}
											<img
												style="margin-left: 3px"
												src="/assets/icon/tips.png"
												alt="#"
											/>
										</a-tooltip>
									</template>
								</template>
								<template
									#bodyCell="{ column, record }"
									v-if="connect_device_table.length > 0 ? true : false"
								>
									<a-form
										v-if="connect_device_table.length > 0 ? true : false"
										layout="inline"
										ref="editTableFormRef"
										:model="edit_table_data[record.key]"
										style="width: 100%"
									>
										<a-form-item
											style="width: 100%"
											name="deviceName"
											v-if="column.dataIndex == 'no'"
										>
											{{ edit_table_data[record.key]['no'] }}
										</a-form-item>
										<a-form-item
											style="width: 100%"
											name="deviceName"
											v-if="
												column.dataIndex == 'deviceName' && connect_device_table.length > 0
													? true
													: false
											"
											:rules="[
												{ required: true, message: '请输入设备名称' },
												{
													required: true,
													validator: (rule, value) => {
														return controlDeviceName(value);
													},
													message: '不支持空格，最多输入64个字符'
												}
											]"
										>
											<a-input
												autocomplete="off"
												v-model:value="edit_table_data[record.key]['deviceName']"
											/>
										</a-form-item>
										<a-form-item
											name="deviceType"
											v-else-if="column.dataIndex == 'deviceType'"
											:rules="[{ required: true, message: '设备类型不能为空' }]"
											style="width: 100%; min-width: 90px"
										>
											<a-select
												ref="select"
												v-model:value="edit_table_data[record.key]['deviceType']"
												:options="element_device_type_list"
												:getPopupContainer="(e) => e.parentNode"
												@change="(value, e) => selectDeviceType(edit_table_data[record.key], e)"
											></a-select>
										</a-form-item>
										<a-form-item
											name="electType"
											v-else-if="column.dataIndex == 'electType'"
											:rules="[{ required: true, message: '设备属性不能为空' }]"
											style="width: 100%; min-width: 140px"
										>
											<a-tooltip placement="top">
												<template #title>
													<span>当设备类型非标表计时,属性选择不可用</span>
												</template>
												<a-select
													ref="select"
													:disabled="
														edit_table_data[record.key]['deviceType'] == 'EMS' ? true : false
													"
													:getPopupContainer="(e) => e.parentNode"
													v-model:value="edit_table_data[record.key]['electType']"
													:options="device_key_list"
												></a-select>
											</a-tooltip>
										</a-form-item>
										<a-form-item
											name="deviceCode"
											v-else-if="column.dataIndex == 'deviceCode'"
											:rules="[
												{ required: true, message: '请输入平台设备编号' },
												{
													required: true,
													validator: (rule, value) => {
														return controlDeviceCode(value);
													},
													message: '平台设备编号只支持中英文数字下划线横杠，最多64个字'
												}
											]"
											style="width: 100%"
										>
											<a-input
												autocomplete="off"
												v-model:value="edit_table_data[record.key]['deviceCode']"
											/>
										</a-form-item>

										<a-form-item
											name="voltageRatio"
											:rules="[
												{
													required: true,
													validator: (rule, value) => {
														return isNumberFormPositive(value);
													},
													message: '电压变比只能为数字'
												}
											]"
											v-else-if="column.dataIndex == 'voltageRatio'"
											style="width: 100%"
										>
											<a-tooltip placement="top">
												<template #title>
													<span>当设备类型非标表计时,属性选择不可用</span>
												</template>
												<a-input
													autocomplete="off"
													:disabled="
														edit_table_data[record.key]['deviceType'] == 'EMS' ? true : false
													"
													v-model:value="edit_table_data[record.key]['voltageRatio']"
												/>
											</a-tooltip>
										</a-form-item>

										<a-form-item
											v-else-if="column.dataIndex == 'currentRatio'"
											name="currentRatio"
											style="width: 100%"
											:rules="[
												{
													required: true,
													validator: (rule, value) => {
														return isNumberFormPositive(value);
													},
													message: '电流变比只能为数字'
												}
											]"
										>
											<a-tooltip placement="top">
												<template #title>
													<span>当设备类型非标表计时,属性选择不可用</span>
												</template>
												<a-input
													autocomplete="off"
													:disabled="
														edit_table_data[record.key]['deviceType'] == 'EMS' ? true : false
													"
													v-model:value="edit_table_data[record.key]['currentRatio']"
												/>
											</a-tooltip>
										</a-form-item>
										<a-form-item
											name="control"
											v-else-if="column.dataIndex == 'control'"
											style="width: 100%"
										>
											<div class="add_delete_table">
												<div class="add_delete_table_item">
													<div
														v-if="edit_table_data[record.key]['control']"
														class="add_delete_button add_table"
														@click="addTableLine"
													></div>
													<div
														class="add_delete_button delete_table"
														@click="deleteTableLine(record.key)"
													></div>
												</div>
											</div>
										</a-form-item>
									</a-form>
								</template>
							</a-table>
						</div>
					</div>
				</div>
			</a-form>
			<template #footer>
				<a-space
					class="drawer_footer"
					align="center"
				>
					<a-button @click="cancleConnectDevice">取消</a-button>
					<a-button
						:loading="up_connect_device_loading"
						@click="upLoadElementContactDeviceList"
						type="primary"
					>
						确定
					</a-button>
				</a-space>
			</template>
		</a-drawer>
	</div>
</template>

<script setup>
	import { ref, watch, reactive, onMounted } from 'vue';
	// import { deviceType } from '../assets/configuration/deviceConfiguration'

	import { message } from 'ant-design-vue';

	// 分页的结构
	import Pagination from '/components/Pagination.vue';

	// 校验规则
	import {
		controlDeviceName,
		controlDeviceCode,
		controlDeviceId,
		countryTableDevice,
		elementConnectDeviceName,
		elementConnectDeviceCode,
		isNumber,
		isNumberForm,
		isNumberFormPositive,
		platId
	} from '/assets/js/reg.js';
	import { getEageDeviceListHttp, getDeviceTypeRelAssetSimpleList } from '/assets/js/commonApi.js';
	import {
		addNewEdgeControlDeviceHttp,
		editEdgeControlDeviceHttp,
		deleteEdgeDeviceHttp,
		getDictHttp,
		addElementHttp,
		editElementHttp,
		deleteElementHttp,
		elementContactDeviceHttp,
		cacheDevices,
		findDevicePageHttp,
		getByIdEdgeDetail,
		getControlConnectDeviceDetail
	} from './api.js';
	import { useRouter } from 'vue-router';
	import DeleteModal from '/components/ModalDelete.vue';
	const router = useRouter();
	// --------------------------------------测试 新增边端控制器

	// ------------------------------------form表单
	const rule_setting_formState = reactive({
		ruleName: null,
		elementType: null,
		deviceType: null,
		deviceIds: null,
		warnLevel: null,
		controId: null
	});

	// 设备类型的列表数据
	const device_type_list = ref([]);

	const getDevicTypeList = () => {
		let params = 'EDGE_CONTRO';
		getDictHttp(params)
			.then((data) => {
				data.forEach((item) => {
					device_type_list.value.push({ label: item.dictValue, value: item.dictKey });
				});
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	// 表单中的是可编辑设备列表  设备类型资产
	const columns_rule = ref([{ deviceType: null, id: null, nodeId: null }]);

	// ------------------------规则新增

	const add_line = () => {
		columns_rule.value.push({ deviceType: null, id: null, nodeId: null });
	};

	const delete_line = (index) => {
		columns_rule.value.splice(index, 1);

		if (columns_rule.value.length == 0) {
			add_line();
		}
	};
	// -------------------------规则新增

	// -----------测试边端控制器

	// 全局数据
	const project_id = ref(null);

	// ******************************************************************************三级菜单切换
	const third_menu_list = ref([
		{ name: '项目信息', url: '/projectDetail' },
		{ name: '项目设备', url: '/deviceConfiguration' },
		{ name: '电价配置', url: '/electricPrice' },
		{ name: '策略配置', url: '/policyConfiguration' }
	]);

	const currentThirdMenu = ref(1);
	const menuChange = (item, index) => {
		currentThirdMenu.value = index;
		router.push(item.url);
	};

	// ********************************************************************************分页器
	const totalNum = ref(10); // 表格数据总数
	const edge_params = reactive({ pageIndex: 1, pageSize: 10, projectId: null });
	const changePageSize = (currentPageMess) => {
		edge_params.pageSize = currentPageMess.currentPageSize;
		edge_params.pageIndex = currentPageMess.currentPage;
		getEageDeviceList();
	};

	// table 相关
	const edge_columns = ref([
		{ title: '序号', dataIndex: 'no', key: 'no', align: 'center', width: '10%' },
		{ title: '控制器名称', dataIndex: 'controName', key: 'controName', width: '15%' },
		{ title: '类型', dataIndex: 'controType', key: 'controType', width: '15%' },
		{ title: '控制器设备编码', key: 'controCode', dataIndex: 'controCode', width: '15%' },
		{ title: '国网关口表', key: 'gridTableName', dataIndex: 'gridTableName', width: '15%' },
		{ title: '操作', key: 'control', dataIndex: 'control', width: '30%' }
	]);
	const edge_device_data = ref([]);

	const edge_contro_code = ref(null);

	// 根据项目ID获取控制器列表
	const getEageDeviceList = () => {
		edge_device_data.value = [];
		// show_hide_expand.value = false
		// Object.assign(device_element_data, [])
		device_element_data.value = [];
		let params = JSON.parse(JSON.stringify(edge_params));
		getEageDeviceListHttp(params).then((data) => {
			totalNum.value = data.total;
			data.list.forEach((item, index) => {
				// 增加key值。在展开子表格的时候有区分作用
				item.key = index;
				item.no = index + 1;
			});
			edge_device_data.value = data.list;
		});
	};

	// 展开表格的触发函数
	// const already_element = ref([]) //已经存在的元素表
	const expandedRowKeys = ref([]); // 默认展开的是第几行的数据 根据标志 0123依次排序
	// antd插件巨坑地方

	const initTableRow = () => {
		// 关闭展开行
		expandedRowKeys.value = [];
	};

	const expandFirst = (expanded, record) => {
		initTableRow();

		edge_contro_code.value = record.controCode;
		if (expanded) {
			// 展开
			edge_device_id.value = record.id; // 边端控制器的id赋值
			let temp_data_all = JSON.parse(JSON.stringify(edge_device_data.value));
			edge_device_data.value = [];

			for (let index = 0; index < temp_data_all.length; index++) {
				if (
					record.id == temp_data_all[index].id &&
					record.controCode == temp_data_all[index].controCode
				) {
					expandedRowKeys.value = [index];
				}
			}
			edge_device_data.value = temp_data_all;
			let temp_data = record.detailList;
			let result = [];
			device_element_list.value.forEach((item1) => {
				item1.status = 1;
			});
			temp_data.forEach((item, index) => {
				let sort = 1;
				device_element_list.value.forEach((item1) => {
					if (item.elementCode == item1.value) {
						sort = item1.sort;
					}
				});
				let controCode = record.controCode;
				let { controId, deviceNum, elementCode, elementName, id } = item;
				let capacity = '';
				let capacityTwo = '';
				let capacity_show = '';
				let unit = null;
				let unit1 = null;

				if (item.paramList.length == 1) {
					capacity = item.paramList[0].capacity;
					capacity_show = capacity;
					unit = item.paramList[0].unit;
				} else if (item.paramList.length == 2) {
					capacity = item.paramList[0].capacity;
					capacityTwo = item.paramList[1].capacity;
					capacity_show = capacity + '/' + capacityTwo;
					unit = item.paramList[0].unit;
					unit1 = item.paramList[1].unit;
				} else {
					capacity_show = '';
					unit = '';
					unit1 = '';
				}
				result.push({
					controCode,
					controId,
					deviceNum,
					elementCode,
					elementName,
					id,
					capacity,
					capacityTwo,
					capacity_show,
					key: index,
					unit,
					unit1,
					sort
				});
				device_element_list.value.forEach((item1) => {
					if (item1.value == elementCode) {
						item1.status = 0;
					}
				});
			});
			device_element_data.value = [];

			device_element_data.value = result;
		} else {
			expandedRowKeys.value = [];
			device_element_data.value = [];
			edge_device_id.value = null;
		}
	};

	// 新增抽屉
	const edge_open = ref(false);
	const drawerTitle = ref('新增边缘控制器');

	// 关闭抽屉的回调
	const closeEdgeDrawer = () => {
		edge_control_device.value.clearValidate();
		edge_control_device.value.resetFields();
		// 列表为空
		columns_rule.value = [{ deviceType: null, id: null, nodeId: null }];
	};
	const edit_or_add = ref(0); // 0 新增 1 编辑

	const showEageDrawer = () => {
		edit_or_add.value = 0;
		edge_open.value = true;
		drawerTitle.value = '新增边缘控制器';
		// 新增需要初始化
		Object.assign(form_state_edge_device, {
			controName: null,
			controCode: null,
			gridTableName: null
		});

		initTableRow();
	};
	const edge_control_device = ref();
	const form_state_edge_device = reactive({
		controName: null,
		controCode: null,
		gridTableName: null
	});

	// 提交边缘控制器表单和编辑表单
	const upLoad_edge_control_loading = ref(false);
	const upLoadEdgeControlDeviceForm = () => {
		upLoad_edge_control_loading.value = true;
		edge_control_device.value
			.validateFields()
			.then((data) => {
				// 此时正常提交
				let { controCode, controName, gridTableName } = data;
				let params = { projectId: project_id.value, controCode, controName, gridTableName };
				let assetList = [];
				let flag = false;
				columns_rule.value.forEach((item) => {
					let temp = { deviceType: item.deviceType, nodeId: item.nodeId };
					// nodeId 校验是否符合规则
					if (!platId(item.nodeId)) {
						message.error('平台资产ID为64位以内的数字字母和横线下划线组合');
						upLoad_edge_control_loading.value = false;
						return;
					}
					if (item.id) {
						temp.id = item.id;
					}
					if (item.deviceType && item.nodeId) {
						assetList.push(temp);
					} else if ((item.deviceType && !item.nodeId) || (!item.deviceType && item.nodeId)) {
						// 当填写了数据，但是仅仅填写了一个就需要提示
						flag = true;
						return;
					}
				});
				if (flag) {
					message.warning('已填写的数据存在不全的情况，请修改后再提交');
					upLoad_edge_control_loading.value = false;
					return;
				}

				if (assetList.length > 0) {
					params.assetList = assetList;
				}

				if (edit_or_add.value == 1) {
					// 编辑
					params.id = edge_device_id.value;
					editEdgeControlDeviceHttp(params)
						.then((data) => {
							if (data) {
								message.success('保存成功！');
								edge_open.value = false;
								// 重新查询一遍
								getEageDeviceList();
								upLoad_edge_control_loading.value = false;
								expandedRowKeys.value = [];
								device_element_data.value = [];
								edge_device_id.value = null;
							} else {
								message.error('保存失败！');
								upLoad_edge_control_loading.value = false;
							}
						})
						.catch((errorInfo) => {
							message.error(errorInfo.message);
							upLoad_edge_control_loading.value = false;
							setTimeout(() => {
								message.destroy();
							}, 1500);
						});
				} else if (edit_or_add.value == 0) {
					// 新增
					addNewEdgeControlDeviceHttp(params)
						.then((data) => {
							if (data) {
								message.success('保存成功！');
								edge_open.value = false;
								// 重新查询一遍
								upLoad_edge_control_loading.value = false;
								expandedRowKeys.value = [];
								device_element_data.value = [];
								edge_device_id.value = null;
								getEageDeviceList();
							} else {
								message.error('保存失败！');
								upLoad_edge_control_loading.value = false;
							}
						})
						.catch((errorInfo) => {
							message.error(errorInfo.message);
							upLoad_edge_control_loading.value = false;
							setTimeout(() => {
								message.destroy();
							}, 2000);
						});
				}
			})
			.catch(() => {
				upLoad_edge_control_loading.value = false;
			});
	};

	//取消边缘控制器的数据添加
	const cancleEdgeControlDevice = () => {
		// 重置表格
		// edge_control_device.value.resetFields()
		closeEdgeDrawer();
		edge_open.value = false;
	};

	// 对边缘控制器 进行编辑
	const edge_device_id = ref(null); //边缘控制器的id
	const editEdgeDeviceItem = (record) => {
		initTableRow();
		drawerTitle.value = '编辑边缘控制器';
		edit_or_add.value = 1;
		let params = record.id;
		getByIdEdgeDetail(params)
			.then((data) => {
				columns_rule.value = data.assetList;
				if (!data.assetList) {
					// 列表为空
					columns_rule.value = [{ deviceType: null, id: null, nodeId: null }];
				}
			})
			.catch((err) => {
				message.error(err.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
		edge_open.value = true;
		edge_device_id.value = record.id;
		// 编辑的初始赋值
		Object.assign(form_state_edge_device, {
			controName: record.controName,
			controCode: record.controCode,
			gridTableName: record.gridTableName
		});
	};

	// 删除边缘控制器的条目
	const deleteDeviceListItem = () => {
		let record = delete_edge_mess.value;
		initTableRow();
		let params = record.id;

		deleteEdgeDeviceHttp(params)
			.then((data) => {
				if (data) {
					message.success('删除成功！');
					// 关闭表格
					expandedRowKeys.value = [];
					device_element_data.value = [];
					edge_device_id.value = null;
				}
			})
			.then(() => {
				getEageDeviceList();
			})
			.catch((err) => {
				message.error(err.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};
	const deleteModal = ref();
	const delete_edge_mess = ref();
	const showDeleteEdge = (record) => {
		delete_edge_mess.value = record;
		deleteModal.value.showHideDeleteModal();
	};

	// deleteModal.value.endDeleteModal();
	// deleteModal.value.endDeleteLoading()
	// ***********************************************************************************************************
	//                                                   元素类型                                                //
	// ***********************************************************************************************************

	// ------------------------------------------------基本元素信息------------------------------------------------
	const device_element_open = ref(false); // 抽屉开关
	const drawer_device_element_title = ref(''); // 抽屉title
	// 元素类型table表头
	const device_element_columns = ref([
		{ title: '', dataIndex: '', key: '' },
		{
			title: '元素',
			dataIndex: 'elementName',
			key: 'elementName',
			// sorter: (a, b) => a.sort - b.sort,
			width: '14.5%'
			// width: '15%'
		},
		{
			title: '容量',
			dataIndex: 'capacity_show',
			key: 'capacity_show',
			width: '14.5%'
			// width: '15%'
		},
		{
			title: '关联设备数',
			dataIndex: 'deviceNum',
			key: 'deviceNum',
			width: '14.4%'
			// width: '15%'
		},
		{
			title: '操作',
			dataIndex: 'control',
			key: 'control',
			width: '28.3%'
			// width: '30%'
		}
	]);
	// 元素类型的table数据
	const device_element_data = ref([]);

	// 添加元素类型的form
	const form_state_device_element = ref();

	// 关闭元素抽屉的处理数据函数
	const elmentDrawerClose = () => {
		// 清空校验规则，并清空表单数据
		form_state_device_element.value.clearValidate();
		form_state_device_element.value.resetFields();
		// 置空编辑选择的元素类型id
		element_id.value = null;
	};
	// 元素抽屉点击取消
	const cancelElement = () => {
		// 调用 关闭后处理函数
		elmentDrawerClose();
		drawer_device_element_title.value = '';
		device_element_open.value = false;
	};

	// 元素类型的字典下拉数组
	const device_element_list = ref([]);

	// 被选中的元素类型的sort  需要再请求保存或者编辑时提供
	const element_sort = ref(null);
	// 筛选获得被选中的元素的sort
	const handleElementSort = (mess) => {
		if (!mess) {
			element_sort.value = null;
			return;
		}
		for (let index = 0; index < device_element_list.value.length; index++) {
			if (mess == device_element_list.value[index].value) {
				element_sort.value = device_element_list.value[index].sort;
			}
		}
	};

	// 选择元素类型后 需要重置 容量数据
	const elementCodeSelect = (value) => {
		form_state_element_device.capacity = null;
		form_state_element_device.capacityTwo = null;

		// 并更新选中元素的sort
		handleElementSort(value);
	};
	// 设备元素的formState
	const form_state_element_device = reactive({
		id: null,
		controCode: null,
		elementCode: null,
		capacity: null,
		capacityTwo: null
	});

	// 点击新增或者编辑后的抽屉按钮状态切换
	const element_loading = ref(false);

	// ------------------------------------------------新增元素信息------------------------------------------------
	// 打开新增元素类型的抽屉
	const openElementDrawer = (record, column_key) => {
		if (record.detailList.length == 5) {
			message.warning('所有元素类型已经添加完毕，无需再增加！');
			return;
		}
		device_element_list.value.forEach((item1) => {
			item1.status = 1;
			record.detailList.forEach((item) => {
				if (item1.value == item.elementCode) {
					item1.status = 0;
				}
			});
		});
		edge_device_id.value = record.id;
		// 清空元素表单
		Object.assign(form_state_element_device, {
			controCode: record.controCode, // 边端控制器ID需要携带
			id: null,
			elementCode: null,
			capacity: null,
			capacityTwo: null
		});
		drawer_device_element_title.value = '新增元素';
		// 打开抽屉
		device_element_open.value = true;
		//
		initTableRow();
	};

	// 新增或者编辑 设备元素类型
	const addEditElement = () => {
		// 验证表单的数据
		element_loading.value = true;

		form_state_device_element.value
			.validateFields()
			.then((data) => {
				let temp_data = [];
				switch (data.elementCode) {
					case 'GF':
						temp_data.push({
							// 光伏容量
							capacity: Number(data.capacity),
							type: 1,
							unit: 'kWp'
						});
						break;
					case 'CN':
						temp_data.push(
							{
								// 功率
								capacity: Number(data.capacity),
								type: 2,
								unit: 'kW'
							},
							{
								// 功容量
								capacity: Number(data.capacityTwo),
								type: 3,
								unit: 'kWh'
							}
						);
						break;
					case 'CDZ':
						temp_data.push({
							//充电桩容量
							capacity: Number(data.capacity),
							type: 4,
							unit: 'kWh'
						});
						break;
					default:
						temp_data = [];
				}

				// 判断是编辑还是新增

				if (element_id.value) {
					// 编辑
					let params = {
						id: element_id.value,
						elementCode: data.elementCode,
						paramList: temp_data,
						sort: Number(element_sort.value) // 元素类型的顺序
					};

					element_loading.value = false;
					editElementHttp(params)
						.then(() => {
							message.success('保存成功！');
							// 重新请求页面
							getEageDeviceList();
							element_loading.value = false;
							device_element_open.value = false;

							// 更新下拉表的数据  后续需要优化
							// let temp_index = expandedRowKeys.value;
							expandedRowKeys.value = [];
							// expandedRowKeys.value = temp_index
						})
						.catch((error) => {
							element_loading.value = false;
							message.error(error.message);
							setTimeout(() => {
								message.destroy();
							}, 1500);
						});
				} else {
					// 新增
					let params = {
						controId: edge_device_id.value,
						elementCode: data.elementCode,
						paramList: temp_data,
						sort: Number(element_sort.value) // 元素类型的顺序
					};

					addElementHttp(params)
						.then(() => {
							message.success('保存成功！');
							getEageDeviceList();
							// 保存后清空使用过的数据
							element_loading.value = false;
							device_element_open.value = false;
							edge_device_id.value = null;

							expandedRowKeys.value = [];
						})
						.catch((error) => {
							element_loading.value = false;
							message.error(error.message);
							setTimeout(() => {
								message.destroy();
							}, 1500);
						});
				}
			})
			.catch((error) => {
				element_loading.value = false;
			});
	};

	// ------------------------------------------------编辑元素类型------------------------------------------------
	// 编辑元素的信息
	const element_id = ref(null);
	const editElementItem = (record, column) => {
		initTableRow();
		element_id.value = record.id;
		element_sort.value = record.sort;
		Object.assign(form_state_element_device, {
			controCode: record.controCode,
			elementCode: record.elementCode,
			capacity: record.capacity,
			capacityTwo: record.capacityTwo
		});

		device_element_open.value = true;
	};

	// ------------------------------------------------删除元素类型------------------------------------------------

	// 删除元素类型
	const deleteElementItem = (record) => {
		initTableRow();
		let params = record.id;
		deleteElementHttp(params)
			.then(() => {
				message.success('删除成功！');
				// 处理表格的更新
				getEageDeviceList();
				// 保存后清空使用过的数据
				edge_device_id.value = null;
				expandedRowKeys.value = [];
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};
	const deleteModal_element = ref();
	const showDeleteElement = () => {
		deleteModal_element.value.showHideDeleteModal();
	};
	const deleteElement = (record) => {
		deleteElementItem(record);
	};
	// **********************************************************************************************
	//                                          元素类型                                            //
	// **********************************************************************************************

	// **********************************************************************************************
	//                                          关联设备                                            //
	// **********************************************************************************************

	// ------------------------------------------------------基本信息

	// 增加关联设备的具体设备的函数

	const connect_device_open = ref(false); // 关联设备的抽屉开关
	const connect_device_title = ref('新增关联设备');
	// 打开抽屉
	// 根据 元素设备code查询 元素的可选设备类型
	const element_device_type_list = ref([]);
	// watch(
	//   () => connect_device_formstate.elementCode,
	//   (newVal) => {
	//     let params = newVal
	//     getDictHttp(params).then((data) => {
	//       let temp_data = []
	//       data.forEach((item) => {
	//         let { dictKey: value, dictValue: label } = item
	//         temp_data.push({
	//           value,
	//           label
	//         })
	//       })
	//       element_device_type_list.value = temp_data
	//     })
	//   },
	//   { deep: true }
	// )

	const addConnectDevice = (record, column_key) => {
		initTableRow();

		connect_device_title.value = `新增${record.elementName}关联设备`;
		let { controCode, elementName, elementCode, capacity, capacityTwo, controId } = record;

		// 初始赋值
		connect_device_formstate.capacity = capacity;
		connect_device_formstate.capacityTwo = capacityTwo;
		connect_device_formstate.controCode = controCode;
		connect_device_formstate.elementName = elementName;
		connect_device_formstate.elementCode = elementCode;

		// 查询关联设备的列表 并处理数据
		let params = {
			controId: edge_device_id.value,
			elementCode: elementCode,
			pageIndex: 1,
			pageSize: 500,
			projectId: project_id.value
		};
		let params1 = { controId: controId, elementCode: elementCode };

		Promise.all([
			findDevicePageHttp(params), // 功率曲线
			getDeviceTypeRelAssetSimpleList(params1)
		])
			.then(([data1, data2]) => {
				if (data1.length > 0) {
					// 有数据表示处理数据
					editTableData(data1);
				} else {
					connect_device_table.value = [];
					edit_table_data.value = null;
					// Object.assign(edit_table_data,null)
					connect_device_open.value = true;
				}

				let temp_data = [];
				data2.forEach((item) => {
					let { dictKey: value, dictValue: label } = item;
					temp_data.push({ value, label });
				});
				if (temp_data.length == 0) {
					message.error('对应设备类型需要在控制器编辑页面进行维护');
				}

				element_device_type_list.value = temp_data;
			})
			.catch((error) => {
				message.error(error.message);
			});
	};

	// 点击抽屉取消按钮
	const cancleConnectDevice = () => {
		connect_device_open.value = false;
	};

	// 关联设备的form表单
	const connect_device_formstate = reactive({
		controCode: null,
		elementName: null,
		capacity: null,
		capacityTwo: null
	});

	const columns_connect_device = [
		{ title: '序号', dataIndex: 'no', key: 'no', align: 'center' },
		{ title: '设备名称', dataIndex: 'deviceName', key: 'deviceName' },
		{ title: '类型', dataIndex: 'deviceType', key: 'deviceType' },
		{ title: '属性', dataIndex: 'electType', key: 'electType' },
		{ title: '平台设备编号', key: 'deviceCode', dataIndex: 'deviceCode', width: 150 },
		{ title: '电压变比', key: 'voltageRatio', dataIndex: 'voltageRatio', width: 120 },
		{ title: '电流变比', key: 'currentRatio', dataIndex: 'currentRatio', width: 120 },
		{
			// 用来
			title: '',
			key: 'control',
			dataIndex: 'control',
			width: 100
		}
	];

	// 元素对应的关联设备的列表数据
	const connect_device_table = ref([
		// {
		//   no: 1,
		//   id: null,
		//   key: '1',
		//   deviceName: null,
		//   deviceType: null,
		//   electType: null,
		//   deviceCode: null,
		//   voltageRatio: 1,
		//   currentRatio: 1,
		//   control: true
		// }
	]);

	const editTableFormRef = ref();
	const edit_table_data = ref(null);

	// {
	// 1: {
	//   no: 1,
	//   id: null,
	//   key: '1',
	//   deviceName: null,
	//   deviceType: null,
	//   electType: null,
	//   deviceCode: null,
	//   voltageRatio: 1,
	//   currentRatio: 1,
	//   control: true
	// }
	// }

	const selectDeviceType = (value, option) => {
		// 选择表计后 重置数据
		value.electType = null;

		if (option.value != 'METER') {
			value.currentRatio = '';
			value.voltageRatio = '';
		} else {
			value.currentRatio = 1;
			value.voltageRatio = 1;
		}
	};

	// 元素的设备类型被选中后的函数 用以 查询关联类型的属性列表
	const device_key_list = ref([]);
	const getElectType = () => {
		device_key_list.value = [];
		let params = 'METER';
		getDictHttp(params)
			.then((data) => {
				let temp_data = [];
				data.forEach((item) => {
					temp_data.push({ label: item.dictValue, value: item.dictKey });
				});
				device_key_list.value = temp_data;
			})
			.catch((error) => {});
	};
	getElectType();
	const elementDeviceTypeSelect = (value) => {
		if (judgeIsNeedElectType(value)) {
			// 需要设备属性
			device_key_list.value = [];
			getDictHttp(value)
				.then((data) => {
					let temp_data = [];
					data.forEach((item) => {
						temp_data.push({ label: item.dictValue, value: item.dictKey });
					});
					device_key_list.value = temp_data;
				})
				.catch((error) => {});
		} else {
			// 不需要设备属性
		}
	};

	// 判断是否需要设备属性的函数
	const judgeIsNeedElectType = (value) => {
		let flag = false;
		switch (value) {
			case 'METER':
				flag = true;
				break;
			default:
				flag = false;
				break;
		}

		return flag;
	};

	// 提交关联设备的列表
	//  提交设备类型新增抽屉的所有数据
	const up_connect_device_loading = ref(false);
	const upLoadElementContactDeviceList = () => {
		// 处理设备列表的数据

		up_connect_device_loading.value = true;
		let result = validateEditForm();
		up_connect_device_loading.value = false;
		if (result) {
			// 校验成功
			let temp_list = [];
			for (const key in edit_table_data.value) {
				let { currentRatio, deviceCode, deviceName, deviceType, electType, id, voltageRatio } =
					edit_table_data.value[key];
				let tem_data = {
					deviceCode,
					deviceName,
					deviceType,
					electType: electType ? Number(electType) : electType
				};
				id ? (tem_data.id = id) : null;
				currentRatio ? (tem_data.currentRatio = currentRatio) : null;
				voltageRatio ? (tem_data.voltageRatio = voltageRatio) : null;
				temp_list.push(tem_data);
			}

			let params = {
				controId: edge_device_id.value, //边端控制器id edge_device_id
				deviceList: temp_list,
				elementCode: connect_device_formstate.elementCode, // 元素类型code
				projectId: project_id.value
			};
			elementContactDeviceHttp(params)
				.then((data) => {
					if (data) {
						let params_dode = { controCode: edge_contro_code.value };
						cacheDevices(params_dode);
						message.success('保存成功！');
						// 处理表格的更新
						getEageDeviceList();
						up_connect_device_loading.value = false;
						connect_device_open.value = false;
						expandedRowKeys.value = [];
					}
				})
				.catch((error) => {
					message.error(error.message);
					setTimeout(() => {
						message.destroy();
					}, 1500);
					up_connect_device_loading.value = false;
				});
		} else {
			// 校验失败
			up_connect_device_loading.value = false;
		}
	};

	// 对表格进行提交前的校验

	const validateEditForm = () => {
		let validate_flag = true;

		if (!edit_table_data.value || Object.keys(edit_table_data.value).length == 0) {
			return true;
		}
		for (const key in edit_table_data.value) {
			let break_flag = false; // 是否结束第一层循环的标志
			for (const key_second in edit_table_data.value[key]) {
				// 校验规则
				// 1. 属性和电压电流变比
				// if(key_second){

				// }

				// if (
				//   !edit_table_data.value[key][key_second] &&
				//   key_second != 'id' &&
				//   key_second != 'control'
				// ) {
				//   validate_flag = false
				//   break_flag = true
				//   message.error('数据不能为空！')
				//   break
				// }
				// 2. 设备名称校验  数字英文汉字  deviceName
				if (key_second == 'deviceName') {
					if (
						!elementConnectDeviceName(edit_table_data.value[key][key_second]) ||
						!edit_table_data.value[key][key_second]
					) {
						// 为空或者是校验不通过
						validate_flag = false;
						break_flag = true;
						message.error('设备名称为非空的数字英文下划线与汉字组合');
						break;
					}
				}
				// 3. 类型校验 不为空省略  deviceType
				if (key_second == 'deviceType') {
					if (!edit_table_data.value[key][key_second]) {
						validate_flag = false;
						break_flag = true;
						message.error('设备类型不能为空');
						break;
					}
				}
				// 4. 属性校验 不为空省略 electType
				if (key_second == 'electType') {
					// 表计的校验和其他的校验不同  表计需要全校验  其他的不需要校验
					if (edit_table_data.value[key]['deviceType'] == 'METER') {
						// 表计
						if (!edit_table_data.value[key][key_second]) {
							// 数据为空
							validate_flag = false;
							break_flag = true;
							message.error('设备属性不能为空');
							setTimeout(() => {
								message.destroy();
							}, 1500);
							break;
						}
					}
				}
				// 5. 控制器设备编码校验  deviceCode
				if (key_second == 'deviceCode') {
					////////console.log(edit_table_data.value[key][key_second])
					//////////console.log(elementConnectDeviceCode(edit_table_data.value[key][key_second]));
					if (
						!elementConnectDeviceCode(edit_table_data.value[key][key_second]) ||
						!edit_table_data.value[key][key_second]
					) {
						validate_flag = false;
						break_flag = true;
						message.error('平台设备编号为非空的英文数字下划线横线组合，最多不超过64位');
						setTimeout(() => {
							message.destroy();
						}, 1500);
						break;
					}
				}
				// 6. 电压和电流变比的校验 为数字  voltageRatio  currentRatio
				if (key_second == 'voltageRatio' || key_second == 'currentRatio') {
					if (edit_table_data.value[key]['deviceType'] == 'METER') {
						// 需要校验
						if (!isNumber(edit_table_data.value[key][key_second])) {
							validate_flag = false;
							break_flag = true;
							message.error('电压变比和电流变比必须为数字');
							setTimeout(() => {
								message.destroy();
							}, 1500);
							break;
						}
					}
				}
			}
			if (break_flag) {
				break;
			}
		}
		return validate_flag;
	};

	const editTableData = (data) => {
		let temp_data = [];
		let temp_data_obj = {};
		for (let index = 0; index < data.length; index++) {
			let {
				id,
				// key_second,
				deviceName,
				deviceType,
				electType,
				deviceCode,
				voltageRatio,
				currentRatio
			} = data[index];

			electType == -1 ? (electType = null) : null;
			temp_data.push({
				no: index + 1,
				key: String(index + 1),
				id,
				// key_second,
				deviceName,
				deviceType,
				electType: electType ? String(electType) : electType,
				deviceCode,
				voltageRatio,
				currentRatio,
				control: false
			});
			temp_data_obj[index + 1] = {
				no: index + 1,
				key: String(index + 1),
				id,
				// key_second,
				deviceName,
				deviceType,
				electType: electType ? String(electType) : electType,
				deviceCode,
				voltageRatio,
				currentRatio,
				control: false
			};
			if (index == data.length - 1) {
				temp_data[index].control = true;
				temp_data_obj[index + 1].control = true;
			}
		}
		console.log(temp_data_obj);

		edit_table_data.value = temp_data_obj;
		connect_device_table.value = temp_data;

		connect_device_open.value = true;
		setTimeout(() => {
			console.log('111111');
			console.log(editTableFormRef.value);
			editTableFormRef.value.clearValidate('deviceName');
			console.log('222222');
		}, 3000);
	};

	// 编辑表格的处理数据
	const handle_edit_table_data = (line_key) => {
		//  console.log(line_key)
		let temp_edit = [];
		// console.log(edit_table_data)
		let length = 0;
		if (edit_table_data.value) {
			length = Object.keys(edit_table_data.value).length;
			// 处理已有数据 不管新增还是删除
			for (const key in edit_table_data.value) {
				let item = edit_table_data.value[key];
				temp_edit.push({
					no: item.no,
					id: item.id,
					key: item.key,
					deviceName: item.deviceName,
					deviceType: item.deviceType,
					electType: item.electType,
					deviceCode: item.deviceCode,
					voltageRatio: item.voltageRatio,
					currentRatio: item.currentRatio,
					control: item.control
				});
			}
			temp_edit.sort((a, b) => a.key - b.key);
		}

		if (line_key) {
			// 表示删除
			let place_idnex = Number(line_key) - 1;
			temp_edit.splice(place_idnex, 1);
		} else {
			// 表示新增
			//此时新增一个
			temp_edit.push({
				no: length + 1,
				id: null,
				key: null,
				deviceName: null,
				deviceType: null,
				deviceCode: null,
				electType: null,
				voltageRatio: 1,
				currentRatio: 1,
				control: true
			});
		}

		// //////////console.log(current_data)
		let temp_edit_list = {};
		// 重置key值   并重置最新edit_table数据
		// if (temp_edit.length > 0) {
		for (let index = 0; index < temp_edit.length; index++) {
			let key = String(index + 1);
			temp_edit[index].key = key;
			temp_edit[index].control = false;
			if (index == temp_edit.length - 1) {
				temp_edit[index].control = true;
			}
			temp_edit_list[key] = {
				no: index + 1,
				id: temp_edit[index].id,
				key: key,
				deviceName: temp_edit[index].deviceName,
				deviceType: temp_edit[index].deviceType,
				electType: temp_edit[index].electType,
				deviceCode: temp_edit[index].deviceCode,
				voltageRatio: temp_edit[index].voltageRatio,
				currentRatio: temp_edit[index].currentRatio,
				control: temp_edit[index].control
			};
		}
		// }

		return { table: temp_edit, table_edit: temp_edit_list };
	};

	// 关联设备增加一个可编辑行
	const addTableLine = () => {
		let result = handle_edit_table_data();
		edit_table_data.value = result.table_edit;
		connect_device_table.value = result.table;
	};

	// 关联设备删除一行
	const deleteTableLine = (line_key) => {
		let result = handle_edit_table_data(line_key);
		edit_table_data.value = result.table_edit;
		connect_device_table.value = result.table;
	};

	// **********************************************************************************************
	//                                          关联设备                                            //
	// **********************************************************************************************

	const addParentClassName = () => {
		//////console.log(111111111111111)
		var childElements = document.getElementsByClassName('unique_table');
		//////console.log(childElements)
		// 遍历这些子元素
		for (var i = 0; i < childElements.length; i++) {
			// 获取当前子元素的父元素
			var parentElement = childElements[i].parentNode; // 注意：在旧版IE中可能是parentElement.parentElement

			// 检查父元素是否已经有了这个类名（可选）
			if (!parentElement.classList.contains('unique_table_parent')) {
				// 给父元素添加类名
				parentElement.classList.add('unique_table_parent');
			}
		}
	};

	onMounted(() => {
		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			//////////console.log(project_mess)
			project_id.value = project_mess.project_id;
			edge_params.projectId = project_id.value;
		} else {
			message.error('未选择项目，请重新选择！');
			setTimeout(() => {
				message.destroy();
			}, 1500);
		}
		getEageDeviceList();
		// 根据字典查询
		let params = 'ElEMENT_TYPE';
		getDictHttp(params).then((data) => {
			////////console.log('字典')
			////////console.log(data)
			let result = [];
			// sort根据顺序添加与后台协商结果
			for (let index = 0; index < data.length; index++) {
				result.push({
					label: data[index].dictValue,
					value: data[index].dictKey,
					sort: index + 1,
					status: 1
				});
			}
			device_element_list.value = result;
		});
		// 根据字典查询边端控制器设备列表筛选
		getDevicTypeList();
	});
</script>
<style lang="less" scoped>
	.current_page {
		padding-top: 32px;
		box-sizing: border-box;
	}
	.page_header_title {
		margin-bottom: 30px;
	}

	.footer_button {
		position: fixed;
		background-color: rgba(255, 255, 255, 1);
		border-top: 1px solid rgba(221, 226, 237, 1);
		bottom: 31px;
		left: 50%;
		transform: translate(-50%, 0%);
		width: calc(100% - 64px);
		z-index: 999;
		height: 8.9vh;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		border-bottom-left-radius: 8px;
		border-bottom-right-radius: 8px;
	}
</style>

<style>
	.form_table_edit {
		padding: 0px 84px 0 0;
		position: relative;

		.add_delete_table {
			/* padding-top: 58.14px; */
			height: 100%;
			width: 100%;
			/* position: absolute;
    right: 0;
    top: 0; */
			width: 84px;
			.add_delete_table_item {
				padding: 0 0 0 16px;
				height: 36px;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
			}
		}
	}
	.form_table_edit1 {
		padding: 0px 84px 0 0;
		position: relative;

		.add_delete_table {
			/* padding-top: 58.14px; */
			height: 100%;
			width: 100%;
			/* position: absolute;
    right: 0;
    top: 0; */
			width: 84px;
			.add_delete_table_item {
				padding: 0 0 0 16px;
				height: 36px;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
			}
		}
		.add_delete_button {
			width: 26px;
			height: 26px;
			border-radius: 50%;
			cursor: pointer;
		}
	}
	.add_delete_button {
		width: 26px;
		height: 26px;
		border-radius: 50%;
		cursor: pointer;
	}
	.add_table {
		background-image: url('/assets/icon/rule_add.png');
		background-repeat: no-repeat;
		background-size: 100%;
		&:hover {
			background-image: url('/assets/icon/rule_add_hover.png');
		}
	}
	.delete_table {
		background-image: url('/assets/icon/rule_delete.png');
		background-repeat: no-repeat;
		background-size: 100%;
		&:hover {
			background-image: url('/assets/icon/rule_delete_hover.png');
		}
	}
	.form_self_normal {
		.ant_edit_table_last_cloumn {
			.ant-table-cell {
				height: 68px !important;
				.ant-form {
					height: 68px !important;
					/* display: flex;
        flex-direction: column;
        justify-content: center; */
					.ant-form-item {
						height: 68px !important;
						margin-bottom: 0 !important;
						box-sizing: border-box !important;
						padding-top: 20px !important;
					}
				}
				&:last-child {
					.ant-form-item {
						/* padding-top: 10px !important; */
					}
				}
			}
		}
	}
	.form_table_edit_item {
		margin-bottom: 10px;
	}
</style>
<style>
	:deep(.ant-tooltip) {
		width: unset;
	}
	:deep(.ant-tooltip-content) {
		width: unset;
	}
</style>
