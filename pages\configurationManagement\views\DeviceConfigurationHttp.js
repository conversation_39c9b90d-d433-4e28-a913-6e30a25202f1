import http from '/tool/http.js'


// 根据项目ID获取控制器列表
export const getControlDeviceList = (params) => {
    let url = "/api/edge/getEdgeInfo";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200 && data.dataList.length > 0) {
                // console.log(data.dataList)
                resolve(data.dataList)
            } else {
                // 空数据
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}

// 新增边缘控制器接口 编辑和新增只有参数的区别
export  const addNewEdgeControlDevice = (params)=>{
    let url = "/api/edge/saveEdge";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200) {
                resolve(data.success)
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}

// 删除边缘控制器
export  const deleteEdgeControlDevice = (params)=>{
    let url = "/api/edge/delEdge";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200) {
                resolve(data.success)
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}

// 获取控制器详情信息 根据设备类型查询控制器基本信息
export  const getControlConnectDeviceDetail = (params)=>{
    let url = "/api/edge/getEdgeDeail";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200) {
                resolve(data.data)
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}

// 获取控制器详情信息 根据设备类型查询控制器基本信息
export  const getControlConnectDeviceList = (params)=>{
    let url = "/api/edge/getEdgeDeail";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200) {
                resolve(data.data)
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}


// 新增控制器关联的设备的基本信息 
export  const addControlConnectDevice = (params)=>{
    let url = "/api/edge/saveEdgeDev";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200) {
                resolve(data.success)
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}