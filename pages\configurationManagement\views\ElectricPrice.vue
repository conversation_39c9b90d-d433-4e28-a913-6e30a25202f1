<template>
	<div class="page_all current_page">
		<div class="third_menu_contain">
			<div
				:class="['third_menu_item', currentThirdMenu == index ? 'active' : '']"
				@click="menuChange(item, index)"
				v-for="(item, index) in third_menu_list"
				:key="index"
			>
				<span class="third_menu_name">{{ item.name }}</span>
				<span class="third_menu_line"></span>
			</div>
		</div>
		<div class="main_contain main_contain_thirdMenu">
			<div class="main_contain_scroll">
				<a-row>
					<a-col :span="12">
						<a-form
							id="time_select"
							class="layout_row"
						>
							<a-form-item
								label="时间选择"
								name="time_select"
							>
								<a-date-picker
									:allowClear="false"
									style="width: 440px"
									v-model:value="time_year"
									picker="year"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/clock.png"
											alt="#"
										/>
									</template>
								</a-date-picker>
							</a-form-item>
						</a-form>
					</a-col>
					<a-col
						:span="12"
						style="
							display: flex;
							flex-direction: row;
							justify-content: flex-end;
							align-items: center;
						"
					>
						<a-button
							@click="drawer_sync_pro = true"
							style="
								margin-right: 32px;
								display: flex;
								flex-direction: row;
								justify-content: center;
							"
							:loading="sync_pro_status"
						>
							<div class="icon_button">
								<div
									v-if="!sync_pro_status"
									class="icon_img_refresh"
								></div>
								<span>同步其他项目</span>
							</div>
						</a-button>
						<a-button
							@click="showDeleteModal"
							style="
								margin-right: 32px;
								display: flex;
								flex-direction: row;
								justify-content: center;
							"
							:loading="sync_time_status"
						>
							<div class="icon_button">
								<!-- <img v-if="!sync_time_status" src="/assets/icon/tb_time.png" alt="#" /> -->
								<div
									v-if="!sync_time_status"
									class="icon_img_refresh"
								></div>
								<span>同步去年时段</span>
							</div>
						</a-button>
						<a-button
							type="primary"
							@click="allYearConfig"
							:loading="all_year_config"
						>
							<div class="icon_button">
								<img
									src="/assets/icon/set_active.png"
									alt="#"
								/>
								<span>全年时段配置</span>
							</div>
						</a-button>
						<DeleteModal
							ref="sync_time_modal"
							:tips="'是否进行操作将去年的分时时段信息同步到今年时段？'"
							@deleteConfirm="syncTime"
						></DeleteModal>
					</a-col>
				</a-row>

				<div class="month_list_contain">
					<div class="month_list">
						<div
							class="month_item"
							v-for="(item, index) in month_list"
							:key="index"
						>
							<div class="month_title">
								<div>{{ item.month }}月</div>
								<div
									class="normal_tag"
									@click="openTime(item)"
								>
									时段
								</div>
								<a-tooltip
									:title="item.tips_title"
									:color="item.color"
								>
									<div
										:class="['price_tips', item.month_status]"
										@click="priceChange(item)"
									>
										<img
											v-if="item.month_status == 'error_tag' ? true : false"
											style="height: 16px; width: 16px; line-height: 16px"
											src="/assets/icon/price_tips_error.png"
											alt=""
										/>
										<img
											v-if="item.month_status == 'warning_tag' ? true : false"
											style="height: 16px; width: 16px; line-height: 16px"
											src="/assets/icon/price_tips_warning.png"
											alt=""
										/>
										价格
									</div>
								</a-tooltip>
							</div>
							<div class="detail_month">
								<div class="month_time_list">
									<a-tooltip
										placement="right"
										v-for="(item1, index1) in item.month_list_detail"
										:key="index1"
									>
										<template #title>
											<span>{{ item1.mess }}</span>
										</template>
										<div
											:class="['month_time_list_item', 'rateType' + item1.rateType]"
											:style="{ width: (item1.line_length / item.month_length) * 100 + '%' }"
										>
											{{ item1.mess }}
										</div>
									</a-tooltip>
								</div>
							</div>
						</div>
					</div>
				</div>

				<a-spin
					size="large"
					:class="['full_page_loading', has_data ? 'ull_page_has_data' : '']"
				>
					<template #indicator>
						<img
							style="height: 100px; width: 100px"
							src="/assets/icon/loading-big.gif"
							alt=""
						/>
					</template>
				</a-spin>
			</div>
		</div>
		<!-- 同步其他项目的抽屉 -->
		<a-drawer
			v-model:open="drawer_sync_pro"
			:closable="true"
			class="custom-class"
			root-class-name="root-class-name"
			title="同步其他项目"
			:getContainer="false"
			placement="right"
			width="40%"
			@close="syncProClose"
		>
			<a-form
				id="form_sync_pro_ref"
				ref="form_sync_pro_ref"
				:model="form_sync_pro"
				name="form_sync_pro"
				:label-col="{ span: 24 }"
				:wrapper-col="{ span: 24 }"
				autocomplete="off"
				labelAlign="left"
				layout="horizontal"
			>
				<a-form-item
					label="项目选择"
					name="copyProjectId"
					:rules="[
						{
							required: true,
							message: '请选择项目'
						}
					]"
				>
					<a-select
						v-model:value="form_sync_pro.copyProjectId"
						placeholder="请选择项目"
						style="width: 100%"
					>
						<a-select-option
							v-for="(item, index) in project_list"
							:key="index"
							:value="item.projectId"
						>
							{{ item.projectName }}
						</a-select-option>
					</a-select>
				</a-form-item>
				<a-form-item
					label="选择月份"
					name="copyMonth"
					:rules="[
						{
							required: true,
							message: '请选择月份'
						}
					]"
				>
					<a-select
						v-model:value="form_sync_pro.copyMonth"
						placeholder="请选择月份"
						style="width: 100%"
					>
						<a-select-option
							v-for="(item, index) in month_options"
							:key="index"
							:value="item.value"
						>
							{{ item.label }}
						</a-select-option>
					</a-select>
				</a-form-item>
			</a-form>

			<template #footer>
				<a-space
					class="drawer_footer"
					align="center"
				>
					<a-button @click="cancelSyncProDrawer">取消</a-button>
					<a-button
						:loading="sync_pro_loading"
						@click="syncOtherProject"
						type="primary"
					>
						确定
					</a-button>
				</a-space>
			</template>
		</a-drawer>

		<!-- 价格配置的抽屉 -->
		<a-drawer
			v-model:open="drawer_price"
			:closable="true"
			class="custom-class"
			root-class-name="root-class-name"
			:title="price_title"
			:getContainer="false"
			placement="right"
			width="40%"
			@close="priceClose"
		>
			<a-form
				id="form_state_price_ref"
				ref="form_state_price_ref"
				:model="form_state_price"
				name="form_state_price"
				:label-col="{ span: 24 }"
				:wrapper-col="{ span: 24 }"
				autocomplete="off"
				labelAlign="left"
				layout="horizontal"
			>
				<a-form-item
					v-for="(item, index) in price_form_list.filter((item) => {
						const dictKey = parseInt(item.dictKey);
						return dictKey >= 1 && dictKey <= 4;
					})"
					:key="index"
					:label="item.dictValue"
					:name="item.dictKey"
					:rules="[
						{
							required: true,
							validator: (rule, value) => {
								return elePriceForm(value);
							},
							message: '请输入小数点后六位以内的数字'
						}
					]"
				>
					<a-input
						autocomplete="off"
						v-model:value="form_state_price[item.dictKey]"
					/>
				</a-form-item>
				<a-form-item
					v-for="(item, index) in price_form_list.filter((item) => {
						const dictKey = parseInt(item.dictKey);
						return dictKey == 12;
					})"
					:key="index"
					:label="item.dictValue"
					:name="item.dictKey"
					:rules="[
						{
							required: false,
							validator: async (rule, value) => {
								if (value && value.trim() !== '' && value !== null) {
									return elePriceForm(value);
								}
								return;
							},
							message: '请输入小数点后六位以内的数字'
						}
					]"
				>
					<a-input
						autocomplete="off"
						v-model:value="form_state_price[item.dictKey]"
					/>
				</a-form-item>
				<a-form-item
					v-for="(item, index) in price_form_list.filter((item) => {
						const dictKey = parseInt(item.dictKey);
						return dictKey >= 5 && dictKey <= 11;
					})"
					:key="index"
					:label="item.dictValue"
					:name="item.dictKey"
					:rules="[
						{
							required: true,
							validator: (rule, value) => {
								return elePriceForm(value);
							},
							message: '请输入小数点后六位以内的数字'
						}
					]"
				>
					<a-input
						autocomplete="off"
						v-model:value="form_state_price[item.dictKey]"
					/>
				</a-form-item>
				<a-form-item
					v-for="(item, index) in price_form_list.filter((item) => {
						const dictKey = parseInt(item.dictKey);
						return dictKey == 13;
					})"
					:key="index"
					:label="item.dictValue"
					:name="item.dictKey"
					:rules="[
						{
							required: true,
							validator: (rule, value) => {
								return eleDemandMaxForm(value);
							},
							message: '请输入小数点后2位以内的数字'
						}
					]"
				>
					<a-input
						autocomplete="off"
						v-model:value="form_state_price[item.dictKey]"
					/>
				</a-form-item>
			</a-form>

			<template #footer>
				<a-space
					class="drawer_footer"
					align="center"
				>
					<a-button @click="cancelPriceDrawer">取消</a-button>
					<a-button
						:loading="upLoad_price_loading"
						@click="upLoadPrice"
						type="primary"
					>
						确定
					</a-button>
				</a-space>
			</template>
		</a-drawer>

		<!-- 时段配置的抽屉 -->
		<a-drawer
			v-model:open="drawer_time"
			class="custom-class"
			root-class-name="root-class-name"
			:title="price_time"
			:getContainer="false"
			:closable="true"
			placement="right"
			width="77%"
			@close="timeClose"
		>
			<a-space
				direction="vertical"
				style="width: 100%; margin-bottom: 40px; margin-top: 10px"
			>
				<a-alert
					message="配置说明：每个分时时段可以点击进行多段时间区间选择；24个小时时间段需要完整，如有缺失或重叠无法保存。"
					type="info"
					show-icon
				>
					<template #icon>
						<img
							style="margin: 0 10px; height: 20px; width: 20px"
							src="/assets/icon/tips_right.png"
							alt=""
						/>
					</template>
				</a-alert>
			</a-space>
			<a-row
				v-if="is_full_year"
				style="
					margin-bottom: 20px;
					display: flex;
					flex-direction: row;
					justify-content: flex-start;
					padding: 0 20px;
				"
			>
				<a-col style="width: 100px">
					<a-checkbox v-model:checked="all_checked">全部</a-checkbox>
				</a-col>
				<a-col style="flex: 1">
					<div
						class="month_checks"
						style="width: 100%"
					>
						<a-checkbox-group
							v-model:value="month_checks"
							style="width: 100%"
						>
							<a-row>
								<a-col
									:span="4"
									v-for="index in 12"
									:key="index"
									style="margin-bottom: 20px"
								>
									<a-checkbox
										:value="index"
										style="float: right; width: 60px"
									>
										{{ index }}月
									</a-checkbox>
								</a-col>
							</a-row>
						</a-checkbox-group>
					</div>
				</a-col>
			</a-row>
			<div class="time_list_contain">
				<div class="time_list_item">
					<div class="time_list_item_title">尖期时段</div>
					<div
						class="time_list_item_24 jian_line"
						line_type="out"
						@mouseleave.stop="mouseleaveLine"
					>
						<a-tooltip
							placement="top"
							v-for="(item, index) in time_allday_jian"
							:key="index"
						>
							<template #title>{{ item.time_start }}-{{ item.time_end }}</template>
							<div
								:class="['time_list_item_24_bar', item.active ? 'jian' : '']"
								line_type="1"
								:index="index"
								:active="item.active"
								:time_start="item.time_start"
								:time_end="item.time_end"
								@click="choiceTime(index, '1', item)"
								@mousedown.stop="mousedownLine"
								@mouseup.stop="mouseupLine"
								@mouseover.stop="mouseoverLine"
							>
								<span
									v-if="Number.isInteger(index / 4) || (index + 1) / 4 == 24"
									:class="['time_number', item.active ? 'active' : '']"
								>
									{{ (index + 1) / 4 == 24 ? 24 : index / 4 }}
								</span>
							</div>
						</a-tooltip>
					</div>
				</div>
				<div class="time_list_item">
					<div class="time_list_item_title">峰期时段</div>
					<div
						class="time_list_item_24 feng_line"
						line_type="out"
						@mouseleave.stop="mouseleaveLine"
					>
						<a-tooltip
							placement="top"
							v-for="(item, index) in time_allday_feng"
							:key="index"
						>
							<template #title>{{ item.time_start }}-{{ item.time_end }}</template>
							<div
								:class="['time_list_item_24_bar', item.active ? 'feng' : '']"
								line_type="2"
								:index="index"
								:active="item.active"
								:time_start="item.time_start"
								:time_end="item.time_end"
								@click="choiceTime(index, '2', item)"
								@mousedown.stop="mousedownLine"
								@mouseup.stop="mouseupLine"
								@mouseover.stop="mouseoverLine"
							>
								<span
									v-if="Number.isInteger(index / 4) || (index + 1) / 4 == 24"
									:class="['time_number', item.active ? 'active' : '']"
								>
									{{ (index + 1) / 4 == 24 ? 24 : index / 4 }}
								</span>
							</div>
						</a-tooltip>
					</div>
				</div>
				<div class="time_list_item">
					<div class="time_list_item_title">平期时段</div>
					<div
						class="time_list_item_24 ping_line"
						line_type="out"
						@mouseleave.stop="mouseleaveLine"
					>
						<a-tooltip
							placement="top"
							v-for="(item, index) in time_allday_ping"
							:key="index"
						>
							<template #title>{{ item.time_start }}-{{ item.time_end }}</template>
							<div
								:class="['time_list_item_24_bar', item.active ? 'ping' : '']"
								line_type="3"
								:index="index"
								:active="item.active"
								:time_start="item.time_start"
								:time_end="item.time_end"
								@click="choiceTime(index, '3', item)"
								@mousedown.stop="mousedownLine"
								@mouseup.stop="mouseupLine"
								@mouseover.stop="mouseoverLine"
							>
								<span
									v-if="Number.isInteger(index / 4) || (index + 1) / 4 == 24"
									:class="['time_number', item.active ? 'active' : '']"
								>
									{{ (index + 1) / 4 == 24 ? 24 : index / 4 }}
								</span>
							</div>
						</a-tooltip>
					</div>
				</div>
				<div class="time_list_item">
					<div class="time_list_item_title">谷期时段</div>
					<div
						class="time_list_item_24 gu_line"
						line_type="out"
						@mouseleave.stop="mouseleaveLine"
					>
						<a-tooltip
							placement="top"
							v-for="(item, index) in time_allday_gu"
							:key="index"
						>
							<template #title>{{ item.time_start }}-{{ item.time_end }}</template>
							<div
								:class="['time_list_item_24_bar', item.active ? 'gu' : '']"
								line_type="4"
								:index="index"
								:active="item.active"
								:time_start="item.time_start"
								:time_end="item.time_end"
								@click="choiceTime(index, '4', item)"
								@mousedown.stop="mousedownLine"
								@mouseup.stop="mouseupLine"
								@mouseover.stop="mouseoverLine"
							>
								<span
									v-if="Number.isInteger(index / 4) || (index + 1) / 4 == 24"
									:class="['time_number', item.active ? 'active' : '']"
								>
									{{ (index + 1) / 4 == 24 ? 24 : index / 4 }}
								</span>
							</div>
						</a-tooltip>
					</div>
				</div>
				<div class="time_list_item">
					<div class="time_list_item_title">深谷期时段</div>
					<div
						class="time_list_item_24 shengu_line"
						line_type="out"
						@mouseleave.stop="mouseleaveLine"
					>
						<a-tooltip
							placement="top"
							v-for="(item, index) in time_allday_shengu"
							:key="index"
						>
							<template #title>{{ item.time_start }}-{{ item.time_end }}</template>
							<div
								:class="['time_list_item_24_bar', item.active ? 'shengu' : '']"
								line_type="5"
								:index="index"
								:active="item.active"
								:time_start="item.time_start"
								:time_end="item.time_end"
								@click="choiceTime(index, '5', item)"
								@mousedown.stop="mousedownLine"
								@mouseup.stop="mouseupLine"
								@mouseover.stop="mouseoverLine"
							>
								<span
									v-if="Number.isInteger(index / 4) || (index + 1) / 4 == 24"
									:class="['time_number', item.active ? 'active' : '']"
								>
									{{ (index + 1) / 4 == 24 ? 24 : index / 4 }}
								</span>
							</div>
						</a-tooltip>
					</div>
				</div>
			</div>
			<template #footer>
				<a-space
					class="drawer_footer"
					align="center"
				>
					<a-button @click="cancelTime">取消</a-button>
					<a-button
						:loading="upLoad_time_loading"
						@click="upLoadTime"
						type="primary"
					>
						确定
					</a-button>
				</a-space>
			</template>
		</a-drawer>
	</div>
</template>
<script setup>
	import { ref, watch, reactive, onMounted } from 'vue';

	import { useRouter } from 'vue-router';
	import { message } from 'ant-design-vue';
	import DeleteModal from '/components/ModalDelete.vue';
	import { getAuthProjects } from '/components/commonApi.js';
	import {
		saveTimeByMonthHttp,
		saveTimeByMonthListHttp,
		priceDetailOnYearHttp,
		saveTimeSyncLastYearHttp,
		getPriceDetailByElerateIdHttp,
		savePriceByMonthHttp,
		syncOtherProjectHttp,
		getTimeDetailByElerateIdHttp
	} from './api.js';
	import { getDictHttp } from '/assets/js/commonApi.js';
	import { elePriceForm, eleDemandMaxForm } from '/assets/js/reg.js';

	import dayjs from 'dayjs';
	import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
	dayjs.extend(isSameOrBefore);
	// ***********************************************************************************************************
	//                                                   全局数据                                                //
	// ***********************************************************************************************************
	// 项目id
	const project_id = ref(null);
	// 时间选择
	const time_year = ref(dayjs());
	watch(time_year, () => {
		getList();
	});

	// ***********************************************************************************************************
	//                                                   全局数据                                                //
	// ***********************************************************************************************************

	// 全局是够有数据
	const has_data = ref(false);

	const router = useRouter();
	// 三级菜单切换
	const third_menu_list = ref([
		{ name: '项目信息', url: '/projectDetail' },
		{ name: '项目设备', url: '/deviceConfiguration' },
		{ name: '电价配置', url: '/electricPrice' },
		{ name: '策略配置', url: '/policyConfiguration' }
	]);

	const currentThirdMenu = ref(2);
	const menuChange = (item, index) => {
		currentThirdMenu.value = index;
		router.push(item.url);
	};

	// ***********************************************************************************************************
	//                                                   月份数据                                                //
	// ***********************************************************************************************************

	const month_list = ref([]);

	// 月份维护  不可编辑状态-在当前月+2的月份后不能进行电价维护，价格按钮置灰状态
	const time_now_month = ref(dayjs().format('M'));
	// console.log(time_now_month.value)

	// ***********************************************************************************************************
	//                                                   月份数据                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                   价格配置                                                //
	// ***********************************************************************************************************
	const drawer_price = ref(false);
	const drawer_sync_pro = ref(false);

	const price_form_list = ref([]);
	const form_state_price_ref = ref();
	const form_sync_pro_ref = ref();
	const form_state_price = reactive({});
	const form_sync_pro = reactive({});
	const project_list = ref([]);
	const month_options = ref([]);

	const getPriceFroList = () => {
		let params = 'ELERATE_PRICE';
		getDictHttp(params)
			.then((data) => {
				if (data) {
					price_form_list.value = data;
					console.log(data, 'data');
				}
				let temp_obj = {};
				data.forEach((item) => {
					temp_obj[item.dictKey] = null;
				});

				Object.assign(form_state_price, temp_obj);
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 2000);
			});
	};
	getPriceFroList();

	const getProjectList = async () => {
		project_list.value = await getAuthProjects();
		// 如果有项目列表，默认选择第一个项目
		if (project_list.value && project_list.value.length > 0) {
			form_sync_pro.copyProjectId = project_list.value[0].projectId;
		}
	};

	const initMonthOptions = () => {
		const currentMonth = dayjs().month() + 1; // 获取当前月份（1-12）
		const options = [];
		for (let i = currentMonth; i <= 12; i++) {
			options.push({
				value: i,
				label: `${i}月`
			});
		}
		month_options.value = options;
		// 默认选择第一个月份
		if (options.length > 0) {
			form_sync_pro.copyMonth = options[0].value;
		}
	};
	getProjectList();
	initMonthOptions();
	const priceChange = (item) => {
		console.log(item.month_status,'item.month_status');
		// 判断是否能够配置
		// /不可编辑状态-在当前月+2的月份后不能进行电价维护，价格按钮置灰状态；
		current_month.value = item.month;
		elerateId.value = item.elerateId;
		let final_time = Number(time_now_month.value) + 2;
		// if (item.month > final_time) {
		// 	message.error('在当前月+2的月份后不能进行电价维护!');
		// 	return;
		// }
		if(item.month_status == 'disabled_tag'){
			message.error('在当前月+2的月份后不能进行电价维护!');
			return;
		}
		if (!drawer_price.value) {
			price_title.value = item.month + '月价格';
		}
		let params = item.elerateId;
		// 需要判断是否需要获取数据进行编辑
		getPriceDetailByElerateIdHttp(params)
			.then((data) => {
				let temp_obj = {};
				if (data.length > 0) {
					for (let index = 0; index < data.length; index++) {
						temp_obj[data[index].rateType] = data[index].price;
					}
					Object.assign(form_state_price, temp_obj);
				} else {
					let temp = JSON.parse(JSON.stringify(form_state_price));
					for (const key in temp) {
						temp[key] = null;
					}
					Object.assign(form_state_price, temp);
				}
				drawer_price.value = true;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
				drawer_price.value = false;
			});
	};

	const price_title = ref('1月价格'); // 抽屉的title
	// 关闭抽屉的回调
	const priceClose = () => {
		// console.log('关闭价格抽屉')
		form_state_price_ref.value.clearValidate();
		form_state_price_ref.value.resetFields();
	};

	// 关闭同步价格抽屉的回调
	const syncProClose = () => {
		if (project_list.value && project_list.value.length > 0) {
			form_sync_pro.copyProjectId = project_list.value[0].projectId;
		}
		if (month_options.value.length > 0) {
			form_sync_pro.copyMonth = month_options.value[0].value;
		}
	};

	const cancelPriceDrawer = () => {
		drawer_price.value = !drawer_price.value;
	};

	const cancelSyncProDrawer = () => {
		drawer_sync_pro.value = !drawer_sync_pro.value;
	};

	const syncOtherProject = () => {
		sync_pro_loading.value = true;
		form_sync_pro_ref.value.validateFields().then(() => {
			let params = JSON.parse(JSON.stringify(form_sync_pro));
			params.projectId = project_id.value
			syncOtherProjectHttp(params)
				.then(() => {
					message.success('同步成功！');
					getList();
					sync_pro_loading.value = false;
					drawer_sync_pro.value = false;
				})
				.catch((error) => {
					message.error(error.message);
						setTimeout(() => {
							message.destroy();
						}, 1500);
						sync_pro_loading.value = false;
				});
		}).catch((error) => {
			sync_pro_loading.value = false;
			console.log('表单验证失败：', error);
		});
	};

	const upLoad_price_loading = ref(false);
	const sync_pro_loading = ref(false);
	const upLoadPrice = () => {
		upLoad_price_loading.value = true;
		// 进行校验
		form_state_price_ref.value
			.validateFields()
			.then(() => {
				let detailList = [];
				for (const key in form_state_price) {
					if (form_state_price[key]) {
						detailList.push({ price: Number(form_state_price[key]), rateType: key });
					} else {
						if (form_state_price[key] == '0') {
							detailList.push({ price: 0, rateType: key });
						} else {
							// detailList.push({ price: null, rateType: key });
						}
					}
				}
				let params = {
					detailList: detailList,
					month: current_month.value,
					projectId: project_id.value,
					year: time_year.value.format('YYYY')
				};
				if (elerateId.value != '0') {
					params.elerateId = elerateId.value;
				}
				savePriceByMonthHttp(params)
					.then(() => {
						message.success('保存成功！');
						upLoad_price_loading.value = false;
						drawer_price.value = false;
						getList();
					})
					.catch((error) => {
						message.error(error.message);
						setTimeout(() => {
							message.destroy();
						}, 1500);
						upLoad_price_loading.value = false;
					});
			})
			.catch(() => {
				upLoad_price_loading.value = false;
			});
	};

	// ***********************************************************************************************************
	//                                                   价格配置                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                   时段配置                                                //
	// ***********************************************************************************************************

	// 尖
	const time_allday_jian = ref();
	// 峰
	const time_allday_feng = ref();
	// 平
	const time_allday_ping = ref();
	// 谷
	const time_allday_gu = ref();
	// 深谷
	const time_allday_shengu = ref();

	const timeCreate = (time_list) => {
		// console.log(time_list)
		// 开始和节数的时间
		let temp_data = [];
		for (let index = 0; index < 24; index++) {
			let start_time_hour = index;
			let end_time_hour = index;
			if (end_time_hour < 10) {
				end_time_hour = '0' + end_time_hour;
			}
			if (start_time_hour < 10) {
				start_time_hour = '0' + start_time_hour;
			}
			for (let index1 = 0; index1 < 4; index1++) {
				let start_time_minute = index1 * 15;
				let end_time_minute = (index1 + 1) * 15;

				if (index1 == 0) {
					start_time_minute = '00';
				}
				if (index1 == 3) {
					end_time_minute = '00';
					end_time_hour = Number(end_time_hour) + 1 + '';
					if (Number(end_time_hour) < 10) {
						end_time_hour = '0' + Number(end_time_hour);
					}
				}
				let active = false;
				// 处理初始化有选中的效果
				if (time_list) {
					time_list.forEach((item) => {
						let start = item.startTime;
						let end = item.endTime;
						if (end == '23:59') {
							end = '24:00';
						}
						let start_number = Number(`${start_time_hour}${start_time_minute}`);
						let end_number = Number(`${end_time_hour}${end_time_minute}`);
						start = Number(`${start.split(':')[0]}${start.split(':')[1]}`);
						end = Number(`${end.split(':')[0]}${end.split(':')[1]}`);

						// //console.log(start);//参考开始时间
						// //console.log(end);// 参考结束时间

						// //console.log(start_number);//96的循环开始时间
						// //console.log(end_number);// 96的循环结束时间

						if (start_number >= start && end_number <= end) {
							// 判断是否再时间区间内
							active = true;
						}
					});
				}

				temp_data.push({
					time_start: `${start_time_hour}:${start_time_minute}`,
					time_end: `${end_time_hour}:${end_time_minute}`,
					active: active
				});
			}
		}
		// console.log(temp_data);
		return temp_data;
	};

	const initTime = () => {
		time_allday_jian.value = timeCreate();
		time_allday_feng.value = timeCreate();
		time_allday_ping.value = timeCreate();
		time_allday_gu.value = timeCreate();
		time_allday_shengu.value = timeCreate();
	};

	// 尖峰平谷时间选择的时候 进行初始化的函数
	initTime();

	const drawer_time = ref();

	//全年配置的时候的月份选择
	const month_checks = ref([]);
	watch(month_checks, (newVal) => {
		if (newVal.length == 12) {
			all_checked.value = true;
		} else {
			all_checked.value = false;
		}
	});
	// 是否全年
	const all_checked = ref(false);
	watch(all_checked, (newVal) => {
		if (newVal) {
			let temp = [];
			for (let index = 1; index <= 12; index++) {
				temp.push(index);
			}
			month_checks.value = temp;
		} else {
			if (month_checks.value.length == 12) {
				month_checks.value = [];
			}
		}
	});
	// 单月配置的月份信息
	const current_month = ref(null);
	const elerateId = ref(null);

	// 全年时段配置
	const is_full_year = ref(false); //是否是全年配置
	const all_year_config = ref(false);
	const allYearConfig = () => {
		all_year_config.value = true;
		is_full_year.value = true;
		current_month.value = null; //单月的时间置空
		price_time.value = `全年时段配置`;
		// 处理时间的初始化
		all_checked.value = true; //是否全选
		let temp_month = [];
		for (let index = 1; index <= 12; index++) {
			temp_month.push(index);
		}
		month_checks.value = temp_month;
		drawer_time.value = !drawer_time.value;
		all_year_config.value = false;
	};

	const sync_time_modal = ref();
	const showDeleteModal = () => {
		sync_time_modal.value.showHideDeleteModal();
	};

	const sync_time_status = ref(false);
	const sync_pro_status = ref(false);
	const syncTime = () => {
		sync_time_status.value = true;
		let params = {
			projectId: project_id.value,
			year: Number(dayjs(time_year.value).format('YYYY'))
		};
		saveTimeSyncLastYearHttp(params)
			.then(() => {
				message.success('同步成功！');
				getList();
				// showDeleteModal();
				sync_time_status.value = false;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
				sync_time_status.value = false;
			});
	};

	// 分时时段配置
	const openTime = (item) => {
		// console.log(item)
		is_full_year.value = false;
		let params = item.elerateId;
		getTimeDetailByElerateIdHttp(params)
			.then((data) => {
				for (let i = 0; i < data.length; i++) {
					//console.log(data[i].rateType)
					switch (data[i].rateType) {
						case 1:
							time_allday_jian.value = timeCreate(data[i].timeRangeList);
							break;
						case 2:
							time_allday_feng.value = timeCreate(data[i].timeRangeList);
							break;
						case 3:
							time_allday_ping.value = timeCreate(data[i].timeRangeList);
							break;
						case 4:
							time_allday_gu.value = timeCreate(data[i].timeRangeList);
							break;
						case 5:
							time_allday_shengu.value = timeCreate(data[i].timeRangeList);
							break;
						default:
							break;
					}
				}
				elerateId.value = item.elerateId;
				price_time.value = `分时时段配置-${item.month}月`;
				current_month.value = item.month;
				// 对月份进行初始化赋值
				is_full_year.value = false;
				drawer_time.value = !drawer_time.value;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	const price_time = ref('');

	const timeClose = () => {
		////console.log('关闭时间')
		// 重置数据
		// 时间选择项目重置
		is_full_year.value = true;
		all_checked.value = true;
		month_checks.value = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
		initTime();
	};

	// 把时间转换为数字比较大小
	const changeTimeToNum = (time) => {
		let num1 = Number(time.split(':')[0]) * 100;
		let num2 = Number(time.split(':')[1]);
		return num1 + num2;
	};

	// 处理选中状态的函数
	const handleChoiced = (data, index, item) => {
		let temp_data = data;
		temp_data[index].active = !temp_data[index].active;
		// 范围之内改变状态
		// for (let i = 0; i < temp_data.length; i++) {
		//   let element = temp_data[i]
		//   if (
		//     changeTimeToNum(element.time_start) >= changeTimeToNum(startNum.value) &&
		//     changeTimeToNum(element.time_end) <= changeTimeToNum(item.time_end)
		//   ) {
		//     element.active = true
		//   }
		// }
		return temp_data;
	};

	// 选择尖峰平谷的时间
	const choiceTime = (index, type, item) => {
		// console.log(1111111);
		// item
		// type  类型：1.尖期 2.峰期 3.平期 4.谷期 5.深谷期
		// index 选择的数据的顺序
		switch (type) {
			case '1':
				time_allday_jian.value = handleChoiced(time_allday_jian.value, index, item);
				break;
			case '2':
				time_allday_feng.value = handleChoiced(time_allday_feng.value, index, item);
				break;
			case '3':
				time_allday_ping.value = handleChoiced(time_allday_ping.value, index, item);
				break;
			case '4':
				time_allday_gu.value = handleChoiced(time_allday_gu.value, index, item);
				break;
			case '5':
				time_allday_shengu.value = handleChoiced(time_allday_shengu.value, index, item);
				break;

			default:
				break;
		}
	};

	// 时间配置的抽屉的提交按钮的状态
	const upLoad_time_loading = ref(false);
	// 取消 时段的抽屉的提交
	const cancelTime = () => {
		timeClose();
		drawer_time.value = false;
	};

	// 处理时间格式为上传的时间格式
	const handleTimeFormat = (data) => {
		let temp_data = [];
		for (let index = 0; index < data.length; index++) {
			if (data[index].active) {
				// let start_time = data[index].time_start
				// start_time = start_time.split(':')
				// let end_time = data[index].time_end
				// end_time = end_time.split(':')
				if (data[index].time_end == '24:00') {
					data[index].time_end = '23:59';
				}
				temp_data.push({ endTime: data[index].time_end, startTime: data[index].time_start });
			}
		}
		return temp_data;
	};

	// 提交时间数据
	const upLoadTime = () => {
		//////console.log('上传时间列表')
		let month_list = JSON.parse(JSON.stringify(month_checks.value));
		let projectId = project_id.value;
		let year = dayjs(time_year.value).format('YYYY');
		let timeRangeList = [];
		// ******************进行数据的判断
		let validata_result = time_allday_jian.value
			.concat(time_allday_feng.value)
			.concat(time_allday_ping.value)
			.concat(time_allday_gu.value)
			.concat(time_allday_shengu.value);

		let result_length_list = [];
		validata_result.forEach((item) => {
			if (item.active) {
				result_length_list.push(item.time_start);
			}
		});
		if (result_length_list.length != 96 && result_length_list.length <= 96) {
			message.error('当前时段配置有时间段遗漏，请检查后在保存');
			return;
		}
		let basic_length = result_length_list.length;
		result_length_list = [...new Set(result_length_list)];
		//////console.log(result_length_list)
		if (basic_length != result_length_list.length) {
			message.error('当前时段配置有重叠，请重新配置！');
			return;
		}
		// ******************进行数据的判断

		let jian_result = handleTimeFormat(JSON.parse(JSON.stringify(time_allday_jian.value)));
		let feng_result = handleTimeFormat(JSON.parse(JSON.stringify(time_allday_feng.value)));
		let ping_result = handleTimeFormat(JSON.parse(JSON.stringify(time_allday_ping.value)));
		let gu_result = handleTimeFormat(JSON.parse(JSON.stringify(time_allday_gu.value)));
		let shengu_result = handleTimeFormat(JSON.parse(JSON.stringify(time_allday_shengu.value)));

		timeRangeList.push({ rateType: 1, timeRangeList: jian_result });
		timeRangeList.push({ rateType: 2, timeRangeList: feng_result });
		timeRangeList.push({ rateType: 3, timeRangeList: ping_result });
		timeRangeList.push({ rateType: 4, timeRangeList: gu_result });
		timeRangeList.push({ rateType: 5, timeRangeList: shengu_result });

		////console.log('是否是全年')
		////console.log(is_full_year.value)

		if (is_full_year.value) {
			if (month_list.length == 0) {
				message.error('时间选择不能为空！');
				return;
			}
			// 多月份配置的
			let params = {
				months: month_list,
				projectId: Number(projectId),
				year: Number(year),
				detailList: timeRangeList
			};
			saveTimeByMonthListHttp(params)
				.then((data) => {
					if (data) {
						message.success('保存成功！');
						cancelTime();
						// drawer_time.value = false
						getList();
					}
				})
				.catch((error) => {
					message.error(error.message);
					setTimeout(() => {
						message.destroy();
					}, 1500);
					drawer_time.value = true;
				});
		} else {
			let params = {
				month: Number(current_month.value),
				projectId: Number(projectId),
				year: Number(year),
				detailList: timeRangeList
			};
			if (elerateId.value != '0') {
				params.elerateId = elerateId.value;
			}
			// 单月配置的
			saveTimeByMonthHttp(params)
				.then((data) => {
					if (data) {
						message.success('保存成功！');
						getList();
						drawer_time.value = false;
					}
				})
				.catch((error) => {
					message.error(error.message);
					setTimeout(() => {
						message.destroy();
					}, 1500);
					drawer_time.value = true;
				});
		}
	};

	// 滑动选择时间的函数
	const mouse_down_status = ref(null);
	const startNum = ref(null);

	// 选中后其他的元素的相同的位置进行置灰的操作

	// 根据类名判断鼠标点击的时候是在哪个元素中
	const choiceStatus = ref(false);
	const mousedownLine = () => {
		choiceStatus.value = true;
	};

	const mouseupLine = () => {
		choiceStatus.value = false;
	};

	const mouseleaveLine = (e) => {
		// console.log('超出范围')
		// console.log(e.target.attributes.line_type.value);
		if (choiceStatus.value && e.target.attributes.line_type.value == 'out') {
			// 选中状态并超出了条目的容器的范围   e.target.attributes.line_type
			choiceStatus.value = false;
		}
	};
	const mouseoverLine = (e) => {
		e.preventDefault();
		if (!choiceStatus.value || !e.target.attributes.line_type.value) {
			//  鼠标 因为 没有处于按中 状态  choiceStatus.value
			// 因为选中的子元素的数字 而不是 颜色条目   e.target.attributes.line_type
			return;
		}

		let line_type = e.target.attributes.line_type.value;
		if (choiceStatus.value) {
			// 鼠标点击和滑动的是在同一个项目中
			let time_start = e.target.attributes.time_start.value;
			let time_end = e.target.attributes.time_end.value;
			let index = e.target.attributes.index.value;
			let active = e.target.attributes.active.value;
			let item = { active, time_start, time_end };
			choiceTime(index, line_type, item);
		}
	};
	const mouseLeaveItem = (e) => {
		let line_type = e.target.attributes.line_type.value;
		if (mouse_down_status.value == line_type) {
			// 鼠标点击和滑动的是在同一个项目中
			let time_start = e.target.attributes.time_start.value;
			let time_end = e.target.attributes.time_end.value;
			let index = e.target.attributes.index.value;
			let active = e.target.attributes.active.value;
			let item = { active, time_start, time_end };
			choiceTime(index, line_type, item);
		}
	};
	/**
	 * 获取电价列表
	 *
	 * @returns 无返回值
	 */
	const getList = () => {
		// 构建请求参数
		let params = { projectId: project_id.value, year: dayjs(time_year.value).format('YYYY') };

		// 发起价格详情请求
		priceDetailOnYearHttp(params)
			.then((data) => {
				// 获取当前时间
				const currentTime = dayjs();
				// 获取当前时间两个月后的时间
				const twoMonthsLater = currentTime.add(2, 'month');

				// 如果有数据
				if (data) {
					// 遍历数据
					data.forEach((item) => {
						// 将年月组合成日期对象
						const targetDateObj = dayjs(`${item.year}-${item.month}`);
						// 判断目标日期是否在当前时间两个月内
						const isBeforeOrEqual = targetDateObj.isSameOrBefore(twoMonthsLater);

						// 如果目标日期在当前时间两个月内
						if (isBeforeOrEqual) {
							// 如果价格信息已维护
							if (item.priceFlag) {
								item.month_status = 'normal_tag';
								item.tips_title = '该月份的价格信息已维护';
								item.color = 'rgba(17, 129, 253, 0.6)';
							} else {
								// 如果目标日期在当前时间之前或相同
								if (targetDateObj.isSameOrBefore(currentTime)) {
									item.month_status = 'error_tag';
									item.tips_title = '该月份的电价信息已经过期，请尽快维护';
									item.color = 'rgba(255, 97, 97, 0.6)';
								} else {
									// 否则
									item.month_status = 'warning_tag';
									item.tips_title = '您已经可以在国网/南网官网查到下月电价，请尽快维护';
									item.color = 'rgba(253, 148, 25, 0.6)';
								}
							}
						} else {
							// 如果目标日期不在当前时间两个月内
							item.month_status = 'disabled_tag';
							item.tips_title = '该月份的电价目前无法配置';
							item.color = 'rgba(156, 162, 175, 0.6)';
						}
					});
					// 处理颜色后赋值给month_list
					month_list.value = handleColor(data);
				}
			})
			.catch((error) => {
				// 请求失败时打印错误信息
				console.error(error.message);
			});
	};
	// const getList = () => {
	// 	// 获取数据
	// 	let params = { projectId: project_id.value, year: dayjs(time_year.value).format('YYYY') };
	// 	//////console.log(params);
	// 	priceDetailOnYearHttp(params)
	// 		.then((data) => {
	// 			console.log(data);
	// 			console.log(time_now_month.value);
	// 			// 获取当前时间
	// 			const currentTime = dayjs();

	// 			// 计算两个月后的时间
	// 			const twoMonthsLater = currentTime.add(2, 'month');
	// 			console.log(dayjs().isSameOrBefore('2011-01-01', 'month'));

	// 			if (data) {
	// 				data.forEach((item) => {
	// 					let targetDateObj = dayjs(`${item.year}-${item.month}`);
	// 					// 比较时间的大小
	// 					// 在当前时间+两个月的之前的时间都是可以配置的
	// 					if (dayjs(targetDateObj).isSameOrBefore(twoMonthsLater)) {
	// 						// 可配置的月份
	// 						if (item.priceFlag) {
	// 							// 已经配置的
	// 							item.month_status = 'normal_tag';
	// 							item.tips_title = '该月份的价格信息已维护';
	// 							item.color = 'rgba(17, 129, 253, 0.6)';
	// 						} else {
	// 							// 本月和本月之前都没配置的
	// 							if (dayjs(targetDateObj).isSameOrBefore(currentTime)) {
	// 								item.month_status = 'error_tag';
	// 								item.tips_title = '该月份的电价信息已经过期，请尽快维护';
	// 								item.color = 'rgba(255, 97, 97, 0.6)';
	// 							} else {
	// 								// 本月之后没有配置的
	// 								item.month_status = 'warning_tag';
	// 								item.tips_title = '您已经可以在国网/南网官网查到下月电价，请尽快维护';
	// 								item.color = 'rgba(253, 148, 25, 0.6)';
	// 							}
	// 						}
	// 					} else {
	// 						// 不可配置的月份
	// 						item.month_status = 'disabled_tag';

	// 						item.tips_title = '该月份的电价目前无法配置';
	// 						item.color = 'rgba(156, 162, 175, 0.6)';
	// 					}
	// 				});
	// 				month_list.value = handleColor(data);
	// 			}
	// 		})
	// 		.catch((error) => {
	// 			message.error(error.message);
	// 			setTimeout(() => {
	// 				message.destroy();
	// 			}, 1500);
	// 		});
	// };

	// 处理初始时间的颜色
	const handleColor = (data) => {
		let result = [];
		//console.log(data)
		data.forEach((item) => {
			// 进入每个月的循环
			let month_list_detail = [];
			let month_length = 0;
			item.detailList.forEach((item1) => {
				// 进入每个月的尖峰平谷的分类
				item1.timeRangeList.forEach((item2) => {
					// 处理具体的时间段
					let hour = item2.endTime.split(':')[0] - item2.startTime.split(':')[0];
					let minute =
						Number(item2.endTime.split(':')[1]) == '59'
							? 60
							: item2.endTime.split(':')[1] - item2.startTime.split(':')[1];
					let sort = Number(item2.startTime.split(':')[0] + '' + item2.startTime.split(':')[1]);
					let line_length = hour * 4 + minute / 15;
					month_length = month_length + line_length;
					month_list_detail.push({
						mess: `${item1.rateTypeDesc}:${item2.startTime}-${item2.endTime}`,
						sort: sort,
						line_length: line_length,
						rateType: item1.rateType
					});
				});
			});
			let { elerateId, month, month_status, priceFlag, year, tips_title, color } = item;
			month_list_detail.sort(function (a, b) {
				return a.sort - b.sort;
			});
			result.push({
				elerateId,
				month,
				month_status,
				priceFlag,
				year,
				month_list_detail,
				month_length,
				tips_title,
				color
			});
		});
		////console.log(result)
		return result;
	};

	// ***********************************************************************************************************
	//                                                   页面初始                                                //
	// ***********************************************************************************************************

	onMounted(() => {
		// 项目id
		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			////////console.log(project_mess)
			project_id.value = project_mess.project_id;
		} else {
			message.error('未选择项目，请重新选择！');
		}
		// 获取初始列表
		getList();
	});
</script>

<style lang="less" scoped>
	.current_page {
		padding-top: 32px;
		box-sizing: border-box;
	}

	// 特殊处理
	.month_list_contain {
		flex: 1;
		overflow-y: scroll;
		.month_list {
			background-color: #ffffff;
			padding-right: 32px;
			padding-bottom: 28px;
		}
	}

	.month_item {
		height: 80px;
		width: 100%;
		background-color: rgba(242, 248, 255, 1);
		display: flex;
		align-items: center;
		padding: 0 32px 0px 0px;
		margin-bottom: 16px;
		.month_title {
			padding: 0 32px;
			height: 100%;
			width: 284px;
			box-sizing: border-box;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			div {
				height: 28px;
				width: 66px;
				line-height: 28px;
				cursor: pointer;
				border-radius: 20px;
			}
			.price_tips {
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				img {
					margin-right: 3px;
				}
			}
			.normal_tag {
				background: rgba(17, 129, 253, 0.17) !important;
				&:hover {
					background-color: rgba(17, 129, 253, 0.27) !important;
				}
			}
			.warning_tag {
				background: rgba(253, 148, 25, 0.17) !important;
				&:hover {
					background-color: rgba(253, 148, 25, 0.27) !important;
				}
			}
			.error_tag {
				background: rgba(255, 97, 97, 0.17) !important;
				&:hover {
					background-color: rgba(255, 97, 97, 0.27) !important;
				}
			}
			.disabled_tag {
				background: rgba(221, 226, 237, 0.2) !important;
				color: rgba(221, 226, 237, 1) !important;
				border: 1px solid rgba(221, 226, 237, 1) !important;
				&:hover {
					background-color: rgba(221, 226, 237, 0.47) !important;
				}
				cursor: not-allowed;
			}
		}
		.detail_month {
			height: 45px;
			width: calc(100% - 284px);
			.ant-col {
				text-align: center;
				line-height: 45px;
			}
			.month_time_list {
				height: 100%;
				width: 100%;
				display: flex;
				flex-direction: row;
				.month_time_list_item {
					cursor: pointer;
					line-height: 45px;
					text-align: center;
					height: 100%;
					overflow: hidden;
					text-overflow: ellipsis;
				}
				.rateType1 {
					background-color: rgba(156, 255, 133, 1);
				}
				.rateType2 {
					background-color: rgba(51, 231, 180, 1);
				}
				.rateType3 {
					background-color: rgba(84, 164, 254, 1);
				}
				.rateType4 {
					background-color: rgba(171, 184, 255, 1);
				}
				.rateType5 {
					background-color: rgba(153, 179, 207, 1);
				}
				.rateType6 {
				}
			}
		}
	}
	.jian {
		background-color: rgba(156, 255, 133, 1) !important;
	}
	.feng {
		background-color: rgba(3, 227, 161, 1) !important;
	}
	.ping {
		background-color: rgba(17, 129, 253, 1) !important;
	}
	.gu {
		background-color: rgba(121, 133, 252, 1) !important;
	}
	.shengu {
		background-color: rgba(153, 179, 207, 0.8) !important;
	}

	.time_list_item {
		display: flex;
		height: 80px;
		padding: 0 20px;
		margin-bottom: 10px;
		.time_list_item_title {
			height: 100%;
			width: 120px;
			text-align: left;
			font-family: SourceHanSansCN-Medium;
			font-weight: 500;
			font-size: 18px;
			color: #092649;
			line-height: 80px;
		}
	}
	.time_list_item_24 {
		flex: 1;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;

		.time_list_item_24_bar {
			flex: 1;
			// margin: 0 1px;
			background-color: rgba(234, 240, 246, 1);
			height: 40px;
			border-radius: 2px;
			cursor: pointer;
			position: relative;
			border: 1px solid #ffffff;
			.time_number {
				position: absolute;
				bottom: 10px;
				bottom: -23px;
				left: -50%;
				font-family: SourceHanSansCN-Regular;
				font-weight: 400;
				font-size: 14px;
				color: #bbc1cd;
				user-select: none;
				-webkit-user-select: none; /* Safari浏览器 */
				-moz-user-select: none; /* Firefox浏览器 */
				-ms-user-select: none; /* IE/Edge浏览器 */
				height: 14px;
				line-height: 14px;
			}
			.time_number.active {
				font-family: SourceHanSansCN-Medium;
				font-weight: 500;
				font-size: 14px;
				color: #092649;
			}
		}
	}

	// 同步去年时段的hover效果
	.icon_button {
		.icon_img_refresh {
			height: 16px;
			width: 16px;
			background-image: url('/assets/icon/tb_time.png');
			background-repeat: no-repeat;
			background-size: 100%;
			margin-right: 10px;
			position: relative;
			top: 0px;
		}
		&:hover {
			.icon_img_refresh {
				height: 16px;
				width: 16px;
				background-image: url('/assets/icon/tb_time_hover.png');
				background-repeat: no-repeat;
				background-size: 100%;
				margin-right: 10px;
				position: relative;
				top: 0px;
			}
		}
	}
</style>
<style>
	.ant-tooltip {
		width: unset;
	}
	.ant-tooltip-content {
		width: unset;
	}
</style>
