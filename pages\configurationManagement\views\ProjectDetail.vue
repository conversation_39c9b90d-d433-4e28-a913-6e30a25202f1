<!-- 配置管理 ---  项目信息  -->
<template>
	<div class="page_all current_page">
		<!-- 页面主要内容 -->
		<div class="third_menu_contain">
			<div
				:class="['third_menu_item', currentThirdMenu == index ? 'active' : '']"
				@click="menuChange(item, index)"
				v-for="(item, index) in third_menu_list"
				:key="index"
			>
				<span class="third_menu_name">{{ item.name }}</span>
				<span class="third_menu_line"></span>
			</div>
		</div>
		<div class="main_contain main_contain_thirdMenu">
			<div class="main_contain_scroll">
				<!-- <div class="page_header_title">基础配置</div> -->
				<!-- form 表单 -->
				<a-form
					id="deviceConfigurationFrom"
					ref="deviceConfigurationFrom"
					name="deviceConfigurationFrom"
					:model="form_state_systemsetting"
				>
					<a-form-item
						label="项目名称"
						name="projectName"
						:rules="[{ required: true, message: '请输入项目名称' }]"
					>
						<a-input
							autocomplete="off"
							v-model:value="form_state_systemsetting.projectName"
							style="width: 420px"
							disabled
							placeholder="请输入项目名称"
						></a-input>
					</a-form-item>
					<a-form-item
						label="项目类型"
						name="projectType"
						:rules="[{ required: true, message: '请选择对应的项目类型' }]"
					>
						<a-select
							v-model:value="form_state_systemsetting.projectType"
							placeholder="请选择对应的项目类型"
							:getPopupContainer="(e) => e.parentNode"
							style="width: 420px"
						>
							<a-select-option
								v-for="(itemSelect, indexSelect) in projectTypeList"
								:key="indexSelect"
								labelInValue
								:value="itemSelect.value"
							>
								{{ itemSelect.label }}
							</a-select-option>
						</a-select>
					</a-form-item>

					<a-form-item
						label="项目户号"
						name="accountNumber"
						:rules="[
							{ required: true, message: '请输入对应的项目户号' },
							{
								required: true,
								validator: (rule, value) => {
									return projectNoValidate(value);
								},
								message: '项目户号只支持64位以内的英文和数字'
							}
						]"
					>
						<a-input
							autocomplete="off"
							v-model:value="form_state_systemsetting.accountNumber"
							style="width: 420px"
							placeholder="请输入对应的项目户号"
						></a-input>
					</a-form-item>

					<!-- row -col用来修噶form的布局位置 -->
					<a-row>
						<a-col>
							<a-form-item
								label="项目关系"
								name="regionId"
								:rules="[{ required: true, message: '请选择对应的项目关系' }]"
							>
								<a-select
									placeholder="请选择项目对应的归属区域"
									v-model:value="form_state_systemsetting.regionId"
									style="width: 420px"
									:options="area_list"
									:getPopupContainer="(e) => e.parentNode"
								></a-select>
							</a-form-item>
						</a-col>
						<a-col>
							<a-form-item
								name="parkId"
								:rules="[{ required: true, message: '请选择项目对应的中心园区' }]"
								style="padding-left: 20px !important"
							>
								<a-select
									placeholder="请选择项目对应的中心园区"
									v-model:value="form_state_systemsetting.parkId"
									:disabled="form_state_systemsetting.regionId ? false : true"
									style="width: 440px"
									:options="current_park"
									:getPopupContainer="(e) => e.parentNode"
								></a-select>
							</a-form-item>
						</a-col>
					</a-row>

					<a-row>
						<a-col>
							<a-form-item
								label="项目地址"
								name="address1"
								:rules="[{ required: true, message: '请选择对应的省市信息' }]"
							>
								<a-tree-select
									placeholder="请选择对应的省市信息"
									style="width: 420px"
									@change="changeTree"
									ref="tree"
									v-model:value="form_state_systemsetting.address1"
									show-search
									:getPopupContainer="(e) => e.parentNode"
									labelInValue
									:tree-data="disableTreeData"
									tree-node-filter-prop="label"
								></a-tree-select>
							</a-form-item>
						</a-col>
						<a-col>
							<a-form-item
								name="address2"
								:rules="[
									{ required: true, message: '请输入项目地址' },
									{
										required: true,
										validator: (rule, value) => {
											return projectLocationValidate(value);
										},
										message: '项目地址只支持中英文数字，最多100个字'
									}
								]"
								style="padding-left: 20px"
							>
								<!--  -->
								<a-input
									autocomplete="off"
									style="width: 440px"
									v-model:value="form_state_systemsetting.address2"
									placeholder="请输入项目地址"
								/>
							</a-form-item>
						</a-col>
					</a-row>
					<a-row>
						<a-col>
							<a-form-item
								label="项目经纬度"
								name="buildLat"
								:rules="[
									{
										required: true,
										validator: (rule, value) => {
											// //console.log(value)
											return longTest(value);
										},
										message: '请按照经度标准输入！'
									}
								]"
							>
								<a-input
									autocomplete="off"
									v-model:value="form_state_systemsetting.buildLat"
									style="width: 420px"
									placeholder="请输入项目的经度信息"
								></a-input>
							</a-form-item>
						</a-col>
						<a-col>
							<!-- class="unique_form_error_tips" -->
							<a-form-item
								label=""
								name="buildLong"
								:rules="[
									{
										required: true,
										validator: (rule, value) => {
											return latTest(value);
										},
										message: '请按照纬度标准输入！'
									}
								]"
								style="padding-left: 20px !important"
							>
								<a-input
									autocomplete="off"
									v-model:value="form_state_systemsetting.buildLong"
									style="width: 440px"
									placeholder="请输入项目的纬度信息"
								></a-input>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
				<!-- 页面数据的重置和保存 -->

				<div
					class="footer_button"
					v-if="operaType == 1 ? true : false"
				>
					<a-button @click="restAllForm">重置</a-button>
					<a-button
						:loading="upload_loading"
						style="margin-left: 32px"
						type="primary"
						@click="upFormDataNoDevice"
					>
						保存
					</a-button>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, watch, reactive, onMounted } from 'vue';
	import { projectType } from '../assets/configuration/deviceConfiguration.js';
	import { message } from 'ant-design-vue';
	// 地址树结构
	import { mapTree } from '/assets/js/mapTree.js';

	//校验规则
	import { projectNoValidate, projectLocationValidate, latTest, longTest } from '/assets/js/reg.js';

	// 实际的接口
	import http from '/tool/http.js';

	import { getProjectDetailHttp, saveProjectMessHttp } from './api.js';

	import { getAllAreaHttp } from '/components/commonApi.js';

	import { useRouter } from 'vue-router';

	const operaType = ref(0);

	const router = useRouter();

	const project_id = ref(null);

	// 三级菜单切换
	const third_menu_list = ref([
		{ name: '项目信息', url: '/projectDetail' },
		{ name: '项目设备', url: '/deviceConfiguration' },
		{ name: '电价配置', url: '/electricPrice' },
		{ name: '策略配置', url: '/policyConfiguration' }
	]);

	const currentThirdMenu = ref(0);
	const menuChange = (item, index) => {
		currentThirdMenu.value = index;
		router.push(item.url);
	};

	const deviceConfigurationFrom = ref();
	const form_state_systemsetting = reactive({
		projectType: null,
		accountNumber: '',
		projectName: null,
		regionId: null,
		parkId: null,
		address1: { value: null, label: null },
		address2: '',
		projectDevice: 'device_table'
	});
	const projectTypeList = ref([...projectType]);
	console.log(projectTypeList.value);
	// 重置表格的数据
	const restAllForm = () => {
		let temp_name = form_state_systemsetting.projectName;
		deviceConfigurationFrom.value.resetFields();
		form_state_systemsetting.projectName = temp_name;
	};

	// 提交系统配置-基础配置的数据 不含设备
	const upload_loading = ref(false);
	const upFormDataNoDevice = () => {
		upload_loading.value = true;
		// 验证表单，并根据结果进行后续
		deviceConfigurationFrom.value
			.validateFields()
			.then((data) => {
				// 此时正常提交
				let {
					projectName,
					accountNumber: accountNumber,
					address1: { label: address1 },
					address2,
					buildLat,
					buildLong,
					regionId,
					parkId,
					projectType
				} = data;
				let params = {
					accountNumber,
					address: address1 + '&&' + address2,
					buildLat: Number(buildLat),
					buildLong: Number(buildLong),
					parkId,
					projectType,
					regionId,
					projectId: project_id.value,
					projectName
				};

				saveProjectMessHttp(params).then((data) => {
					console.log(data);
					if (data.code == 200) {
						message.success(data.msg);
						upload_loading.value = false;
						setTimeout(() => {
							window.history.go(0);
						}, 1000);
					}
				});
			})
			.catch((errorInfo) => {
				//console.log(errorInfo)
				upload_loading.value = false;
			});
	};

	// 获取项目关系的数据
	const area_list = ref([]); // 区域
	const park_list_all = ref([]); // 中心园区
	const current_park = ref(null); //选的哪个园区
	// 获取所有的区域数据
	const getProjectRelation = () => {
		getAllAreaHttp({ proShow: 0 }).then((data) => {
			let temp_area = [];
			let tem_park_list = [];
			data.forEach((item) => {
				let { regionName: label, regionId: value } = item;
				temp_area.push({ label, value });
				let temp = [];
				item.parkList.forEach((item1) => {
					let { parkId: value, parkName: label } = item1;
					temp.push({ value, label });
				});
				tem_park_list.push({ area_id: item.regionId, park_list: JSON.parse(JSON.stringify(temp)) });
			});
			area_list.value = temp_area;
			park_list_all.value = tem_park_list;
			console.log(park_list_all);
			let params = project_id.value;
			// 获取基本信息
			getProjectDetailHttp(params).then((data) => {
				handleInitData(data);
			});
		});
	};

	// 初始项目信息的处理
	const handleInitData = (data) => {
		let {
			accountNumber,
			address,
			buildLat,
			buildLong,
			parkId,
			projectName,
			projectType,
			regionId
		} = data;
		console.log(parkId);
		console.log(projectType);
		let address1 = { value: null, label: null };
		let address2 = null;
		if (address) {
			address1.label = address.split('&&')[0];
			let temp_address = address1.label.split('/');
			address1.value = temp_address[temp_address.length - 1];
			address2 = address.split('&&')[1];
		}
		Object.assign(form_state_systemsetting, {
			accountNumber,
			address1,
			address2,
			buildLat,
			buildLong,
			parkId,
			projectName,
			projectType,
			regionId
		});
	};

	// 选择区域时切换园区数据
	watch(
		() => form_state_systemsetting.regionId,
		(newVal) => {
			current_park.value = [];
			console.log(form_state_systemsetting.parkId);
			park_list_all.value.forEach((item) => {
				if (item.area_id == newVal) {
					current_park.value = item.park_list;
					console.log(item.park_list);
					let flag = true;
					item.park_list.forEach((item1) => {
						if (item1.value == form_state_systemsetting.parkId) {
							flag = false;
						}
					});
					if (flag) {
						form_state_systemsetting.parkId = null;
					}
				}
			});
		},
		{ deep: true }
	);

	// 处理地址相关的数据-------------------
	// 选择地址树结构的数据
	const selectResult = ref();
	watch(selectResult, () => {
		// //console.log(value);
	});
	const changeTree = (mess) => {
		////console.log(mess)
		let resultLabel = getFullLabel(mess.value);
		////console.log(resultLabel)
		flatArray.value.forEach((item) => {
			if (item.value == mess.value && item.label == mess.label) {
				if (item.hasOwnProperty('children')) {
					form_state_systemsetting.address1 = { value: '', label: '' };
				} else {
					form_state_systemsetting.address1 = { value: mess.value, label: resultLabel };
				}
			}
		});
		// emit('update:value', { ...props.value, ...finalResult });
		// formItemContext.onFieldChange();
	};

	//  把树结构的数据拉平
	const flatArray = ref([]);
	const flatArrayFn = (data) => {
		data.forEach((item) => {
			if (item.hasOwnProperty('children')) {
				flatArray.value.push({
					value: item.value,
					label: item.label,
					parentId: item.parentId,
					children: true
				});
				flatArrayFn(item.children);
			} else {
				flatArray.value.push({ value: item.value, label: item.label, parentId: item.parentId });
			}
		});
	};
	flatArrayFn(mapTree);

	// 获取全名称的数据
	const getFullLabel = (target) => {
		let tempLabel = '';
		flatArray.value.forEach((item) => {
			if (target == item.value && item.parentId) {
				tempLabel = getFullLabel(item.parentId) + '/' + item.label;
			} else if (target == item.value) {
				tempLabel = item.label + tempLabel;
			}
		});
		return tempLabel;
	};

	// 地址选择时禁用上级目录  需要配合设置禁用样式修改
	const disableTreeData = ref([]);
	const disableTreeDataFn = (data) => {
		let tempData = [];
		data.forEach((item) => {
			if (item.hasOwnProperty('children')) {
				tempData.push({
					value: item.value,
					label: item.label,
					disabled: true,
					children: disableTreeDataFn(item.children)
				});
				item.children.forEach((item1) => {
					item1.parentId = item.value;
				});
			} else {
				tempData.push({ value: item.value, label: item.label, disabled: false });
			}
		});
		return tempData;
	};
	disableTreeData.value = disableTreeDataFn(mapTree);

	onMounted(() => {
		// 初始获取项目关系的数据

		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			project_id.value = project_mess.project_id;
		} else {
			message.error('未选择项目，请重新选择！');
		}
		let operaType_local = localStorage.getItem('operaType');
		if (operaType_local) {
			operaType.value = operaType_local;
		}

		getProjectRelation();
	});
</script>
<style lang="less" scoped>
	.page_header_title {
		margin-bottom: 30px;
	}

	.footer_button {
		position: absolute;
		background-color: rgba(255, 255, 255, 1);
		border-top: 1px solid rgba(221, 226, 237, 1);
		bottom: 0;
		left: 50%;
		transform: translate(-50%, 0%);
		width: calc(100% - 64px);
		z-index: 999;
		height: 65px;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		border-bottom-left-radius: 8px;
		border-bottom-right-radius: 8px;
	}
	:deep(.ant-form-item) {
		.ant-form-item-label > label {
			min-width: 105px !important;
		}
	}
</style>
