import http from '/tool/http.js';

// 获取所有区域数据
export const getAllAreaHttp = (params) => {
	let url = '/api/basic/set/treeAll';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200 && data.dataList.length > 0) {
					resolve(data.dataList);
				} else {
					// 空数据
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 根据项目ID获取项目基本信息
export const getProjectDetailHttp = (params) => {
	let url = `/api/basic/set/getProjectDeail/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, {})
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					// 空数据
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 保存项目基本信息
export const saveProjectMessHttp = (params) => {
	let url = '/api/basic/set/saveProject';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data);
				} else {
					// 空数据
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 根据项目ID获取控制器列表
export const getEageDeviceListHttp = (params) => {
	let url = '/api/edge/findPage';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve({ total: data.data.total, list: data.data.records });
				} else {
					// 空数据
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 新增边缘控制器接口
export const addNewEdgeControlDeviceHttp = (params) => {
	let url = '/api/edge/addEdgeContro';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 编辑边缘控制器接口
export const editEdgeControlDeviceHttp = (params) => {
	let url = '/api/edge/updateEdgeContro';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 删除边缘控制器  /edge/deleteEdgeContro/{id}
export const deleteEdgeDeviceHttp = (params) => {
	let url = `/api/edge/deleteEdgeContro/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 获取控制器详情信息 根据设备类型查询控制器基本信息
export const getControlConnectDeviceDetail = (params) => {
	let url = '/api/edge/getEdgeDeail';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 获取控制器详情信息 根据设备类型查询控制器基本信息
export const getControlConnectDeviceList = (params) => {
	let url = '/api/edge/getEdgeDeail';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 新增控制器关联的设备的基本信息
export const addControlConnectDevice = (params) => {
	let url = '/api/edge/saveEdgeDev';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 根据字典查询
// 1. 查询元素字典  传参  ElEMENT_TYPE
// 2. 查询 对应元素的设备的 设备类型 传参 元素编码 GF GKB CN 等

export const getDictHttp = (params) => {
	let url = `/api/dict/findSimpleList/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, {})
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 新增边缘控制器元素
export const addElementHttp = (params) => {
	let url = '/api/edge/element/addControDevice';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 根据id查询边端控制器详情
export const getByIdEdgeDetail = (params) => {
	let url = `/api/edge/getById/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 修改边端控制器关联元素
export const editElementHttp = (params) => {
	let url = '/api/edge/element/updateControDevice';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 删除边端控制器关联元素
export const deleteElementHttp = (params) => {
	let url = `/api/edge/element/deleteControDevice/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, {})
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 元素关联设备新增
export const elementContactDeviceHttp = (params) => {
	let url = `/api/edge/element/relDevices`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 根据控制器编码缓存设备列表(注：在关联设备保存操作以后调用)
export const cacheDevices = (params) => {
	let url = `/api/edge/element/cacheDevices/${params.controCode}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 元素关联设备列表查询     分页查询元素关联设备列表
export const findDevicePageHttp = (params) => {
	let url = `/api/edge/element/findDevicePage`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data.records);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 按月保存分时时段信息
export const saveTimeByMonthHttp = (params) => {
	let url = `/api/elerate/saveTimeByMonth`;

	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 按月批量保存分时时段信息
export const saveTimeByMonthListHttp = (params) => {
	let url = `/api/elerate/saveTimeBatchByMonth`;

	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询当前年份下价格配置详情
export const priceDetailOnYearHttp = (params) => {
	let url = `/api/elerate/getDetailOnYear/${params.projectId}/${params.year}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, {})
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 同步去年分时时段信息
export const saveTimeSyncLastYearHttp = (params) => {
	let url = `/api/elerate/saveTimeSyncLastYear`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 根据配置id查询电价详情
export const getPriceDetailByElerateIdHttp = (params) => {
	let url = `/api/elerate/getPriceDetailByElerateId/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, {})
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 按月保存分时价格信息
export const savePriceByMonthHttp = (params) => {
	let url = `/api/elerate/savePriceByMonth`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 同步其他项目
export const syncOtherProjectHttp = (params) => {
	let url = `/api//elerate/copyProjectElerate`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};


// 根据配置id查询分时时段详情
export const getTimeDetailByElerateIdHttp = (params) => {
	let url = `/api/elerate/getTimeDetailByElerateId/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

//根据项目Id获取策略配置信息
export const getStrategyByProjectId = (params) => {
	let url = `/api/basic/set/getStrategyByProjectId/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// // 查询设备类型列表(用于关联设备页面)
// export const getDeviceTypeRelAssetSimpleList = (params) => {
//     let url = `/api/select/getDeviceTypeRelAssetSimpleList`;
//     return new Promise((resolve, reject) => {
//         http.post(url, params).then((data) => {
//             if (data.code == 200) {
//                 resolve(data.data)
//             } else {
//                 // 空数据
//                 reject({
//                     message: '数据为空'
//                 })
//             }
//         }).catch((errorInfo) => {
//             reject({
//                 message: errorInfo
//             })
//         });
//     })
// }
