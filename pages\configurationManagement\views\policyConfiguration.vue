<!-- 配置管理 ---  策略配置  -->
<template>
	<div class="page_all current_page">
		<!-- 页面主要内容 -->
		<div class="third_menu_contain">
			<div
				:class="['third_menu_item', currentThirdMenu == index ? 'active' : '']"
				@click="menuChange(item, index)"
				v-for="(item, index) in third_menu_list"
				:key="index"
			>
				<span class="third_menu_name">{{ item.name }}</span>
				<span class="third_menu_line"></span>
			</div>
		</div>
		<div class="main_contain main_contain_thirdMenu">
			<div class="main_contain_scroll">
				<!-- form 表单 -->
				<a-form
					id="deviceConfigurationFrom"
					ref="deviceConfigurationFrom"
					name="deviceConfigurationFrom"
					:model="form_state_systemsetting"
				>
					<a-form-item
						label="经济策略"
						name="economicStrategy "
						:rules="[
							{
								required: true,
								message: ' '
							}
						]"
					>
						<a-checkbox-group
							:options="economicStrAtegy"
							v-model:value="economicSelect"
							disabled
							style="margin-left: 10px; margin-top: 10px"
						/>
					</a-form-item>
					<a-form-item
						label="安全策略"
						name="economicStrategy "
						:rules="[
							{
								required: true,
								message: ' '
							}
						]"
					>
						<a-checkbox-group
							:options="policyStrAtegy"
							v-model:value="policySelect"
							disabled
							style="margin-left: 10px; margin-top: 10px"
						/>
					</a-form-item>
				</a-form>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, watch, reactive, onMounted } from 'vue';
	import { useRouter } from 'vue-router';
	import { message } from 'ant-design-vue';
	import { getDictHttp } from '/assets/js/commonApi.js';

	import { getStrategyByProjectId } from './api.js';

	const router = useRouter();

	// 三级菜单切换
	const third_menu_list = ref([
		{
			name: '项目信息',
			url: '/projectDetail'
		},
		{
			name: '项目设备',
			url: '/deviceConfiguration'
		},
		{
			name: '电价配置',
			url: '/electricPrice'
		},
		{
			name: '策略配置',
			url: '/policyConfiguration'
		}
	]);

	const currentThirdMenu = ref(3);

	const menuChange = (item, index) => {
		currentThirdMenu.value = index;
		router.push(item.url);
	};

	const economicStrAtegy = ref([]);
	const policyStrAtegy = ref([]);
	const project_id = ref(null);
	const economicSelect = ref([]);
	const policySelect = ref([]);

	onMounted(() => {
		let strategys = ['ECONOMIC_STRATEGY', 'SECURITY_POLICY'];
		let result1 = [];
		getDictHttp(strategys[0]).then((data) => {
			for (let index = 0; index < data.length; index++) {
				result1.push({
					label: data[index].dictValue,
					value: data[index].dictKey
				});
			}
			economicStrAtegy.value = result1;
		});

		let result2 = [];
		getDictHttp(strategys[1]).then((data) => {
			for (let index = 0; index < data.length; index++) {
				result2.push({
					label: data[index].dictValue,
					value: data[index].dictKey
				});
			}
			policyStrAtegy.value = result2;
		});

		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			project_id.value = project_mess.project_id;
		} else {
			message.error('未选择项目，请重新选择！');
		}
		let params = project_id.value;
		getStrategyByProjectId(params).then((data) => {
			if (JSON.stringify(data) !== '{}') {
				economicSelect.value = data.economicStrategy.split(',');
				policySelect.value = data.securityPolicy.split(',');
			}
		});
	});
</script>

<style lang="less" scoped>
	.current_page {
		padding-top: 32px;
		box-sizing: border-box;
	}

	:deep(.ant-checkbox-group-item) {
		width: 100px;
		margin-right: 10px;
	}
</style>
