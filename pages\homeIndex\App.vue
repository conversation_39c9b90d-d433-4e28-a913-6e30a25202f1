<template>
	<a-config-provider
		:theme="themeSelf"
		:locale="zhCN"
	>
		<!-- <Loading :has_data="has_data"></Loading> -->
		<Header></Header>
		<RouterView />
	</a-config-provider>
</template>
<script setup>
	import { ref } from 'vue';
	import { RouterView } from 'vue-router';
	import { themeSelf } from '/assets/js/theme.js';
	import zhCN from 'ant-design-vue/es/locale/zh_CN';
	import dayjs from 'dayjs';
	import 'dayjs/locale/zh-cn';
	dayjs.locale('zh-cn');
	import Header from '/components/Header.vue';
	// import Loading from '/components/Loading.vue'
	import { getCurrentInstance } from 'vue';
	import { onMounted, onBeforeUnmount } from 'vue';

	const { proxy } = getCurrentInstance();
	const handleResize = () => {
		proxy.$forceUpdate(); // 强制重新渲染组件
		// 另外一种实现全局刷新的方法
		window.location.reload();
	};

	onMounted(() => {
		window.addEventListener('resize', handleResize); // 添加监听
	});

	onBeforeUnmount(() => {
		window.removeEventListener('resize', handleResize); // 移除监听
	});

	const has_data = ref(true);

	setTimeout(() => {
		has_data.value = false;
	}, 3000);

	// const devicePixelRatio = window.devicePixelRatio // 获取下载的缩放 125% -> 1.25    150% -> 1.5
	// console.log(devicePixelRatio)
	// if (devicePixelRatio !== 1) {
	//   // 老ie可以提示
	//   if (!!window.ActiveXObject || 'ActiveXObject' in window) {
	//     alert('balabala,去用chrome吧')
	//   } else {
	//     const c = document.querySelector('body')
	//     c.style.zoom = 1 / devicePixelRatio // 修改页面的缩放比例
	//   }
	// }

	// created () {
	//     this.$nextTick(() => {
	//       this.resizeFun()
	//       window.addEventListener('resize', this.resizeFun)
	//     })
	//   },
	//   methods: {
	//     resizeFun () {
	//       const devicePixelRatio = window.devicePixelRatio // 获取下载的缩放 125% -> 1.25    150% -> 1.5
	//       if (devicePixelRatio !== 1) {
	//         // 如果在笔记本中用IE浏览器打开 则弹出提示
	//         if (!! window.ActiveXObject || 'ActiveXObject' in window) {
	//           alert('balabala。。。')
	//         } else {
	//           const c = document.querySelector('body')
	//           c.style.zoom = 1 / devicePixelRatio// 修改页面的缩放比例
	//         }
	//       }
	//     }
	//   }
</script>
<style scoped></style>
