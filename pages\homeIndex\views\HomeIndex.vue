<template>
	<div class="page_all home_index">
		<div class="main_contain">
			<div class="main_left">
				<div class="total_income">
					<div class="title">
						<span>累计收益</span>
						<div class="title_right">
							<div class="tab_mess_title_sub">
								<span>
									{{ formatNumber(total) }}
									<i class="unit">元</i>
								</span>
							</div>
							<span
								class="title_icon_arrow"
								@click="skipIncomeAnalysis"
							></span>
						</div>
					</div>
					<div class="map_chart_1">
						<div
							class="echart_pie_total"
							id="map_pie_sum"
						></div>
					</div>
				</div>

				<div class="current_month_income">
					<div class="title">
						<span>本月收益</span>
						<div class="title_right">
							<div class="tab_mess_title_sub">
								<span>
									{{ formatNumber(current_month_sum) }}
									<i class="unit">元</i>
								</span>
							</div>
							<span
								class="title_icon_arrow"
								@click="skipIncomeAnalysis"
							></span>
						</div>
					</div>
					<div class="percent_up_down">
						<div>
							<span>环比</span>
							<span>{{ formatNumber(last_month_percent) }}</span>
							<span>%</span>
							<span v-if="last_month_percent != '-' ? true : false">
								<span
									v-if="last_month_percent > 0 ? true : false"
									class="up_img"
								></span>
								<span
									v-else-if="last_month_percent < 0 ? true : false"
									class="down_img"
								></span>
							</span>
						</div>
						<div>
							<span>同比</span>
							<span>{{ formatNumber(last_year_percent) }}</span>
							<span>%</span>
							<span v-if="last_year_percent != '-' ? true : false">
								<span
									v-if="last_year_percent > 0 ? true : false"
									class="up_img"
								></span>
								<span
									v-else-if="last_year_percent < 0 ? true : false"
									class="down_img"
								></span>
							</span>
						</div>
					</div>
					<span
						class="unit"
						v-if="!hasMonthPrice"
					>
						元
					</span>
					<div class="map_chart_2">
						<span
							class="map_unit"
							v-if="hasMonthPrice"
						>
							元
						</span>
						<div
							class="ehcart_line_month"
							id="ehcart_line_month"
							v-if="hasMonthPrice"
						></div>
						<a-empty
							v-else
							style="margin: 10% auto"
						/>
					</div>
				</div>
				<!-- <div class="title">
          <span
            >收益分析
          </span>
          <span class="title_icon_arrow" @click="skipIncomeAnalysis"></span>
        </div> -->
				<!-- <div class="message_contain" v-if="message_show_hide">
          <div class="message_1 message" @click="skipPrice">
            <img src="/assets/icon/tips_right.png" alt="提示：" />
            <div class="mess">请维护电价</div>
            <div class="message_close" @click.stop="closeMessage"></div>
          </div>
        </div> -->

				<!-- <div class="total_count tab_title">
          <div class="left_bar_line"></div>
          <div class="tab_mess_title">
            <span>累计收益</span>
            <div class="tab_mess_title_sub">
              <span
                >{{ formatNumber(total_sum) }}
                <i class="unit">元</i>
              </span>
            </div>
          </div>
        </div> -->

				<!-- <div class="map_chart_1">
          <div class="echart_pie_total" id="map_pie_sum"></div>
        </div> -->

				<!-- <div class="total_count tab_title">
          <div class="left_bar_line"></div>
          <div class="tab_mess_title">
            <span>本月收益</span>
            <div class="tab_mess_title_sub">
              <span
                >{{ formatNumber(current_month_sum) }}
                <i class="unit">元</i>
              </span>
            </div>
          </div>
        </div> -->

				<!-- <div class="percent_up_down">
          <div>
            <span>环比</span>
            <span>{{ formatNumber(last_month_percent) }}</span>
            <span>%</span>
            <span v-if="last_month_percent != '-' ? true : false">
              <span v-if="last_month_percent > 0 ? true : false" class="up_img"></span>
              <span
                v-else-if="last_month_percent < 0 ? true : false"
                class="down_img"
              ></span>
            </span>
          </div>
          <div>
            <span>同比</span>
            <span>{{ formatNumber(last_year_percent) }}</span>
            <span>%</span>
            <span v-if="last_year_percent != '-' ? true : false">
              <span v-if="last_year_percent > 0 ? true : false" class="up_img"></span>
              <span
                v-else-if="last_year_percent < 0 ? true : false"
                class="down_img"
              ></span>
            </span>
          </div>
        </div> -->

				<!-- <div class="map_chart_2">
          <div class="ehcart_line_month" id="ehcart_line_month"></div>
        </div> -->
			</div>
			<div class="main_center">
				<div class="center_top">
					<div class="title">
						<span>
							微网实时功率
							<i>（{{ dayjs().format('YYYY-MM-DD HH:mm:ss') }}）</i>
						</span>
						<span
							class="show_card_pic"
							@click="showCardPic"
						>
							<img
								src="/assets/icon/index_card.png"
								alt="项目拓扑图"
							/>
							项目拓扑图
						</span>
						<a-modal
							wrapClassName="modal_card"
							v-model:open="modal_card"
							:centered="true"
							:footer="null"
							title="拓扑图"
							:getContainer="false"
						>
							<CloudUploadOutlined
								style="font-size: 30px; position: absolute; right: 30px"
								@click="clickUpload"
							/>
							<img
								class="tuopu"
								:src="topologyImgUrl"
								alt=""
								v-if="topologyImgUrl"
							/>
							<div
								class="tuopu"
								v-else
							></div>
						</a-modal>
						<a-modal
							wrapClassName="modal_img"
							v-model:open="modal_img"
							:centered="true"
							title="裁剪图片"
							:getContainer="false"
							style="width: 800px !important; height: 800px !important"
							@ok="handleUploadCrop"
						>
							<div style="height: 600px">
								<vue-cropper
									ref="cropper"
									:guides="false"
									:img="imageSrc"
									:min-container-width="250"
									:outputSize="option.outputSize"
									:outputType="option.outputType"
									:info="option.info"
									:full="option.full"
									:canMove="option.canMove"
									:canMoveBox="option.canMoveBox"
									:original="option.original"
									:autoCrop="option.autoCrop"
									:autoCropWidth="option.autoCropWidth"
									:autoCropHeight="option.autoCropHeight"
									:fixedBox="option.fixedBox"
									:min-container-height="100"
									:min-canvas-width="100"
									:min-canvas-height="50"
									@cropend="handleCrop"
								/>
							</div>
						</a-modal>
						<span
							class="title_icon_arrow"
							@click="skipDevice"
						></span>
					</div>

					<div class="static_topology">
						<div class="level_first">
							<div class="level_first_left">
								<div class="unit_first unit_item">
									<img
										class="unit_item_img"
										src="/assets/icon/dianwang.png"
										alt=""
									/>
									<span class="unit_item_name">电网</span>
								</div>
								<div
									class="unit_item_line"
									style="width: 80px"
								>
									<span></span>
									<span></span>
									<span></span>
									<span></span>
								</div>
								<div class="unit_second unit_item">
									<img
										class="unit_item_img"
										src="/assets/icon/guankoubiao.png"
										alt=""
									/>
									<span class="unit_item_name">关口表</span>
								</div>
							</div>
							<div class="level_first_right">
								<div class="unit_third unit_item">
									<img
										class="unit_item_img"
										src="/assets/icon/fuhe.png"
										alt=""
									/>
									<span class="unit_item_name">负荷</span>
								</div>
							</div>
						</div>
						<div class="level_second">
							<div class="level_second_left">
								<div
									class="unit_item_line unit_item_line_cloumn line_cloumn_1"
									style="width: 75px"
								>
									<span></span>
									<span></span>
									<span></span>
									<span></span>
									<div
										v-if="topology_obj.arrow_gkb"
										:class="[
											'orange_arrow',
											topology_obj.arrow_gkb == 'in' ? 'orange_arrow_down' : '',
											topology_obj.arrow_gkb == 'out' ? 'orange_arrow_up' : ''
										]"
									>
										<img
											src="/assets/icon/orange_arrow.png"
											alt=""
										/>
									</div>
								</div>
								<div class="unit_mess_contain">
									<div class="unit_mess">
										<div class="unit_mess_icon">
											<img
												src="/assets/icon/power_icon.png"
												alt=""
											/>
										</div>
										<div>
											功率
											<span>{{ topology_obj.power_gkb }}</span>
											kW
										</div>
									</div>
								</div>
							</div>
							<div class="level_second_right">
								<div class="unit_mess_contain">
									<div class="unit_mess">
										<div class="unit_mess_icon">
											<img
												src="/assets/icon/power_icon.png"
												alt=""
											/>
										</div>
										<div>
											功率
											<span>{{ topology_obj.power_fh }}</span>
											kW
										</div>
									</div>
								</div>
								<div
									class="unit_item_line unit_item_line_cloumn line_cloumn_1"
									style="width: 75px"
								>
									<span></span>
									<span></span>
									<span></span>
									<span></span>
									<div
										v-if="topology_obj.arrow_fh"
										:class="[
											'orange_arrow',
											topology_obj.arrow_fh == 'in' ? 'orange_arrow_down' : '',
											topology_obj.arrow_fh == 'out' ? 'orange_arrow_up' : ''
										]"
									>
										<img
											src="/assets/icon/orange_arrow.png"
											alt=""
										/>
									</div>
								</div>
							</div>
						</div>
						<div
							class="unit_item_line divid_line"
							style="width: 620px"
						>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
						</div>
						<div class="level_third">
							<div class="level_third_left">
								<div class="unit_mess_contain">
									<div class="unit_mess">
										<div class="unit_mess_icon">
											<img
												src="/assets/icon/power_icon.png"
												alt=""
											/>
										</div>
										<div>
											功率
											<span>{{ topology_obj.power_gf }}</span>
											kW
										</div>
									</div>
								</div>
								<div
									class="unit_item_line unit_item_line_cloumn line_cloumn_1"
									style="width: 75px"
								>
									<span></span>
									<span></span>
									<span></span>
									<span></span>
									<div
										v-if="topology_obj.arrow_gf"
										:class="[
											'orange_arrow',
											topology_obj.arrow_gf == 'out' ? 'orange_arrow_down' : '',
											topology_obj.arrow_gf == 'in' ? 'orange_arrow_up' : ''
										]"
									>
										<img
											src="/assets/icon/orange_arrow.png"
											alt=""
										/>
									</div>
								</div>
							</div>
							<div class="level_third_right">
								<div
									class="unit_item_line unit_item_line_cloumn line_cloumn_1"
									style="width: 75px"
								>
									<span></span>
									<span></span>
									<span></span>
									<span></span>
									<div
										v-if="topology_obj.arrow_cn"
										:class="[
											'orange_arrow',
											topology_obj.arrow_cn == 'out' ? 'orange_arrow_down' : '',
											topology_obj.arrow_cn == 'in' ? 'orange_arrow_up' : ''
										]"
									>
										<img
											src="/assets/icon/orange_arrow.png"
											alt=""
										/>
									</div>
								</div>
								<div class="unit_mess_contain">
									<div class="unit_mess">
										<div class="unit_mess_icon">
											<img
												src="/assets/icon/power_icon.png"
												alt=""
											/>
										</div>
										<div>
											功率
											<span>{{ topology_obj.power_cn }}</span>
											kW
										</div>
									</div>
									<div class="unit_mess">
										<div class="unit_mess_icon">
											<img
												src="/assets/icon/soc_icon.png"
												alt=""
											/>
										</div>
										<div>
											SOC
											<span>{{ topology_obj.soc_cn }}</span>
											%
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="level_fouth">
							<div class="level_fouth_left">
								<div class="unit_item">
									<img
										class="unit_item_img"
										src="/assets/icon/guangfu_icon.png"
										alt=""
									/>
									<span class="unit_item_name">
										光伏
										<span>{{ topology_obj.capacity_gf }}</span>
										kWp
									</span>
								</div>
							</div>
							<div class="level_fouth_right">
								<div class="unit_third unit_item">
									<img
										class="unit_item_img"
										src="/assets/icon/chuneng_icon.png"
										alt=""
									/>
									<span class="unit_item_name">
										储能
										<span>{{ topology_obj.real_demand }}</span>
										kW /
										<span>{{ topology_obj.capacity_cn }}</span>
										kWh
									</span>
								</div>
							</div>
						</div>
					</div>

					<!-- 旧版本的类型 -->
					<div
						style="display: none"
						:class="['map_change_5', 'item_num_' + topology_length]"
					>
						<div
							class="map_center_list_top"
							v-if="topology_list_top.length == 0 ? false : true"
						>
							<div
								v-for="(item, index) in topology_list_top"
								:key="index"
								placement="right"
								:class="['card_list', item.elementCode]"
								@click="skipElement(item)"
							>
								<div class="mess_card_list">
									<a-tooltip placement="right">
										<template #title>
											<div style="width: 300px">
												<div
													v-for="(item1, index1) in item.paramList"
													:key="index1"
													style="display: inline-block; width: 50%"
												>
													<div class="tp_mess">
														<span>
															{{ item1.paramName }}:{{
																item1.value ? formatNumber(parseFloat(item1.value).toFixed(2)) : '-'
															}}
															{{ item1.unit }}
														</span>
													</div>
												</div>
											</div>
										</template>
										<span>{{ item.elementCodeDesc }}</span>
									</a-tooltip>
									<span class="card_center_list">
										{{ formatNumber(item.yougong) }}
										<i>{{ item.unit_1 }}</i>
									</span>
									<span class="card_bottom_list">
										<span v-if="item.capacity_1">
											功率
											<i>{{ formatNumber(item.capacity_1) }}</i>
											{{ item.unit_1 }}
											<i
												class="card_bottom_center_line"
												v-if="item.list_length == 2 ? true : false"
											>
												/
											</i>
											<i v-if="item.list_length == 2 ? true : false">
												{{ formatNumber(item.capacity_2) }}
											</i>
											<span v-if="item.list_length == 2 ? true : false">{{ item.unit_2 }}</span>
										</span>
									</span>
								</div>
								<div class="icon_img card_img">
									<span v-if="item.elementCode == 'CN'">soc</span>
									<span v-if="item.elementCode == 'CN'">80%</span>
								</div>
								<div class="map_line_border">
									<div :class="[item.upArrow == 1 ? 'arrow_in' : '']"></div>
									<div :class="[item.downArrow == 1 ? 'arrow_out' : '']"></div>
								</div>
							</div>
						</div>
						<div
							class="map_center_line"
							v-if="topology_list_top.length == 0 ? false : true"
						></div>
						<div
							class="map_center_list_bottom"
							v-if="topology_list_top.length == 0 ? false : true"
						>
							<div
								v-for="(item, index) in topology_list_bottom"
								:key="index"
								:class="['card_list', item.elementCode]"
								@click="skipElement(item)"
							>
								<div class="mess_card_list">
									<a-tooltip placement="right">
										<template #title>
											<div style="width: 300px">
												<div
													v-for="(item1, index1) in item.paramList"
													:key="index1"
													style="display: inline-block; width: 50%"
												>
													<div class="tp_mess">
														<span>
															{{ item1.paramName }}:{{
																item1.value ? formatNumber(parseFloat(item1.value).toFixed(2)) : '-'
															}}
															{{ item1.unit }}
														</span>
													</div>
												</div>
											</div>
										</template>
										<span>{{ item.elementCodeDesc }}</span>
									</a-tooltip>
									<span class="card_center_list">
										{{ formatNumber(item.yougong) }}
										<i>{{ item.unit_1 }}</i>
										<span v-if="item.elementCode == 'CN'">
											<span class="card_center_list_sub1">&nbsp;SOC</span>
										</span>
										<span v-if="item.elementCode == 'CN'">
											<span class="card_center_list_sub">
												{{ formatNumber(item.soc) }}
											</span>
											<i>%</i>
										</span>
									</span>

									<span class="card_bottom_list">
										<span v-if="item.capacity_1">
											功率
											<i>{{ formatNumber(item.capacity_1) }}</i>
											{{ item.unit_1 }}
											<i
												class="card_bottom_center_line"
												v-if="item.list_length == 2 ? true : false"
											>
												/
											</i>
											<i v-if="item.list_length == 2 ? true : false">{{ item.capacity_2 }}</i>
											<span v-if="item.list_length == 2 ? true : false">{{ item.unit_2 }}</span>
										</span>
									</span>
								</div>
								<div class="icon_img card_img"></div>
								<div class="map_line_border">
									<div :class="[item.upArrow == 1 ? 'arrow_in' : '']"></div>
									<div :class="[item.downArrow == 1 ? 'arrow_out' : '']"></div>
								</div>
							</div>
						</div>
						<a-empty
							v-if="topology_list_top.length == 0 ? true : false"
							style="margin: 10% auto"
						/>
					</div>
				</div>
				<div class="center_bottom">
					<div class="tab_change_list">
						<div class="title">
							{{ tab_change_list[0].name }}
						</div>
						<!-- <div
              :class="['tab_change_item', currentThirdMenu == item.value ? 'active' : '']"
              @click="menuChange(item.value)"
              v-for="(item, index) in tab_change_list"
              :key="index"
            >
              <span class="tab_change_name">{{ item.name }}</span>
              <span class="tab_change_line"></span>
            </div> -->
					</div>
					<div class="select_arrow">
						<a-select
							ref="select"
							v-model:value="time_select"
							style="width: 120px"
							:getPopupContainer="(e) => e.parentNode"
							@select="changeTime"
						>
							<a-select-option value="1">5分钟</a-select-option>
							<a-select-option value="2">15分钟</a-select-option>
							<a-select-option value="3">1小时</a-select-option>
						</a-select>
						<img
							@click="skipOverview"
							src="/assets/icon/table_arrow.png"
							alt="详情"
						/>
					</div>
					<div class="map_center_two">
						<!-- <span class="unit_mess">kW</span> -->
						<div
							:style="{ 'z-index': currentThirdMenu == '0' ? 9 : 0 }"
							id="map_charts_power"
							v-if="hasPowerData"
						></div>
						<!-- <div :style="{ 'z-index': currentThirdMenu == '1' ? 9 : 0 }" id="map_charts_elec">
              电量
            </div> -->
						<a-empty v-else />
					</div>
				</div>
			</div>
			<div class="main_right">
				<div
					style=""
					class="item_1"
				>
					<div class="title">
						<span>快捷入口</span>
						<!-- <span class="title_icon_arrow"></span> -->
					</div>
					<div class="skip_list">
						<div
							class="skip_list_item"
							v-for="(item, index) in skip_list_data"
							:key="index"
							@click="skipMenu(item)"
						>
							{{ item.name }}
						</div>
					</div>
				</div>
				<div class="item_2">
					<div class="title">
						<span>异常统计</span>
						<span
							class="title_icon_arrow"
							@click="skipUnnormal"
						></span>
					</div>
					<div class="unnormal_mess">
						<div class="mess_left">
							<div class="left_img"></div>
							<span>
								{{ formatNumber(total_unnormal) }}
								<i>条</i>
							</span>
							<span>异常总数</span>
						</div>
						<div class="mess_right">
							<div
								class="status_list"
								v-for="(item, index) in unnormal_list"
								:key="index"
							>
								<div>
									<span>{{ item.warnStatusDesc }}({{ formatNumber(item.totalNum) }}条)</span>
									<span>
										{{ item.percent }}
										<i>%</i>
									</span>
								</div>
								<div class="percent_status">
									<span :style="{ width: item.percent + '%' }"></span>
								</div>
							</div>
						</div>
					</div>
					<div class="warning_title_head">
						<div>序号</div>
						<div>告警等级</div>
						<div>异常事件</div>
					</div>

					<div class="warning_list_contain">
						<div
							:class="['warning_list_item_contain', { scrollBoxRanklist: warnn_list.length > 5 }]"
						>
							<div
								class="warning_list_item"
								v-for="(item, index) in warnn_list"
								:key="index"
							>
								<div>{{ index + 1 }}</div>
								<div>
									<span
										:class="[
											item.warnLevel == '1' ? 'error_tag' : '',
											item.warnLevel == '2' ? 'warning_tag' : '',
											item.warnLevel == '3' ? 'normal_tag' : '',
											item.warnLevel == '4' ? 'prompt_tag' : ''
										]"
									>
										{{ item.warnLevelDesc }}
									</span>
								</div>
								<a-tooltip
									placement="top"
									style="position: relative; top: -20px"
								>
									<template #title>
										{{ item.warnContent }}
									</template>
									<div>{{ item.warnContent }}</div>
								</a-tooltip>
							</div>
						</div>
						<div
							:class="['warning_list_item_contain', { scrollBoxRanklist1: warnn_list.length > 5 }]"
							v-if="warnn_list.length > 5"
						>
							<div
								class="warning_list_item"
								v-for="(item, index) in warnn_list"
								:key="index"
							>
								<div>{{ index + 1 }}</div>
								<div>
									<span
										:class="[
											item.warnLevel == '1' ? 'error_tag' : '',
											item.warnLevel == '2' ? 'warning_tag' : '',
											item.warnLevel == '3' ? 'normal_tag' : '',
											item.warnLevel == '4' ? 'prompt_tag' : ''
										]"
									>
										{{ item.warnLevelDesc }}
									</span>
								</div>

								<a-tooltip placement="top">
									<template #title>
										{{ item.warnContent }}
									</template>
									<div>{{ item.warnContent }}</div>
								</a-tooltip>
							</div>
						</div>
						<a-empty
							v-if="warnn_list.length == 0 ? true : false"
							style="margin: 10% auto"
						/>
					</div>
				</div>
			</div>
		</div>
		<div></div>
	</div>
</template>

<script setup>
	import { onMounted, ref, markRaw, watch, defineComponent, reactive } from 'vue';
	import { formatNumber } from '/tool/numberFormat.js';
	import { getObjectUrl } from '/assets/js/commonApi.js';

	import {
		checkEleratePriceSetting,
		sumBenefitAmountByType,
		monthBenefitAmountByType,
		statTotalBenefitByDay,
		statTotalBenefitByMonth,
		statElementLatest,
		statActivePower,
		statPositiveAndReverseActivePower,
		countWarnNumByWarnStatus,
		findWarnPage,
		saveProjectTopology,
		getProjectTopology
	} from './api.js';
	import { CloudUploadOutlined } from '@ant-design/icons-vue';
	import {
		init_x_y,
		handleEchartsLineBg,
		handleLineMapOptionSingal,
		handleBarMapSelection
	} from '/assets/js/commonTool.js';
	// import { colorList } from "/assets/js/echartsConfig.js";
	import { message } from 'ant-design-vue';
	import * as echarts from 'echarts';
	import dayjs from 'dayjs';
	import { eventBus } from '/assets/js/eventBus'; // 导入事件总线实例
	import 'vue-cropper/dist/index.css';
	import { VueCropper } from 'vue-cropper';
	import { ProTopologyImageUpload } from '/components/upload.js';

	// 图片组件的方法
	const option = reactive({
		img: '', // 裁剪图片的地址
		info: true, // 裁剪框的大小信息
		outputSize: 1, // 剪切后的图片质量（0.1-1）
		full: true, // 输出原图比例截图 props名full
		outputType: 'png', // 裁剪生成的图片的格式
		canMove: true, // 能否拖动图片
		original: false, // 上传图片是否显示原始宽高
		canMoveBox: true, // 能否拖动截图框
		autoCrop: true, // 是否默认生成截图框
		autoCropWidth: 615, // 默认生成截图框宽度
		autoCropHeight: 404, // 默认生成截图框高度
		fixedBox: true // 截图框固定大小
	});

	const handleUploadCrop = () => {
		cropper.value.getCropData(async (dataUrl) => {
			const blob = convertBase64UrlToBlob(dataUrl);
			const file = new File([blob], 'cropped-image.jpg', { type: 'image/jpeg' });
			console.log(file);
			let tempFormData = new FormData(); // 声明formData数据类型
			tempFormData.append('file', file);
			let imgMess = await ProTopologyImageUpload(tempFormData);
			console.log(imgMess);
			if (imgMess.success) {
				message.success('上传成功');
				saveProjectTopology(imgMess.data, Number(project_id.value)).then((data) => {
					console.log(data, '保存成功,查看保存的地址');
					modal_img.value = false;
					getTopologyImgUrl();
				});
			} else {
				message.error(imgMess.msg);
			}
		});
	};

	const hasMonthPrice = ref(true);
	const hasPowerData = ref(true);

	// 将base64的图片转换为file文件
	const convertBase64UrlToBlob = (urlData) => {
		const bytes = window.atob(urlData.split(',')[1]);
		const ab = new ArrayBuffer(bytes.length);
		const ia = new Uint8Array(ab);
		for (let i = 0; i < bytes.length; i++) {
			ia[i] = bytes.charCodeAt(i);
		}
		return new Blob([ab], { type: 'image/jpeg' });
	};

	const imageSrc = ref(null);
	const cropper = ref(null);

	function handleCrop(data) {
		// data 是裁剪后的图片信息
		console.log(data);
		// 处理裁剪后的图片，例如上传到服务器
	}

	// --------------------------------------------------------------

	//-----------------------------------全局参数------------------------

	const project_id = ref(null);
	const projectTitle = ref(null);
	const skipElement = (item) => {
		switch (item.elementCode) {
			case 'GKB':
				eventBus.emit('menu-change', [1, 0, '/pages/operationMonitoring/#/analysis/analysis_all']); // 触发事件
				break;
			case 'GF':
				eventBus.emit('menu-change', [1, 0, '/pages/operationMonitoring/#/analysis/analysis_gf']); // 触发事件
				break;
			case 'FH':
				eventBus.emit('menu-change', [1, 0, '/pages/operationMonitoring/#/analysis/analysis_yn']); // 触发事件
				break;
			case 'CN':
				eventBus.emit('menu-change', [1, 0, '/pages/operationMonitoring/#/analysis/analysis_cn']); // 触发事件
				break;
			case 'CDZ':
				eventBus.emit('menu-change', [1, 0, '/pages/operationMonitoring/#/analysis/analysis_cdz']); // 触发事件
				break;
			default:
				eventBus.emit('menu-change', [1, 0, '/pages/operationMonitoring/#/analysis/analysis_all']); // 触发事件
				break;
		}
	};

	const clickUpload = () => {
		const input = document.createElement('input');
		input.type = 'file';
		input.accept = 'image/*'; //打开文件选择时，会自动过滤掉非图片类型的文件
		input.click();

		input.onchange = (e) => {
			const file = input.files[0];
			if (file) {
				if (!file.type.startsWith('image/')) {
					message.error('仅支持png、jpeg、jpg等图片格式，图片大小限制10M');
					e.target.value = '';
				} else if (file.size > 10 * 1024 * 1024) {
					message.error('仅支持png、jpeg、jpg等图片格式，图片大小限制10M');
					// 清除文件选择
					e.target.value = '';
				} else {
					// 创建FileReader读取图片
					const reader = new FileReader();
					reader.onload = (e) => {
						imageSrc.value = e.target.result;
					};
					reader.readAsDataURL(file);
					modal_img.value = true;
				}
			}
		};
	};

	const getId = (index) => {
		return document.getElementsByClassName('mySwiper_mess')[index];
	};
	//-----------------------------------全局参数------------------------

	// 跳转收益分析的页面
	const skipIncomeAnalysis = () => {
		eventBus.emit('menu-change', [2, 0, '/pages/incomeAnalysis/#/incomeAnalysis_all']); // 触发事件
	};

	const skipUnnormal = () => {
		eventBus.emit('menu-change', [1, 2, '/pages/operationMonitoring/#/unNormalControl']); // 触发事件
	};
	//-----------------------------------判断电价是否配置------------------------

	const message_show_hide = ref(false);
	const message_list = ref(false);
	const getMessage = () => {
		let params = project_id.value;
		checkEleratePriceSetting(params)
			.then((data) => {
				if (data == 0) {
					message_show_hide.value = true;
				} else {
					message_show_hide.value = false;
				}
			})
			.catch((err) => {
				message.error(err.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};
	const closeMessage = () => {
		//////console.log(1111)
		message_show_hide.value = false;
	};
	const skipPrice = () => {
		eventBus.emit('menu-change', [4, 0, '/pages/configurationManagement/#/electricPrice']); // 触发事件
	};

	//-----------------------------------判断电价是否配置------------------------

	// ------------------------------------------累计收益------------------------------

	const sum_list = ref([]);
	const map_pie_sum = ref();
	const total_sum = ref(0);
	const total = ref(0);
	const getSum = () => {
		let params = project_id.value;
		sumBenefitAmountByType(params)
			.then((data) => {
				const allTotalAmountsAreNull = data.benefitList.every((item) => item.totalAmount === null);
				if (data.benefitList.length == 0 || allTotalAmountsAreNull) {
					// 数据为空
					data = [
						{
							totalAmount: 0,
							type: 1,
							typeDesc: '光伏收益'
						},
						{
							totalAmount: 0,
							type: 9,
							typeDesc: '储能峰谷套利'
						}
					];
					handlesumData(data);
				} else {
					handlesumData(data.benefitList);
				}

				// if (data.benefitList.length > 0) {
				//   handlesumData(data.benefitList)
				// } else {
				//   message.warning('累计收益为空！')
				// }
			})
			.catch((err) => {
				message.error(err.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	const colorList = {
		光伏收益: '#2DCDFC',
		需量优化: '#03E3A1',
		储能峰谷套利: '#7C8DFF',
		余光入储: '#D59DFD',
		现货交易: '#FEB56E',
		需求侧响应: '#1181FD'
	};

	const handlesumData = (data) => {
		let temp_series_data = [];
		for (let i = 0; i < data.length; i++) {
			if (data[i].type == 0) {
				total.value = data[i].totalAmount;
			}
			if (data[i].typeDesc == '光伏发电') {
				data[i].typeDesc = '光伏收益';
			}
			if (data[i].type != 0 && data[i].totalAmount >= 0) {
				// total_sum.value = parseFloat(data[i].totalAmount).toFixed(2)
				total_sum.value = total_sum.value + data[i].totalAmount;
				// break
			}
		}
		total_sum.value = parseFloat(total_sum.value).toFixed(6);
		for (let i = 0; i < data.length; i++) {
			let percnet = parseFloat(
				Number((data[i].totalAmount / Number(total_sum.value)).toFixed(4)) * 100
			).toFixed(1);
			if (Number(total_sum.value) == 0) {
				percnet = 0;
			}
			if (data[i].type != 0 && data[i].totalAmount >= 0) {
				temp_series_data.push({
					value: parseFloat(data[i].totalAmount).toFixed(2),
					name: data[i].typeDesc,
					label: {
						formatter: [`{a|${data[i].typeDesc}}`, `{b|${percnet}%}`].join('\n'),
						rich: {
							a: {
								color: '#092649',
								fontSize: 14,
								lineHeight: 20,
								fontFamily: 'SourceHanSansCN-Regular',
								textAlign: 'left'
							},
							b: {
								color: '#092649',
								fontSize: 14,
								lineHeight: 20,
								fontFamily: 'SourceHanSansCN-Regular',
								textAlign: 'left'
							}
						}
					},
					labelLine: {
						show: true,
						smooth: false,
						// length: 15,
						// length2: 24,

						lineStyle: {
							type: 'dashed',
							color: colorList[data[i].typeDesc]
						}
					},
					itemStyle: {
						color: colorList[data[i].typeDesc]
					}
				});
			}
		}
		let option = {
			tooltip: {
				trigger: 'item',
				confine: true,
				formatter: function (params) {
					let value = params.value;
					if (value != 0 && !value) {
						value = '-';
					} else {
						value = Number(parseFloat(params.value).toFixed(2));
						value = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
					}
					return `<div ><span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${params.color};"></span>
	                       ${params.name}: ${value}元 ${params.percent.toFixed(1)}%
	                   </div>`;
				}
			},
			legend: { show: false },
			grid: {
				left: 40,
				right: 0,
				top: 0,
				bottom: 50
			},
			series: [
				{
					name: '',
					type: 'pie',
					radius: [51, 74],
					// radius: ['42%', '60%'],
					itemStyle: {
						borderWidth: 3, // 设置每个扇区之间的间隙宽度为 3px
						borderColor: '#fff' // 设置间隙颜色为白色
					},
					avoidLabelOverlap: true,

					labelLine: {
						smooth: false,
						show: true
						// length:5,
						// length2: 24,
						// lineStyle: {
						//   color: 'red'
						// }
					},
					emphasis: {
						itemStyle: {
							// shadowBlur: 10,
							// shadowOffsetX: 0,
							// shadowColor: 'rgba(0, 0, 0, 0.5)',
							borderWidth: 0 // 悬浮时移除边框
						}
					},
					data: temp_series_data
				}
			]
		};
		if (map_pie_sum.value) {
			map_pie_sum.value.dispose();
		}
		map_pie_sum.value = markRaw(echarts.init(document.getElementById('map_pie_sum')));

		map_pie_sum.value.setOption(option);
	};

	// ----------------------------------------------累计收益-------------------------------

	// ----------------------------------------------本月收益-------------------------------
	const mont_total = ref(0);
	const map_ecahrt_month = ref();
	const getMonthTotal = () => {
		let params = project_id.value;
		monthBenefitAmountByType(params)
			.then((data) => {
				handleMonthData(data);
			})
			.catch((err) => {
				message.error(err.message);
				setTimeout(() => {
					message.destroy();
				}, 2000);
			});
	};
	const current_month_sum = ref('-');
	const last_month_sum = ref(0);
	const last_month_percent = ref('-');
	const last_year_sum = ref(0);
	const last_year_percent = ref('-');
	const handleMonthData = (data) => {
		// let data_x = []
		// let data_y_1 = []
		// let data_y_2 = []

		// 处理本月的数据
		let month_sum = 0;
		current_month_sum.value == '-'
			? (month_sum = 0)
			: (month_sum = Number(current_month_sum.value));
		for (let i = 0; i < data.currentMonthList.length; i++) {
			let item = data.currentMonthList[i];
			// data_x.push(i + 1 + '天')
			// data_y_1.push(item.totalAmount)
			if (item.type == 0) {
				month_sum = item.totalAmount;
			}
		}
		current_month_sum.value = parseFloat(month_sum).toFixed(2);
		for (let i = 0; i < data.lastMonthList.length; i++) {
			let item = data.lastMonthList[i];
			// data_y_2.push(item.totalAmount)
			// last_month_sum.value = Number(last_month_sum.value) + item.totalAmount
			if (item.type == 0) {
				last_month_sum.value = item.totalAmount;
			}
		}
		for (let i = 0; i < data.lastYearList.length; i++) {
			let item = data.lastYearList[i];
			// last_year_sum.value = Number(last_year_sum.value) + item.totalAmount;
			if (item.type == 0) {
				last_year_sum.value = item.totalAmount;
			}
		}

		// 计算环比
		if (
			last_month_sum.value == 0 ||
			last_month_sum.value == null ||
			current_month_sum.value == null
		) {
			last_month_percent.value = '-';
		} else {
			last_month_percent.value = parseFloat(
				(100 * (current_month_sum.value - last_month_sum.value)) / last_month_sum.value
			).toFixed(2);
		}

		// 计算同比
		//console.log(last_year_sum.value)
		if (
			last_year_sum.value == 0 ||
			last_year_sum.value == null ||
			current_month_sum.value == null
		) {
			last_year_percent.value = '-';
		} else {
			last_year_percent.value = parseFloat(
				(100 * (current_month_sum.value - last_year_sum.value)) / last_year_sum.value
			).toFixed(2);
			//console.log(last_year_percent.value)
		}

		if (data.currentMonthList.length == 0) {
			// 本月数据为空
			current_month_sum.value = '-';
		}
		// let legend_data = ['本月', '上月']
		// let option = {
		//   backgroundColor: 'transparent',
		//   legend: {
		//     show: true,
		//     type: 'scroll',
		//     center: true,
		//     top: 0,
		//     width: 920,
		//     data: legend_data,
		//     itemWidth: 24,
		//     itemGap: 80,
		//     align: 'auto',
		//     textStyle: { color: '#092649' }
		//   },
		//   dataZoom: [
		//     { type: 'inside', start: 0, end: 30 },
		//     { start: 0, end: 30 }
		//   ],
		//   tooltip: {
		//     trigger: 'axis',
		//     extraCssText:
		//       'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;'
		//   },
		//   grid: { top: 80, containLabel: true, left: '0%', right: 0, bottom: 20 },
		//   xAxis: [
		//     {
		//       show: true,
		//       type: 'category',
		//       axisTick: { show: false },
		//       axisLine: { show: true, lineStyle: { type: 'dashed', color: '#DFE3EC' } },
		//       splitLine: {
		//         show: true,
		//         lineStyle: { type: 'dashed', color: ['transparent'], width: 1 }
		//       },
		//       axisLabel: {
		//         inside: false,
		//         color: '#9CA2AF',
		//         fontWeight: '100',
		//         fontSize: '14',
		//         fontFamily: 'shsr',
		//         lineHeight: 14,
		//         margin: 10
		//       },
		//       data: data_x
		//     }
		//   ],
		//   yAxis: [
		//     {
		//       type: 'value',
		//       name: '',
		//       offset: -2,
		//       nameTextStyle: {
		//         color: '#9CA2AF',
		//         fontSize: 14,
		//         lineHeight: 40,
		//         fontWeight: 100,
		//         align: 'left',
		//         fontFamily: 'shsr',
		//         padding: [0, 0, 0, 0],
		//         width: 100
		//       },
		//       splitLine: { show: true, lineStyle: { type: 'dashed', color: '#DFE3EC', width: 1 } },
		//       axisLine: { show: true, lineStyle: { type: 'dashed', color: 'transparent' } },
		//       axisLabel: {
		//         inside: false,
		//         color: '#9CA2AF',
		//         fontWeight: '100',
		//         fontSize: '14',
		//         fontFamily: 'shsr',
		//         lineHeight: 14,
		//         margin: 10
		//       },
		//       axisTick: { show: false }
		//     }
		//   ],
		//   series: [
		//     {
		//       name: '本月',
		//       data: data_y_1,
		//       type: 'bar',
		//       itemStyle: { color: 'rgba(33, 148, 255, 1)' },
		//       barWidth: 18
		//     },
		//     {
		//       name: '上月',
		//       data: data_y_2,
		//       type: 'bar',
		//       itemStyle: { color: 'rgba(3, 227, 161, 1)' },
		//       barWidth: 18
		//     }
		//   ]
		// }

		// if (map_ecahrt_month.value) {
		//   map_ecahrt_month.value.dispose()
		// }
		// map_ecahrt_month.value = markRaw(echarts.init(document.getElementById('ehcart_line_month')))

		// map_ecahrt_month.value.setOption(option)
	};
	// const handleMonthDataDay = (data) => {
	//   let data_x = [];
	//   let data_y_1 = [];
	//   let data_y_2 = [];
	//   //console.log(data)
	//   // 处理本月的数据
	//   for (let i = 0; i < data.benefitList.length; i++) {
	//     let item = data.benefitList[i];
	//     data_x.push(dayjs(item.timePointer).format("MM-DD"));
	//     data_y_1.push(parseFloat(item.value).toFixed(1));
	//   }
	//   for (let i = 0; i < data.compareBenefitList.length; i++) {
	//     let item = data.compareBenefitList[i];
	//     data_y_2.push(parseFloat(item.value).toFixed(1));
	//   }

	//   //默认显示近7天数据
	//   let start1 = 0;
	//   let length = data.benefitList.length;
	//   if (length >= 0 && length <= 7) {
	//     start1 = 0;
	//   } else if (length >= 8 && length <= 19) {
	//     start1 = Math.ceil((1 - 7 / data.benefitList.length) * 100);
	//   } else if (length == 20) {
	//     start1 = 66;
	//   } else if (length == 21) {
	//     start1 = 68;
	//   } else if (length == 22) {
	//     start1 = 70;
	//   } else if (length >= 23 && length <= 24) {
	//     start1 = 72;
	//   } else if (length >= 25 && length <= 26) {
	//     start1 = 74;
	//   } else if (length >= 27 && length <= 28) {
	//     start1 = 76;
	//   } else if (length >= 29 && length <= 30) {
	//     start1 = 78;
	//   } else if (length == 31) {
	//     start1 = 80;
	//   }
	//   console.info("start2:" + start1);

	//   let legend_data = ["本月", "上月"];
	//   let option = {
	//     backgroundColor: "transparent",
	//     legend: {
	//       show: true,
	//       type: "scroll",
	//       center: true,
	//       top: 25,
	//       width: 920,
	//       data: legend_data,
	//       itemWidth: 16,
	//       itemGap: 80,
	//       align: "auto",
	//       textStyle: { color: "#092649" },
	//     },
	//     // dataZoom: [
	//     //   { start: start1, end: 100, bottom: 20, height: 30 },
	//     //   // { start: 0, end: 100 }
	//     // ],
	//     tooltip: {
	//       trigger: "axis",
	//       extraCssText:
	//         "background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;",
	//     },
	//     grid: { top: 90, containLabel: true, left: "0%", right: 0, bottom: 60 },
	//     xAxis: [
	//       {
	//         show: true,
	//         type: "category",
	//         axisTick: { show: false },
	//         axisLine: { show: true, lineStyle: { type: "dashed", color: "#DFE3EC" } },
	//         splitLine: {
	//           show: true,
	//           lineStyle: { type: "dashed", color: ["transparent"], width: 1 },
	//         },
	//         axisLabel: {
	//           inside: false,
	//           color: "#9CA2AF",
	//           fontWeight: "100",
	//           fontSize: "14",
	//           fontFamily: "shsr",
	//           lineHeight: 14,
	//           margin: 10,
	//         },
	//         data: data_x,
	//       },
	//     ],
	//     yAxis: [
	//       {
	//         type: "value",
	//         name: "",
	//         offset: -2,
	//         nameTextStyle: {
	//           color: "#9CA2AF",
	//           fontSize: 14,
	//           lineHeight: 40,
	//           fontWeight: 100,
	//           align: "left",
	//           fontFamily: "shsr",
	//           padding: [0, 0, 0, 0],
	//           width: 100,
	//         },
	//         splitLine: {
	//           show: true,
	//           lineStyle: { type: "dashed", color: "#DFE3EC", width: 1 },
	//         },
	//         axisLine: { show: true, lineStyle: { type: "dashed", color: "transparent" } },
	//         axisLabel: {
	//           inside: false,
	//           color: "#9CA2AF",
	//           fontWeight: "100",
	//           fontSize: "14",
	//           fontFamily: "shsr",
	//           lineHeight: 14,
	//           margin: 10,
	//         },
	//         axisTick: { show: false },
	//       },
	//     ],
	//     series: [
	//       {
	//         name: "本月",
	//         data: data_y_1,
	//         type: "bar",
	//         barWidth: 6,
	//         itemStyle: { color: "rgba(33, 148, 255, 1)" },
	//         //   barWidth: '35%',
	//       },
	//       {
	//         name: "上月",
	//         data: data_y_2,
	//         barWidth: 6,
	//         type: "bar",
	//         itemStyle: { color: "rgba(3, 227, 161, 1)" },
	//         //     barWidth: '35%',
	//       },
	//     ],
	//   };

	//   if (map_ecahrt_month.value) {
	//     map_ecahrt_month.value.dispose();
	//   }
	//   map_ecahrt_month.value = markRaw(
	//     echarts.init(document.getElementById("ehcart_line_month"))
	//   );

	//   map_ecahrt_month.value.setOption(option);
	// };

	const handleMonthDataDay = (data) => {
		let x_axis = [];
		let y_axis = [];
		// x_axis = ['上月','本月']
		console.log(data, 'data');
		for (let i = 0; i < data.benefitList.length; i++) {
			data.benefitList[i].detailList = data.benefitList[i].detailList.filter(
				(item) => item.benefitTypeDesc !== '收益总计'
			);
		}
		console.log(data, '过滤后的数据');

		for (let i = 0; i < data.benefitList[0].detailList.length; i++) {
			let value_list = [];
			for (let j = 0; j < data.benefitList.length; j++) {
				value_list.push(data.benefitList[j].detailList[i].value);
			}
			y_axis.push({
				name: data.benefitList[0].detailList[i].benefitTypeDesc,
				value_list: value_list
			});
		}

		for (let j = 0; j < data.benefitList.length; j++) {
			let time = data.benefitList[j].timePointer;
			x_axis.push(time.slice(0, 4) + '-' + time.slice(4));
		}

		for (let i = 0; i < y_axis.length; i++) {
			if (y_axis[i].name == '光伏发电') {
				y_axis[i].name = '光伏收益';
			}
		}

		let order = ['光伏收益', '储能峰谷套利', '余光入储', '现货交易', '需量优化', '需求侧响应'];
		// 定义一个方法来根据指定顺序重新排列数组

		y_axis = sortByOrder(y_axis, order);

		console.log(JSON.stringify(y_axis), 'y_axis');

		let unit_echarts_bar = '元';
		let result_option = [];
		let colors = ['#2DCDFC', '#03E3A1', '#1181FD', '#FEB56E', '#7C8DFF'];

		result_option = handleBarMapSelection(x_axis, '', y_axis, [], unit_echarts_bar, colors);

		delete result_option.dataZoom;

		result_option.legend.width = 380;
		result_option.legend.itemGap = 25;
		result_option.grid = { top: 75, containLabel: true, left: '0%', right: 0, bottom: 60 };
		result_option.xAxis[0].data = ['上月', '本月'];
		result_option.tooltip = {
			trigger: 'axis',
			confine: true,
			formatter: function (params) {
				let html = '';
				params.forEach((v) => {
					let time = x_axis[v.dataIndex];

					let value = v.value;
					if (value != 0 && !value) {
						value = '-';
					} else {
						value = Number(parseFloat(v.value).toFixed(2));
						value = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
					}
					html += `<div style="color: #666;font-size: 14px;line-height: 24px">
	                   <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${v.color};"></span>
	                   ${v.seriesName} : ${time} <span style="color:${v.color};font-weight:600;font-size: 14px"> ${value} </span>   ${unit_echarts_bar}
	                   `;
				});
				return html;
			},
			extraCssText:
				'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;'
		};

		console.log(
			result_option.series,
			'result_option.seriesresult_option.seriesresult_option.series'
		);

		for (let i = 0; i < result_option.series.length; i++) {
			result_option.series[i].barWidth = 8;
			result_option.series[i].stack = 'contrast';
			delete result_option.series[i].barMinWidth;
			delete result_option.series[i].barMaxWidth;
			if (colorList[result_option.series[i].name]) {
				result_option.series[i].itemStyle.color = colorList[result_option.series[i].name];
			}
		}

		if (ehcart_line_month.value) {
			ehcart_line_month.value.dispose();
		}
		ehcart_line_month.value = markRaw(echarts.init(document.getElementById('ehcart_line_month')));

		console.log(result_option, 'result_option');

		ehcart_line_month.value.setOption(result_option);
	};

	function sortByOrder(data, order) {
		// 创建一个映射表，将每个 name 映射到其在 order 中的索引
		const orderMap = {};
		order.forEach((item, index) => {
			orderMap[item] = index;
		});

		// 使用 sort 方法根据 orderMap 中的索引排序
		return data.sort((a, b) => {
			return orderMap[a.name] - orderMap[b.name];
		});
	}

	// 按照月查询本月收益
	const getMonthPrice = () => {
		let params = project_id.value;
		statTotalBenefitByMonth(params).then((data) => {
			console.log(data, '本月收益柱状图');
			if (data && data.benefitList.length > 0) {
				handleMonthDataDay(data);
			} else {
				hasMonthPrice.value = false;
			}
		});
	};

	// ----------------------------------------------本月收益-------------------------------

	// -----------------------------------------获取拓扑图-------------------------

	const topology_list_top = ref([]);
	const topology_list_bottom = ref([]);
	const time = ref(dayjs().format('YYYY-MM-DD HH:mm:ss'));

	const skipDevice = () => {
		eventBus.emit('menu-change', [1, 1, '/pages/operationMonitoring/#/deviceControl']); // 触发事件
	};

	const getTopology = () => {
		let params = project_id.value;
		statElementLatest(params)
			.then((data) => {
				if (data && data.elementList.length > 0) {
					time.value = data.latestTime;
					handleTopologyNew(data.elementList);
					// handleTopology(data.elementList);
				} else {
					message.info('拓扑图数据为空！');
					message.destroy();
				}
			})
			.catch((err) => {
				message.error(err.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	const topology_length = ref(0);
	const handleTopology = (data) => {
		console.log(data);
		let temp_data = [];
		// console.log(data)
		data.forEach((item) => {
			let capacity_1 = 0;
			let capacity_2 = 0;
			let topologyValue = 0;
			let unit_1 = 'kW';
			let unit_2 = 'kWh';
			if (item.elementValueList.length == 1) {
				capacity_1 = item.elementValueList[0].capacity;
				topologyValue = item.elementValueList[0].capacity;
			}
			if (item.elementValueList.length == 2) {
				capacity_1 = item.elementValueList[0].capacity;
				capacity_2 = item.elementValueList[1].capacity;
				topologyValue = item.elementValueList[0].capacity;
			}
			let yougong = 0;
			let paramList = [];
			let soc = 0;
			for (let i = 0; i < item.paramList.length; i++) {
				const element = item.paramList[i];
				if (element.paramCode == 'arr_soc') {
					typeof element.value == 'number'
						? (soc = Number(parseFloat(element.value).toFixed(2)))
						: (soc = '-');
					// soc = parseFloat(element.value).toFixed(2)
					item.paramList.splice(i, 1);
					i--;
					paramList = item.paramList;
				}
				if (element.paramCode == 'ps') {
					typeof element.value == 'number'
						? (yougong = parseFloat(element.value).toFixed(2))
						: (yougong = '-');
					// yougong = parseFloat(element.value).toFixed(2)
					item.paramList.splice(i, 1);
					i--;
					paramList = item.paramList;
				}
			}
			paramList.forEach((item) => {
				//console.log(item)
				if (item.paramCode == 'Ia' || item.paramCode == 'Ib' || item.paramCode == 'Ic') {
					item.unit = 'A';
				} else if (item.paramCode == 'Ua' || item.paramCode == 'Ub' || item.paramCode == 'Uc') {
					item.unit = 'kV';
				} else if (item.paramCode == 'Ps' || item.paramCode == 'real_demand') {
					item.unit = 'kW';
				}
			});

			//产品经理要求关口表的方向要对调 by叶东林 2024-8-19
			if (item.elementCode == 'GKB') {
				var tempDown = item.downArrow;
				item.downArrow = item.upArrow;
				item.upArrow = tempDown;
			}

			temp_data.push({
				downArrow: item.downArrow,
				upArrow: item.upArrow,
				elementCodeDesc: item.elementCodeDesc,
				elementCode: item.elementCode,
				capacity_1: capacity_1,
				capacity_2: capacity_2,
				list_length: item.elementValueList.length,
				topologyValue: topologyValue,
				unit_1,
				unit_2,
				yougong,
				soc,
				paramList: paramList
			});
		});

		topology_length.value = temp_data.length;
		//console.log(temp_data)
		let split_index = Math.trunc(temp_data.length / 2);
		topology_list_bottom.value = temp_data.slice(0, split_index);

		topology_list_top.value = temp_data.slice(split_index);
		console.log(topology_list_top.value);
	};

	const topology_obj = reactive({
		power_fh: '-', //功率
		arrow_fh: null, //
		power_gkb: '-', //功率
		arrow_gkb: null, //
		power_gf: '-', //功率
		arrow_gf: null, //
		capacity_gf: '-', //容量
		power_cn: '-', //功率
		arrow_cn: null, //
		real_demand: '-', //实时需量
		capacity_cn: '-', // 容量
		soc_cn: '-'
	});
	const handleTopologyNew = (data) => {
		console.log(data);
		data.forEach((item, index) => {
			let yougong = '-';
			let soc = '-';
			let powerValue = 0;
			for (let i = 0; i < item.paramList.length; i++) {
				const element = item.paramList[i];
				if (element.paramCode == 'arr_soc') {
					typeof element.value == 'number'
						? (soc = Number(parseFloat(element.value).toFixed(2)))
						: (soc = '-');
				}
				if (element.paramCode == 'ps') {
					powerValue = element.value;
					typeof element.value == 'number'
						? (yougong = Math.abs(Number(parseFloat(element.value).toFixed(2))))
						: (yougong = '-');
				}
			}

			if (item.elementCode == 'FH') {
				// topology_obj.arrow_fh = item.downArrow?"in":'out';
				if (item.downArrow) {
					topology_obj.arrow_fh = 'in';
				} else if (item.upArrow) {
					topology_obj.arrow_fh = 'out';
				} else {
					topology_obj.arrow_fh = null;
				}
				topology_obj.power_fh = yougong;
			}
			if (item.elementCode == 'GKB') {
				//产品经理要求关口表的方向要对调
				// topology_obj.arrow_gkb = item.downArrow?"in":'out';
				if (item.downArrow) {
					topology_obj.arrow_gkb = 'out';
				} else if (item.upArrow) {
					topology_obj.arrow_gkb = 'in';
				} else {
					topology_obj.arrow_gkb = null;
				}
				topology_obj.power_gkb = yougong;
			}
			if (item.elementCode == 'GF') {
				// topology_obj.arrow_gf = item.downArrow?"in":'out';
				if (projectTitle.value.includes('开沃')) {
					if (item.downArrow) {
						topology_obj.arrow_gf = 'in'; // 向下
					} else if (item.upArrow) {
						topology_obj.arrow_gf = 'out'; // 向上
					} else {
						topology_obj.arrow_gf = null;
					}
				} else {
					if (powerValue < 0) {
						topology_obj.arrow_gf = 'out'; // 向下
					} else if (powerValue > 0) {
						topology_obj.arrow_gf = 'in'; // 向上
					} else {
						topology_obj.arrow_gf = null;
					}
				}

				// if (item.downArrow) {
				// 	topology_obj.arrow_gf = 'in';  
				// } else if (item.upArrow) {
				// 	topology_obj.arrow_gf = 'out'; 
				// } else {
				// 	topology_obj.arrow_gf = null;
				// }
				topology_obj.power_gf = yougong;
				topology_obj.capacity_gf = item.elementValueList[0].capacity;
			}
			if (item.elementCode == 'CN') {
				if (projectTitle.value.includes('开沃')) {
					if (item.downArrow) {
						topology_obj.arrow_cn = 'in';
					} else if (item.upArrow) {
						topology_obj.arrow_cn = 'out';
					} else {
						topology_obj.arrow_cn = null;
					}
				} else {
					console.log('不是开沃项目CN', powerValue);
					if (powerValue < 0) {
						topology_obj.arrow_cn = 'out'; // 向下
					} else if (powerValue > 0) {
						topology_obj.arrow_cn = 'in'; // 向上
						console.log('out');
					} else {
						topology_obj.arrow_cn = null;
					}
				}

				topology_obj.soc_cn = soc;
				if (item.elementValueList.length > 0) {
					item.elementValueList.forEach((item2, index2) => {
						if (item2.rateType == 2) {
							topology_obj.real_demand = item2.capacity;
						}
						if (item2.rateType == 3) {
							topology_obj.capacity_cn = item2.capacity;
						}
					});
				}
				topology_obj.power_cn = yougong;
			}
		});
	};

	// -----------------------------------------获取拓扑图-------------------------

	// -------------------------------------------查询功率电量--------------------------

	const tab_change_list = ref([
		{
			name: '功率',
			value: '0'
		}
		// {
		//   name: '电量',
		//   value: '1'
		// }
	]);
	const currentThirdMenu = ref('0');
	const menuChange = (item) => {
		currentThirdMenu.value = item;
	};

	const time_select = ref('1');

	const changeTime = () => {
		getPower();
	};

	const getPower = () => {
		let params = {
			minuteParticle: Number(time_select.value),
			projectId: Number(project_id.value)
		};
		statActivePower(params)
			.then((data) => {
				if (data) {
					handlePower(data);
				} else {
					hasPowerData.value = false;
				}
			})
			.catch((err) => {
				message.error(err.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	const map_charts_power = ref();
	let handlePower = (data, timeList) => {
		let x_axis = [];
		let y_axis = [];
		const unit = 'kW';
		if (data.elementList.length > 0) {
			let result = init_x_y(data.elementList, 'time');
			x_axis = result.x_axis;
			y_axis = result.y_axis;
		}

		// let markArea = handleEchartsLineBg(timeList, x_axis)

		if (map_charts_power.value) {
			map_charts_power.value.dispose();
		}
		map_charts_power.value = markRaw(echarts.init(document.getElementById('map_charts_power')));
		let option_result = handleLineMapOptionSingal(x_axis, y_axis, unit);

		const colorMap = {
			光伏: '#2DCDFC',
			负荷: '#03E3A1',
			储能: '#7C8DFF',
			关口表: '#1181FD'
		};

		for (let i = 0; i < option_result.series.length; i++) {
			if (colorMap[option_result.series[i].name]) {
				option_result.series[i].itemStyle.color = colorMap[option_result.series[i].name];
			}
		}

		// option_result.series[0].markArea = markArea
		map_charts_power.value.setOption(option_result);
	};

	const getElec = () => {
		let params = {
			minuteParticle: Number(time_select.value),
			projectId: Number(project_id.value)
		};
		statPositiveAndReverseActivePower(params)
			.then((data) => {
				// ////console.log(data)
				handleElec(data);
			})
			.catch((err) => {
				message.error(err.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	const map_charts_elec = ref();
	const handleElec = (data) => {
		let x_axis = [];
		let y_axis = [];
		let x_axis_fan = [];
		let y_axis_fan = [];
		const unit = 'kW';
		// 正向
		if (data[0].elementList.length > 0) {
			let result = init_x_y(data[0].elementList);
			x_axis = result.x_axis;
			y_axis = result.y_axis;
		}
		// 反向
		if (data[1].elementList.length > 0) {
			let result = init_x_y(data[1].elementList);
			// x_axis_fan = result.x_axis
			y_axis_fan = result.y_axis;
		}
		if (map_charts_elec.value) {
			map_charts_elec.value.dispose();
		}
		map_charts_elec.value = markRaw(echarts.init(document.getElementById('map_charts_elec')));

		let map_data = y_axis.concat(y_axis_fan);
		map_data[1].name = '光伏反向';
		let option_result = handleLineMapOptionSingal(x_axis, map_data, unit);
		map_charts_elec.value.setOption(option_result);
	};

	const skipOverview = () => {
		eventBus.emit('menu-change', [1, 0, '/pages/operationMonitoring/#/analysis/analysis_all']); // 触发事件
	};
	// ------------------------------------------查询功率电量-----------------------

	// ----------------------------------收益分析-------------------------------

	const skip_list_data = ref([
		{
			name: '项目配置',
			url: '/pages/configurationManagement/#/projectDetail',
			first: 4,
			second: 0
		},
		{
			name: '收益总览',
			url: '/pages/incomeAnalysis/#/incomeAnalysis_all',
			first: 2,
			second: 0
		},
		{
			name: '设备管理',
			url: '/pages/operationMonitoring/#/deviceControl',
			first: 1,
			second: 1
		},
		{
			name: '异常管理',
			url: '/pages/operationMonitoring/#/unNormalControl',
			first: 1,
			second: 2
		},
		{
			name: '运行总览',
			url: '/pages/operationMonitoring/#/analysis/analysis_all',
			first: 1,
			second: 0
		}
	]);

	const skipMenu = (mess) => {
		eventBus.emit('menu-change', [mess.first, mess.second, mess.url]); // 触发事件
	};

	// ----------------------------------------收益分析-----------------------

	// -----------------------------------------------异常统计---------------------------

	const getUnnormal = () => {
		let params = project_id.value;
		countWarnNumByWarnStatus(params)
			.then((data) => {
				if (data.length == 0) {
					let data1 = [
						{
							totalNum: 0,
							warnStatus: 0,
							warnStatusDesc: '未处理'
						},
						{
							totalNum: 0,
							warnStatus: 2,
							warnStatusDesc: '已处理'
						}
					];

					handleUnnormal(data1);
					return;
				}
				handleUnnormal(data);
			})
			.catch((err) => {
				message.error(err.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	const total_unnormal = ref(0);
	const unnormal_list = ref([]);
	const handleUnnormal = (data) => {
		//////console.log(data)
		for (let i = 0; i < data.length; i++) {
			total_unnormal.value = Number(total_unnormal.value) + Number(data[i].totalNum);
		}
		for (let i = 0; i < data.length; i++) {
			let percent = parseFloat((100 * data[i].totalNum) / total_unnormal.value).toFixed(1);
			let totalNum = data[i].totalNum;
			if (total_unnormal.value == 0) {
				percent = '-';
				totalNum = '-';
			}
			unnormal_list.value.push({
				percent: percent,
				totalNum: totalNum,
				warnStatusDesc: data[i].warnStatusDesc
			});
		}
		total_unnormal.value == 0 ? (total_unnormal.value = '-') : null;
	};

	// -----------------------------------------------异常统计-----------------------------

	// ------------------------------------------------异常统计列表------------------------
	const warnn_list = ref([]);
	const getWarningList = () => {
		let params = { pageIndex: 1, pageSize: 10, projectId: Number(project_id.value) };
		findWarnPage(params)
			.then((data) => {
				warnn_list.value = data;
			})
			.catch((err) => {
				message.error(err.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	// -----------------------------------------异常统计列表-------------------------------
	const modal_img = ref(false);
	const modal_card = ref(false);
	// 首页卡片展示
	const showCardPic = () => {
		modal_card.value = true;
	};

	const topologyImgUrl = ref(null);
	const getTopologyImgUrl = () => {
		getProjectTopology(Number(project_id.value))
			.then((data) => {
				console.log(data, '返回的地址');
				if (data.success) {
					topologyImgUrl.value = data.data;
					console.log(topologyImgUrl.value, 'topologyImgUrl.value');
				}
			})
			.catch((err) => {
				message.error(err.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	onMounted(() => {
		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			project_id.value = project_mess.project_id;
			projectTitle.value = project_mess.project_title;
		} else {
			message.error('未选择项目，请重新选择！');
		}

		//查询拓扑图Url
		getTopologyImgUrl();
		//消息
		getMessage();
		// 累计收益
		getSum();
		// 本月数据
		getMonthTotal();
		// 拓扑图
		getTopology();
		// 查询功率
		getPower();
		// 查询电量
		// getElec()
		// 异常统计
		getUnnormal();
		// 预警列表
		getWarningList();
		// 查询月收益 按天
		getMonthPrice();
	});
</script>
<style lang="less" scoped>
	:deep(.ant-upload-list) {
		display: none;
	}

	.home_index {
		.main_contain {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
		}

		.main_center,
		.main_right {
			height: 100%;
			border-radius: 8px;
			background-color: #ffffff;
			box-sizing: border-box;
		}

		.main_left {
			width: 450px;

			box-sizing: border-box;

			.title_right {
				display: flex;
				flex-direction: row;
				justify-content: flex-start;
				align-items: center;

				.tab_mess_title_sub {
					margin-right: 17px;
				}
			}

			.total_income {
				border-radius: 8px;
				background-color: #ffffff;
				height: 337px;
				width: 100%;
				padding: 28px;
				box-sizing: border-box;
				margin-bottom: 28px;
			}

			.current_month_income {
				padding: 28px;
				border-radius: 8px;
				background-color: #ffffff;
				height: 459px;
				box-sizing: border-box;
			}

			.percent_up_down {
				height: 18px;
				width: 100%;
				margin-top: 44px;
				margin-bottom: 20px;

				div {
					width: 50% !important;
					float: left;
					display: flex;
					flex-direction: row;
					justify-content: center;
					align-items: center;
				}

				span {
					font-family: SourceHanSansCN-Regular;
					font-weight: 400;
					font-size: 14px;
					color: #9ca2af;
					line-height: 14px;

					&:nth-child(1) {
						margin-right: 9px;
					}

					&:nth-child(2) {
						font-family: D-DIN-PRO-Bold;
						font-weight: bold;
						font-size: 18px;
						color: #092649;
						line-height: 18px;
					}

					&:nth-child(3) {
						margin-right: 9px;
					}
				}
			}
		}

		.main_center {
			box-sizing: border-box;
			// width: 46.6%;
			width: 908px;
			// padding: 32px;
			background-color: transparent;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			.center_top {
				// height: 48.7vh;
				// height: 460px;
				height: 460px;
				box-sizing: border-box;
				border-radius: 8px;
				background-color: #ffffff;
				// padding: 32px 32px 0;
				padding: 28px 28px 0;

				.title {
					// margin-bottom: 32px;
					height: 16px;
					margin-bottom: 38px;
				}

				// margin-bottom: 32px;
				margin-bottom: 28px;
			}

			.center_bottom {
				// height: 35.6vh;
				// height: 336px;
				// height: 344px;
				height: 337px;
				box-sizing: border-box;
				border-radius: 8px;
				background-color: #ffffff;
				// padding: 3.4%;
				padding: 0 32px;
				// padding-top: 32px;
				padding-top: 28px;
				box-sizing: border-box;
				position: relative;
				overflow: hidden;

				.tab_change_list {
					height: 36px;
					// height: 31.5px;
					background: #ffffff;
					border-radius: 8px;
					box-sizing: border-box;
					// display: flex;

					overflow: hidden;
					position: relative;

					.title {
						height: 36px;
						line-height: 36px;
					}

					.tab_change_item {
						height: 100%;
						margin-right: 25px;
						position: relative;
						cursor: pointer;

						.tab_change_name {
							font-family: SourceHanSansCN-Regular;
							font-weight: 400;
							font-size: 18px;
							line-height: 18px;
							color: #9ca2af;
						}

						.tab_change_line {
							height: 3px;
							background: #1181fd;
							border-radius: 2px;
							width: 100%;
							display: none;
							position: absolute;
							bottom: 0;
						}
					}

					.tab_change_item.active {
						.tab_change_name {
							font-family: SourceHanSansCN-Bold;
							font-weight: bold;
							color: #1181fd;
						}

						.tab_change_line {
							display: block;
						}
					}
				}

				.select_arrow {
					position: absolute;
					// top: 5.7%;
					// right: 3.6%;
					top: 28px;
					right: 32px;

					img {
						margin-left: 40px;
						cursor: pointer;
					}
				}

				.map_center_two {
					height: calc(100% - 36px);
					width: 100%;
					box-sizing: border-box;
					position: relative;

					.unit_mess {
						color: #949aa6;
						font-size: 14px;
						font-family: 'shsr';
						position: absolute;
						top: 5px;
						z-index: 99;
					}

					#map_charts_power {
						height: calc(100% - 18px);
						width: 100%;
						position: absolute;
						top: 0px;
						left: 0;
						background-color: #ffffff;
					}

					#map_charts_elec {
						height: calc(100% - 68px);
						width: 100%;
						position: absolute;
						top: 0;
						left: 0;
						background-color: #ffffff;
					}
				}
			}
		}

		.main_right {
			// width: 23.6%;
			width: 450px;
			// padding: 32px;
			background-color: transparent;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
		}

		.item_1 {
			// padding: 6.9%;
			padding: 28px;
			border-radius: 8px;
			box-sizing: border-box;
			background-color: #ffffff;
			// height: 19.7vh;
			height: 188px;
			width: 100%;
			display: flex;
			flex-direction: column;

			.skip_list {
				flex: 1;
				// padding-top: 9%;
				padding-top: 28px;
				display: flex;
				flex-direction: row;
				justify-content: flex-start;
				flex-wrap: wrap;
				align-items: flex-start;

				.skip_list_item {
					-webkit-user-select: none;
					/* Safari */
					-moz-user-select: none;
					/* Firefox */
					-ms-user-select: none;
					/* IE10+/Edge */
					user-select: none;
					/* Standard syntax */
					cursor: pointer;
					// height: 36px;
					height: 32px;
					max-width: 120px;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					line-height: 16px;
					background: rgba(17, 129, 253, 0.15);
					border-radius: 5px;
					font-family: SourceHanSansCN-Regular;
					font-weight: 400;
					font-size: 16px;
					color: #1181fd;
					box-sizing: border-box;
					padding: 8px 14px;
					margin-bottom: 21px;
					margin-right: 28px;
				}
			}
		}

		.item_2 {
			// height: 64.44vh;
			height: 609px;
			background: #ffffff;
			border-radius: 8px;
			// padding: 6.9%;
			padding: 28px;
			box-sizing: border-box;

			.unnormal_mess {
				// height: 24vh;
				height: 216px;
				box-sizing: border-box;

				// margin-top: 1vh;
				.mess_left {
					width: 40%;
					height: 100%;
					float: left;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;

					span {
						font-family: D-DIN-PRO-600-SemiBold;
						font-weight: 600;
						font-size: 28px;
						color: #092649;
						height: 28px;
						line-height: 28px;
						display: block;

						i {
							height: 14px;
							line-height: 14px;
							font-style: normal;
							font-family: SourceHanSansCN-Regular;
							font-weight: 400;
							font-size: 14px;
							color: #9ca2af;
						}

						&:nth-child(2) {
							margin-top: 10px;
							margin-bottom: 10px;
						}

						&:nth-child(3) {
							height: 16px;
							line-height: 16px;
							font-family: SourceHanSansCN-Regular;
							font-weight: 400;
							font-size: 16px;
							color: #9ca2af;
						}
					}
				}

				.mess_right {
					width: 60%;
					height: 100%;
					float: left;
					display: flex;
					flex-direction: column;
					justify-content: space-evenly;

					.status_list {
						height: 33px;
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						div {
							&:nth-child(1) {
								height: 16px;
								line-height: 16px;
								font-family: SourceHanSansCN-Regular;
								font-weight: 400;
								font-size: 14px;
								color: #9ca2af;
								// line-height: 20px;
								display: flex;
								flex-direction: row;
								justify-content: space-between;
								align-items: flex-end;
							}

							&:nth-child(2) {
								height: 10px;
							}

							span {
								&:nth-child(2) {
									font-family: D-DIN-PRO;
									font-weight: 600;
									font-size: 20px;
									color: #092649;

									// line-height: 20px;
									i {
										color: rgba(156, 162, 175, 1);
										font-size: 14px;
										font-style: normal;
									}
								}
							}
						}
					}

					.percent_status {
						height: 10px;
						background: #edf4fc;

						span {
							height: 100%;
							float: left;
							display: block;
							background: rgba(17, 129, 253, 1);
						}
					}
				}
			}

			.warning_title_head {
				// height: 5.55vh;
				height: 52px;
				background: #f2f8ff;
				// margin-top: 3vh;
				margin-top: 26px;
				display: flex;
				flex-direction: row;
				align-items: center;
				text-align: center;

				div {
					&:nth-child(1) {
						width: 50px;
					}

					&:nth-child(2) {
						width: 100px;
					}

					&:nth-child(3) {
						width: calc(100% - 150px);
						text-align: left;
					}
				}
			}

			.warning_list_contain {
				// height: 22.22vh;
				height: 272px;
				overflow: hidden;
				position: relative;

				// &::-webkit-scrollbar {
				//   display: none;
				// }
				// scrollbar-width: none;
				// -ms-overflow-style: none;
				.warning_list_item_contain {
					// height: 55.55vh;
					height: max-content;
					width: 100%;
				}

				.warning_list_item {
					// height: 5.55vh;
					height: 54px;
					display: flex;
					flex-direction: row;
					align-items: center;
					text-align: center;
					// line-height: 5.55vh;
					line-height: 54px;
					// box-shadow: 0px 1px 0px 0px #dde2ed;
					border-bottom: 1px solid #dde2ed;

					div {
						&:nth-child(1) {
							width: 50px;
							font-family: SourceHanSansCN-Regular;
							font-weight: 400;
							font-size: 14px;
							color: #092649;
						}

						&:nth-child(2) {
							width: 100px;

							span {
								display: block;
								width: 46px;
								border-radius: 18px;
								margin: 0 auto;
								font-family: SourceHanSansCN-Regular;
								font-weight: 400;
								font-size: 14px;
							}
						}

						&:nth-child(3) {
							width: calc(100% - 150px);
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
							will-change: transform;
							transform: translateZ(0);
							text-align: left;
							font-family: SourceHanSansCN-Regular;
							font-weight: 400;
							font-size: 14px;
							color: #092649;
						}
					}
				}

				&:hover {
					.scrollBoxRanklist {
						animation-play-state: paused;
					}

					.scrollBoxRanklist1 {
						animation-play-state: paused;
					}
				}
			}

			.left_img {
				box-sizing: border-box;
				// width: 41.66%;
				width: 52.66%;
				aspect-ratio: 1/1;
				background-image: url('/assets/icon/warning_unnormal.png');
				background-repeat: no-repeat;
				background-size: 100% 100%;
			}
		}

		.title {
			font-family: SourceHanSansCN-Regular;
			font-weight: bold;
			font-size: 18px;
			height: 18px;
			line-height: 18px;
			color: #092649;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			position: relative;

			// margin-bottom: 5.5%;
			i {
				font-style: normal;
				font-size: 14px;
				font-weight: 400;
				color: #9ca2af;
			}

			.title_icon_arrow {
				display: block;
				background-image: url('/assets/icon/table_arrow.png');
				background-repeat: no-repeat;
				width: 7px;
				height: 12px;
				cursor: pointer;
				background-size: 100%;
			}
		}

		// .message_all {
		//   height: 40px;
		//   width: 100%;
		// }
		.message {
			cursor: pointer;
			// height: 40px;
			height: 35px;
			width: 100%;
			border-radius: 5px;
			// padding: 2.2% 3.2%;
			padding: 0 16px;
			line-height: 35px;
			box-sizing: border-box;
			overflow: hidden;
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;
			z-index: 9;

			// position: relative;
			img {
				// display: block;
				height: 18px;
				width: 18px;
				margin-right: 2.2%;
				// position: absolute;
				// top: 50%;
				// left: 20px;
				// transform: translate(0%, -50%);
			}

			.message_close {
				height: 10px;
				width: 10px;
				background: url('/assets/icon/modal_close.png');
				background-repeat: no-repeat;
				background-size: contain;
				cursor: pointer;
			}

			.mess {
				padding: 0 3px;
				box-sizing: border-box;
				height: 14px;
				width: calc(100% - 36px);
				font-family: SourceHanSansCN-Regular;
				font-weight: 400;
				font-size: 14px;
				line-height: 14px;
				color: #1181fd;
			}

			.mess_animation {
				animation-name: lineChange;
				animation-duration: 10s;
				animation-delay: 0.3s;
				animation-timing-function: linear;
				animation-iteration-count: infinite;
				white-space: nowrap;

				/* 防止文字换行 */
				@keyframes lineChange {
					0% {
						transform: translateX(0);
					}

					20% {
						transform: translateX(0);
					}

					100% {
						transform: translateX(-100%);
					}
				}
			}
		}

		.message_contain {
			// height: 40px;
			height: 35px;
			width: 100%;
			margin: 0 auto;
			// margin-bottom: 9.7%;
			position: relative;
			transform-style: preserve-3d;

			// animation-name: flip;
			// animation-duration: 40s;
			// animation-timing-function: ease-in-out;
			// animation-iteration-count: infinite;
			transform: rotateX(0deg);

			@keyframes flip {
				0% {
					transform: rotateX(0deg);
				}

				5% {
					transform: rotateX(-90deg);
				}

				25% {
					transform: rotateX(-90deg);
				}

				30% {
					transform: rotateX(-180deg);
				}

				50% {
					transform: rotateX(-180deg);
				}

				55% {
					transform: rotateX(-270deg);
				}

				75% {
					transform: rotateX(-270deg);
				}

				80% {
					transform: rotateX(-360deg);
				}

				100% {
					transform: rotateX(-360deg);
				}
			}

			.message_1 {
				height: 35px;
				width: 100%;
				position: absolute;
				background: #dfefff;
				transform: rotateX(0deg) translateX(0px) translateZ(20px) translateY(0px);
			}

			.message_2 {
				height: 35px;
				width: 100%;
				position: absolute;
				background: #dfefff;

				transform: rotateX(90deg) translateX(0px) translateZ(20px) translateY(0px);
			}

			.message_3 {
				height: 35px;
				width: 100%;
				position: absolute;
				background: #dfefff;

				transform: rotateX(-180deg) translateX(0px) translateZ(20px) translateY(0px);
			}

			.message_4 {
				height: 35px;
				width: 100%;
				position: absolute;
				background: #dfefff;

				transform: rotateX(-90deg) translateX(0px) translateZ(20px) translateY(0px);
			}
		}

		.tab_title {
			// height: 40px;
			height: 35px;
			width: 100%;
			box-sizing: border-box;
			// padding: 2.2% 0;
			position: relative;
			line-height: 35px;
			background: linear-gradient(90deg, rgba(17, 129, 253, 0.19), rgba(17, 129, 253, 0.04));

			.left_bar_line {
				width: 3px;
				// height: 40px;
				height: 35px;
				background-color: #0e81fd;
				position: absolute;
				top: 0;
				left: 0;
			}

			.tab_mess_title {
				padding-left: 23px;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				font-family: SourceHanSansCN-Regular;
				font-weight: 400;
				font-size: 16px;
				color: #092649;

				.tab_mess_title_sub {
					float: right;
					padding-right: 30px;
					font-family: D-DIN-PRO-SemiBold;
					font-weight: 600;
					font-size: 24px;
					height: 24px;
					line-height: 24px;
					color: #092649;
					letter-spacing: -1px;

					span {
						font-family: D-DIN-PRO-SemiBold;
					}

					.unit {
						font-style: normal;
						font-family: SourceHanSansCN-Regular;
						font-weight: 400;
						font-size: 16px;
						color: #9ca2af;
						line-height: 20px;
					}
				}
			}
		}

		.map_chart_1 {
			height: 200px;
			width: 100%;
			box-sizing: border-box;
			position: relative;
			margin-top: 46px;

			.echart_pie_total {
				height: 100%;
				width: 100%;
			}

			#map_pie_sum {
				// height: 19vh;
				// height: 262px;
				height: 230px;
				position: absolute;
				// top: -1.5vh;
				left: 0;
			}
		}

		.unit {
			font-family: 'SourceHanSansCN-Regular';
			font-weight: 400;
			font-size: 16px;
			color: #9ca2af;
		}

		.map_chart_2 {
			height: 338px;
			// height: 30.9vh;
			width: 100%;
			box-sizing: border-box;
			position: relative;

			.ehcart_line_month {
				height: 100%;
				width: 100%;
			}

			.map_unit {
				font-family: 'SourceHanSansCN-Regular';
				font-weight: 400;
				font-size: 16px;
				color: #9ca2af;
				left: 0px;
				position: absolute;
				top: 35px;
			}
		}
	}

	.map_change_5 {
		height: 363px;

		.card_list {
			// height: 11.84vh;
			height: 130px;
			// width: 32.1%;
			width: 266px;
			border-radius: 26px;
			box-sizing: border-box;
			padding: 23px;
			position: relative;
			float: left;

			.mess_card_list {
				display: flex;
				flex-direction: column;
				justify-content: center;
				height: 100%;
				width: 80%;

				span {
					// color: #9CA2AF;
					font-weight: 400;

					// display: block;
					&:nth-child(1) {
						height: 16px;
						line-height: 16px;
						font-family: SourceHanSansCN-Regular;
						font-size: 16px;
						font-weight: 400;
						color: #9ca2af;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						width: max-content;
					}

					&:nth-child(2) {
						font-family: D-DIN-PRO-Bold;
						font-weight: bold;
						font-size: 36px;
						height: 27px;
						color: #092649;
						line-height: 27px;
						margin-top: 12px;
						margin-bottom: 12px;

						i {
							font-style: normal;
							font-family: SourceHanSansCN-Regular;
							font-size: 14px;
							font-weight: 400;
							color: #9ca2af;
						}

						.card_center_list_sub1 {
							font-weight: bold;
							font-size: 14px;
							color: #9ca2af;
						}

						.card_center_list_sub {
							font-weight: bold;
							font-size: 14px;
							color: #092649;
						}
					}
				}

				.card_bottom_list {
					// position: relative;
					// top: 16px;
					font-family: SourceHanSansCN-Regular;
					font-weight: 400;
					font-size: 16px;
					color: rgba(255, 255, 255, 0.7);
					display: flex;
					flex-direction: row;
					justify-content: flex-start;
					align-items: center;
					width: max-content;

					i {
						font-style: normal;
						font-family: D-DIN-PRO-Bold;
						font-weight: bold;
						font-size: 18px;
						color: #092649;
					}

					.card_bottom_center_line {
						font-style: normal;
						font-family: SourceHanSansCN-Regular;
						font-weight: 400;
						font-size: 16px;
						color: rgba(255, 255, 255, 0.7);
					}
				}
			}

			.icon_img {
				position: absolute;
				// top: 25%;
				top: 26px;
				// right: 9%;
				right: 0;
				// height: 50%;
				height: 94px;
				aspect-ratio: 1/1;
				box-sizing: border-box;
			}

			.map_line_border {
				box-sizing: border-box;
				position: absolute;
				// height: 62.5%;
				// height: 38px;
				// height: 52px;
				height: 40px;
				width: 0px;
				border-left: 4px dashed #dde2ed;
				bottom: 0;
				left: 50%;
				transform: translate(0, 100%);
			}
		}

		.map_center_list_top {
			// height: 38.3vh;
			// height: 19.24vh;
			height: 168px;
			box-sizing: border-box;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			margin: 0 auto;
		}

		.map_center_line {
			width: 68%;
			height: 3px;
			border-bottom: 3px dashed #dde2ed;
			margin: 0 auto;
			position: relative;
			left: 2px;
			top: -4px;
		}

		.map_center_list_bottom {
			display: flex;
			flex-direction: row;
			justify-content: space-evenly;
			align-items: flex-end;
			// height: 19.24vh;
			height: 168px;
			width: 100%;
			margin: 0 auto;
			position: relative;
			top: -4px;

			.map_line_border {
				box-sizing: border-box;
				position: absolute;
				// height: 62.5%;
				// height: 52px;
				height: 40px;
				width: 0px;
				// border-left: 4px dashed #dde2ed;
				border-left: 3px dashed #dde2ed;
				top: 0px;
				left: 50%;
				transform: translate(0, -100%);
			}
		}

		.GKB {
			background: linear-gradient(90deg, rgba(17, 129, 253, 0.14), rgba(17, 129, 253, 0.02));

			.card_img {
				background: url('/assets/icon/home_index_dw.png');
				background-size: contain;
				background-repeat: no-repeat;
			}
		}

		.GF {
			background: linear-gradient(90deg, rgba(17, 129, 253, 0.14), rgba(17, 129, 253, 0.02));

			.card_img {
				background: url('/assets/icon/home_index_gf.png');
				background-size: contain;
				background-repeat: no-repeat;
			}
		}

		.FH {
			background: linear-gradient(90deg, rgba(17, 129, 253, 0.14), rgba(17, 129, 253, 0.02));

			.card_img {
				background: url('/assets/icon/home_index_fh.png');
				background-size: contain;
				background-repeat: no-repeat;
			}
		}

		.CN {
			background: linear-gradient(90deg, rgba(17, 129, 253, 0.14), rgba(17, 129, 253, 0.02));

			.card_img {
				background: url('/assets/icon/home_index_cn.png');
				background-size: contain;
				background-repeat: no-repeat;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;

				span {
					font-family: SourceHanSansCN-Medium;
					font-weight: 500;
					font-size: 12px;
					transform: scale(0.6);
					color: #7c8dff;
					text-align: center;
					display: block;
					height: 10px;
					line-height: 10px;
					position: relative;
					top: -12px;
				}
			}
		}

		.CDZ {
			background: linear-gradient(90deg, rgba(17, 129, 253, 0.14), rgba(17, 129, 253, 0.02));

			.card_img {
				background: url('/assets/icon/home_index_cdz.png');
				background-size: contain;
				background-repeat: no-repeat;
			}
		}
	}

	// 四个条目的数据
	.map_change_5.item_num_4 {
		.map_center_list_top {
			width: 78%;
			justify-content: space-between;

			.card_list {
				width: 41.2%;
			}
		}

		.map_center_line {
			width: 46.5%;
		}

		.map_center_list_bottom {
			width: 69.4%;
			justify-content: space-between;

			.card_list {
				width: 46%;
			}
		}
	}

	.map_change_5.item_num_3 {
		.map_center_list_top {
			width: 78%;
			justify-content: space-between;

			.card_list {
				width: 41.2%;
			}
		}

		.map_center_list_bottom {
			width: 100%;
			justify-content: center;

			.card_list {
				width: 32.1%;
			}
		}
	}

	.up_img {
		width: 20px;
		height: 14px;
		display: inline-block;
		background: url('/assets/icon/up_percent.png');
		background-repeat: no-repeat;
	}

	.down_img {
		width: 10px;
		height: 14px;
		display: inline-block;
		background: url('/assets/icon/down_percent.png');
		background-repeat: no-repeat;
		background-size: 100%;
	}

	// #mySwiper_mess {
	//   .ant-tooltip {
	//     width: max-content !important;
	//   }
	//   .ant-tooltip-content {
	//     width: max-content !important;
	//   }
	// }
	// .mySwiper_mess {
	//   width: 100%;
	//   div {
	//     width: 100% !important;
	//     box-sizing: border-box;
	//     padding-right: 10px;
	//     overflow: hidden;
	//     text-overflow: ellipsis;
	//     white-space: nowrap;
	//   }
	// }

	.map_center_list_top {
		.arrow_in {
			position: absolute;
			height: 22px;
			width: 18.4px;
			background: url('/assets/icon/home_index_in.png');
			background-size: contain;
			background-repeat: no-repeat;
			top: 50%;
			left: 50%;
			transform: translate(-11px, -50%) rotateZ(180deg);
		}

		.arrow_out {
			position: absolute;
			height: 22px;
			width: 18.4px;
			background: url('/assets/icon/home_index_out.png');
			background-size: contain;
			background-repeat: no-repeat;
			top: 50%;
			left: 50%;
			transform: translate(-11px, -50%) rotateZ(180deg);
		}
	}

	.map_center_list_bottom {
		.arrow_in {
			position: absolute;
			height: 22px;
			width: 18.4px;
			background: url('/assets/icon/home_index_in.png');
			background-size: contain;
			background-repeat: no-repeat;
			top: 50%;
			left: 50%;
			transform: translate(-11px, -50%);
		}

		.arrow_out {
			position: absolute;
			height: 22px;
			width: 18.4px;
			background: url('/assets/icon/home_index_out.png');
			background-size: contain;
			background-repeat: no-repeat;
			top: 50%;
			left: 50%;
			transform: translate(-11px, -50%);
		}
	}

	.scrollBoxRank {
		position: relative;
		height: 100%;
	}

	.scrollBoxRanklist {
		position: absolute;
		top: 0;
		left: 0;
		/* position: relative; */
		/* top: 0; */
		/* background: red; */
		height: unset !important;
		width: 100% !important;
		animation: moveScroll 24s linear infinite;
	}

	.scrollBoxRanklist1 {
		position: absolute;
		top: 0;
		left: 0;
		height: unset !important;
		width: 100% !important;
		animation: moveScroll1 24s linear infinite;
		transform: translateY(100%);
	}

	@keyframes moveScroll {
		0% {
			transform: translateY(0%);
		}

		100% {
			transform: translateY(-100%);
		}
	}

	@keyframes moveScroll1 {
		0% {
			transform: translateY(100%);
		}

		100% {
			transform: translateY(0%);
		}
	}

	.show_card_pic {
		position: absolute;
		right: 40px;
		top: -10px;
		display: block;
		height: 36px;
		box-sizing: border-box;
		width: 124px;
		border-radius: 5px;
		background: rgba(17, 129, 253, 0.15);
		border: 1px solid #1181fd;
		font-family: SourceHanSansCN-Regular;
		font-weight: 400;
		font-size: 16px;
		color: #1181fd;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		cursor: pointer;

		img {
			width: 17.8px;
			height: 16.4px;
			margin-right: 6px;
		}
	}

	.tuopu {
		display: block;
		aspect-ratio: 1229/808;
		width: 1229px;
		margin: 0 auto;
	}

	.center_top {
		:deep(.ant-modal) {
			width: 74% !important;
			aspect-ratio: 1420/900;
			overflow: hidden;

			.ant-modal-content {
				padding: 20px 0px;
			}

			.ant-modal-header {
				padding: 0 20px;
			}
		}
	}

	.static_topology {
		height: 347px;
		/* 396*0.875*/
		box-sizing: border-box;

		.unit_item {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;

			.unit_item_img {
				display: block;
			}

			.unit_item_name {
				font-family: 'SourceHanSansCN-Medium';
				font-weight: 500;
				font-size: 16px;
				color: #9ca2af;

				span {
					font-family: 'D-DIN-PRO-Bold';
					font-weight: 600;
					font-size: 16px;
					color: #092649;
				}
			}
		}

		.unit_first {
			.unit_item_img {
				height: 45.5px;
				aspect-ratio: 125/78;
			}
		}

		.unit_second {
			padding-top: 5px;

			.unit_item_img {
				height: 62px;
				aspect-ratio: 98/106;
			}
		}

		.unit_third {
			.unit_item_img {
				height: 69.5px;
				aspect-ratio: 162/119;
			}
		}

		.unit_item_line {
			height: 4px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			position: relative;

			span {
				display: block;
				height: 4px;
				width: 10px;
				background-color: #dde2ed;
			}

			.orange_arrow {
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translate(-50%, 0);

				img {
					display: block;
					height: 17.5px;
					aspect-ratio: 27/30;
				}
			}

			.orange_arrow_up {
				animation-name: orangeArrowUp;
				animation-duration: 3s;
				animation-delay: 0.3s;
				animation-timing-function: linear;
				animation-iteration-count: infinite;

				@keyframes orangeArrowUp {
					0% {
						top: 90%;
					}

					100% {
						top: 10%;
					}
				}
			}

			.orange_arrow_down {
				animation-name: orangeArrowDown;
				animation-duration: 3s;
				animation-delay: 0.3s;
				animation-timing-function: linear;
				animation-iteration-count: infinite;

				img {
					transform: rotateZ(180deg);
				}

				@keyframes orangeArrowDown {
					0% {
						top: 10%;
					}

					100% {
						top: 80%;
					}
				}
			}
		}

		.unit_item_line_cloumn {
			height: 100%;
			width: 4px !important;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			align-items: center;

			span {
				display: block;
				height: 10px;
				width: 4px;
				background-color: #dde2ed;
			}
		}

		.level_first {
			padding: 0 55px 0 33px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;

			.level_first_left {
				display: flex;
				flex-direction: row;
				justify-content: flex-start;
				align-items: flex-start;

				.unit_item_line {
					position: relative;
					top: 28px;
				}
			}
		}

		.level_second {
			box-sizing: border-box;
			padding: 0 94px 0 211px;
			height: 75px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;

			.level_second_left,
			.level_second_right {
				display: flex;
				flex-direction: row;
				height: 100%;
			}

			.level_second_right {
				.unit_mess_contain {
					margin-right: 20px;
				}
			}

			// .unit_item_line {
			//   transform: rotateZ(90deg) translateX(50%);
			// }
		}

		.divid_line {
			width: 100% !important;
			padding-left: 165px;
			padding-right: 39px;
			box-sizing: border-box;
		}

		.level_third {
			box-sizing: border-box;
			padding: 0 75px 0 196px;
			height: 75px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;

			.level_third_left,
			.level_third_right {
				display: flex;
				flex-direction: row;
				justify-content: space-between;

				.unit_mess_contain {
					margin-right: 20px;
				}
			}
		}

		.level_fouth {
			box-sizing: border-box;
			padding: 0 166px 0 325px;
			height: 91px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;

			.level_fouth_left {
				height: 100%;

				.unit_item_img {
					height: 70px;
					aspect-ratio: 171/120;
				}
			}

			.level_fouth_right {
				height: 100%;

				.unit_item_img {
					height: 61px;
					aspect-ratio: 167/104;
				}
			}

			.unit_item_name {
				height: 21px;
				line-height: 21px;
			}

			.unit_item {
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;
			}
		}

		.unit_mess_icon {
			height: 18px;
			width: 18px;

			img {
				display: block;
				height: 100%;
				width: 100%;
			}
		}

		.unit_mess_contain {
			display: flex;
			flex-direction: column;
			justify-content: center;

			.unit_mess {
				height: 18px;
				font-family: 'SourceHanSansCN-Regular';
				font-weight: 400;
				font-size: 16px;
				color: #9ca2af;
				display: flex;
				flex-direction: row;
				align-items: center;
				margin-left: 20px;

				&:nth-child(2) {
					margin-top: 13px;
				}

				.unit_mess_icon {
					margin-right: 8px;
				}

				span {
					font-family: 'D-DIN-PRO-Bold';
					font-weight: bold;
					font-size: 26px;
					color: #092649;
					line-height: 18px;
					position: relative;
					top: 1px;
					margin: 0 5px 0 8px;
				}
			}
		}
	}
</style>
<style>
	.tp_mess {
		font-family: SourceHanSansCN-Regular;
		font-weight: 400;
		font-size: 16px;
		color: #ffffff;
	}

	.ant-tooltip {
		width: max-content;
	}

	.ant-tooltip-content {
		width: max-content;
	}
</style>
