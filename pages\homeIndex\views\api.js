import http from '/tool/http.js';
// 判断当前月是否设置电价价格(结果：0表示未设置 1表示已设置)
export const checkEleratePriceSetting = (params) => {
	let url = `/api/index/checkEleratePriceSetting/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				//console.log(data)
				if (data.code == 200) {
					resolve(data.data);
				}
				// else {
				//     reject({
				//         message:data.msg
				//     })
				// }
			})
			.catch((errorInfo) => {
				reject({
					message: errorInfo
				});
			});
	});
};

// 判断当前月是否设置电价价格(结果：0表示未设置 1表示已设置)
export const sumBenefitAmountByType = (params) => {
	let url = `/api/index/sumBenefitAmountByType/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				//console.log(data)
				if (data.code == 200) {
					resolve(data.data);
				}
				// else {
				//     reject({
				//         message:data.msg
				//     })
				// }
			})
			.catch((errorInfo) => {
				reject({
					message: errorInfo
				});
			});
	});
};

// 查询本月收益
export const monthBenefitAmountByType = (params) => {
	let url = `/api/index/monthBenefitAmountByType/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				//console.log(data)
				if (data.code == 200) {
					resolve(data.data);
				}
				// else {
				//     reject({
				//         message:data.msg
				//     })
				// }
			})
			.catch((errorInfo) => {
				reject({
					message: errorInfo
				});
			});
	});
};

// 按天查询收益信息
export const statTotalBenefitByDay = (params) => {
	let url = `/api/index/statTotalBenefitByDay/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				}
				// else {
				//     reject({
				//         message:data.msg
				//     })
				// }
			})
			.catch((errorInfo) => {
				reject({
					message: errorInfo
				});
			});
	});
};

//按月查询收益信息
export const statTotalBenefitByMonth = (params) => {
	let url = `/api/index/statTotalBenefitByMonth/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				}
				// else {
				//     reject({
				//         message:data.msg
				//     })
				// }
			})
			.catch((errorInfo) => {
				reject({
					message: errorInfo
				});
			});
	});
};

// 查询拓扑图
export const statElementLatest = (params) => {
	let url = `/api/index/statElementLatest/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				}
				// else {
				//     reject({
				//         message:data.msg
				//     })
				// }
			})
			.catch((errorInfo) => {
				reject({
					message: errorInfo
				});
			});
	});
};

// 查询功率
export const statActivePower = (params) => {
	let url = `/api/index/statActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				}
				// else {
				//     reject({
				//         message:data.msg
				//     })
				// }
			})
			.catch((errorInfo) => {
				reject({
					message: errorInfo
				});
			});
	});
};

// 查询电量
export const statPositiveAndReverseActivePower = (params) => {
	let url = `/api/index/statPositiveAndReverseActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				}
				// else {
				//     reject({
				//         message:data.msg
				//     })
				// }
			})
			.catch((errorInfo) => {
				reject({
					message: errorInfo
				});
			});
	});
};

// 根据处理状态统计预警数
export const countWarnNumByWarnStatus = (params) => {
	let url = `/api/index/countWarnNumByWarnStatus/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				}
				// else {
				//     reject({
				//         message:data.msg
				//     })
				// }
			})
			.catch((errorInfo) => {
				reject({
					message: errorInfo
				});
			});
	});
};

// 分页查询预警列表
export const findWarnPage = (params) => {
	let url = `/api/index/findWarnPage`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data.records);
				}
				// else {
				//     reject({
				//         message:data.msg
				//     })
				// }
			})
			.catch((errorInfo) => {
				reject({
					message: errorInfo
				});
			});
	});
};

// 保存项目拓扑图
export const saveProjectTopology = (topologyUrl, projectId) => {
	let url = `/api/basic/set/saveProjectTopology?topologyUrl=${topologyUrl}&projectId=${projectId}`;
	console.log(url, 'url');
	return new Promise((resolve, reject) => {
		http
			.post(url)
			.then((data) => {
				if (data.code == 200) {
					resolve(data);
				}
			})
			.catch((errorInfo) => {
				reject({
					message: errorInfo
				});
			});
	});
};

// 查询拓扑图Url
export const getProjectTopology = (projectId) => {
	let url = `/api/basic/set/getProjectTopology/${projectId}`;
	return new Promise((resolve, reject) => {
		http
			.post(url)
			.then((data) => {
				if (data.code == 200) {
					resolve(data);
				}
			})
			.catch((errorInfo) => {
				reject({
					message: errorInfo
				});
			});
	});
};
