<template>
	<div
		class="page_all current_page"
		@click="addItemShow(0)"
	>
		<a
			href="#"
			id="download"
			style="position: absolute; top: -1000px; right: -1000px"
		></a>
		<div class="main_contain">
			<div class="main_contain_scroll_item_list">
				<div class="income_overview income_list_item">
					<div class="title">
						收益总览
						<span class="title_span">({{ time }})</span>
					</div>
					<div class="serach_params_list">
						<a-form
							:model="form_state_echarts_line"
							name="horizontal_login"
							layout="inline"
							autocomplete="off"
							style="margin-bottom: 32px"
						>
							<a-form-item>
								<a-select
									ref="select"
									:getPopupContainer="(e) => e.parentNode"
									v-model:value="day_type"
									style="width: 120px"
								>
									<a-select-option value="month">月</a-select-option>
									<a-select-option value="year">年</a-select-option>
								</a-select>
							</a-form-item>
							<a-form-item name="time">
								<a-range-picker
									:allowClear="false"
									:disabled-date="
										(current) => {
											return current && current > dayjs().endOf(day_type);
										}
									"
									:picker="day_type"
									v-model:value="form_state_echarts_line.time"
									style="width: 300px; "
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/clock.png"
											alt="#"
										/>
									</template>
								</a-range-picker>
							</a-form-item>

							<a-form-item>
								<a-checkbox
									v-model:checked="isContrast"
									@change="isContrastChange"
									style="font-size: 16px"
								>
									对比
								</a-checkbox>
							</a-form-item>

							<a-form-item
								label="添加对比"
								name="compareStartTime"
								v-if="isContrast"
							>
								<a-date-picker
									:allowClear="false"
									:picker="day_type"
									:disabled-date="
										(current) => {
											return current && current > dayjs().endOf(day_type);
										}
									"
									v-model:value="form_state_echarts_line.compareStartTime"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/clock.png"
											alt="#"
										/>
									</template>
								</a-date-picker>
							</a-form-item>

							<a-form-item>
								<a-button
									type="primary"
									@click="getData"
									html-type="submit"
								>
									查询
								</a-button>
							</a-form-item>
							<a-form-item>
								<a-button @click="resetInput">重置</a-button>
							</a-form-item>
						</a-form>
						<!-- <a-button class="button_icon_type" type="primary">
              算法套餐
              <template #icon>
                <div class="ai_icon"></div>
              </template>
            </a-button> -->
					</div>

					<div class="money_list">
						<div
							v-for="(item, index) in show_list"
							:key="index"
							:class="['money_list_item', 'income_1']"
						>
							<div class="sub_title">
								{{ item.typeDesc }} &nbsp;&nbsp;
								<a-tooltip placement="rightTop">
									<template #title>
										<span>{{ item.described }}</span>
									</template>
									<InfoCircleOutlined />
								</a-tooltip>
							</div>
							<div class="num_detail">
								<span class="total">
									<i class="total_num">
										{{ item.totalAmount == null ? '- ' : moneyNumberHandle(item.totalAmount) }}
									</i>
									<i class="unit">元</i>
									<span
										class="percent"
										v-if="item.type != 0 && item.type != 7"
									>
										{{ item.percent == null ? '-' : item.percent }}
										<i>%</i>
									</span>
									<span
										class="percent"
										v-if="item.type == 7"
									>
										<i class="contrast">增益率</i>
										{{ item.upgradeRate == null ? '-' : item.upgradeRate }}
										<i>%</i>
									</span>
								</span>
							</div>
							<img
								class="icon"
								v-if="item.type == 0"
								src="/assets/icon/income_icon0.png"
								alt="#"
							/>
							<img
								class="icon"
								v-if="item.type == 1"
								src="/assets/icon/income_icon2.png"
								alt="#"
							/>
							<img
								class="icon"
								v-if="item.type == 2"
								src="/assets/icon/income_icon1.png"
								alt="#"
							/>
							<img
								class="icon"
								v-if="item.type == 7"
								src="/assets/icon/income_icon7.png"
								alt="#"
							/>
							<img
								class="icon"
								v-if="item.type == 4"
								src="/assets/icon/income_icon3.png"
								alt="#"
							/>
							<template v-if="isContrastCard">
								<div class="contrast_title">收益对比</div>
								<div class="contrast_detail">
									<span class="total">
										<i class="total_num">
											{{ item.totalAmountF == null ? '-' : moneyNumberHandle(item.totalAmountF) }}
										</i>
										<i class="unit">元</i>

										<span class="percent">
											<i class="contrast">变化率</i>
											{{ item.percentF }}
											<i>%</i>
											<span
												v-if="item.totalAmountF < item.totalAmount && item.percentF != '-'"
												class="up_img"
											></span>
											<span
												v-if="item.totalAmountF > item.totalAmount && item.percentF != '-'"
												class="down_img"
											></span>
										</span>
									</span>
								</div>
							</template>
							<div
								v-if="item.type != 0 ? true : false"
								@click="deleteItem(item.type)"
								class="delete_icon"
							></div>
						</div>
						<div
							class="money_list_item add_item"
							@click.stop="addItemShow(1)"
							v-if="show_list.length != income_list.length && show_list.length != 0 ? true : false"
						>
							<img
								class="add_show"
								src="/assets/icon/add_show.png"
								alt="#"
							/>
							<div
								class="select_list"
								v-if="add_item_status"
							>
								<span
									@click="addItem(item.type)"
									:class="[item.status == 1 ? 'disabled' : '']"
									v-for="(item, index) in select_list"
									:key="index"
								>
									{{ item.typeDesc }}
								</span>
							</div>
						</div>

						<a-empty
							v-if="!show_list.length"
							style="margin: 0 auto; position: relative; top: 30%"
						/>
					</div>
					<div class="tap_title">
						<div
							style="
								width: 4px;
								height: 18px;
								background: #1481fd;
								border-radius: 2px;
								margin-right: 10px;
							"
						></div>
						<span>收益分析</span>
						<a-button
							type="primary"
							class="button_icon_type"
							:loading="export_loading"
							style="margin-left: auto"
							@click="exportFn"
						>
							导出
							<template #icon>
								<div class="export_icon"></div>
							</template>
						</a-button>
					</div>
					<div class="map_line_contain">
						<!-- <span class="unit_mess">元</span> -->
						<div
							v-if="hasData"
							class="map_line"
							id="echarts_bar_id"
						></div>
						<a-empty
							v-if="!hasData"
							style="margin: 0 auto; position: relative; top: 30%"
						/>
					</div>
				</div>
				<!-- <div class="gffd map_list income_list_item">
          <div :class="['title', title_close_list[0] ? 'title_close_list' : '']">
            光伏发电
            <a-button type="primary" class="button_icon_type">
              导出
              <template #icon>
                <div class="export_icon"></div>
              </template>
            </a-button>
            <a-button @click="closeMap(0)" class="button_icon_type">
              <template #icon>
                <div
                  :class="[
                    title_close_list[0] ? 'icon_img_close_map' : 'icon_img_expand_map',
                  ]"
                ></div>
              </template>
              <span class="title_span" v-if="!title_close_list[0]">收起</span>
              <span class="title_span" v-if="title_close_list[0]">展开</span>
            </a-button>
          </div>
          <div
            id="echarts_gf"
            :class="['map_charts', title_close_list[0] ? 'map_charts_hidden' : '']"
          ></div>
        </div> -->
				<!-- <div class="cn map_list income_list_item">
          <div :class="['title', title_close_list[1] ? 'title_close_list' : '']">
            储能套利
            <a-button type="primary" class="button_icon_type">
              导出
              <template #icon>
                <div class="export_icon"></div>
              </template>
            </a-button>
            <a-button @click="closeMap(1)" class="button_icon_type">
              <template #icon>
                <div
                  :class="[
                    title_close_list[1] ? 'icon_img_close_map' : 'icon_img_expand_map',
                  ]"
                ></div>
              </template>
              <span class="title_span" v-if="!title_close_list[1]">收起</span>
              <span class="title_span" v-if="title_close_list[1]">展开</span>
            </a-button>
          </div>
          <div
            id="echarts_cn"
            :class="['map_charts', title_close_list[1] ? 'map_charts_hidden' : '']"
          ></div>
        </div> -->
				<!-- <div class="zh map_list income_list_item">
          <div :class="['title', title_close_list[2] ? 'title_close_list' : '']">
            需求侧响应
            <a-button type="primary" class="button_icon_type">
              导出
              <template #icon>
                <div class="export_icon"></div>
              </template>
            </a-button>
            <a-button @click="closeMap(2)" class="button_icon_type">
              <template #icon>
                <div
                  :class="[
                    title_close_list[2] ? 'icon_img_close_map' : 'icon_img_expand_map',
                  ]"
                ></div>
              </template>
              <span class="title_span" v-if="!title_close_list[2]">收起</span>
              <span class="title_span" v-if="title_close_list[2]">展开</span>
            </a-button>
          </div>
          <div
            id="echarts_zh"
            :class="['map_charts', title_close_list[2] ? 'map_charts_hidden' : '']"
          ></div>
        </div> -->
				<!-- <div class="gffd map_list income_list_item">
          <div :class="['title', title_close_list[3] ? 'title_close_list' : '']">
            需量控制
            <a-button type="primary" class="button_icon_type">
              导出
              <template #icon>
                <div class="export_icon"></div>
              </template>
            </a-button>
            <a-button @click="closeMap(3)" class="button_icon_type">
              <template #icon>
                <div
                  :class="[
                    title_close_list[3] ? 'icon_img_close_map' : 'icon_img_expand_map',
                  ]"
                ></div>
              </template>
              <span class="title_span" v-if="!title_close_list[3]">收起</span>
              <span class="title_span" v-if="title_close_list[3]">展开</span>
            </a-button>
          </div>
          <div
            id="echarts_xq"
            :class="['map_charts', title_close_list[3] ? 'map_charts_hidden' : '']"
          ></div>
        </div> -->
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, reactive, onMounted, markRaw, watch } from 'vue';
	import * as echarts from 'echarts';
	import { hexToRgba } from '/assets/js/echartsConfig.js';
	import { getRevenueAnalysis, sumBenefitAmountByType, exportGroup } from './api.js';
	import dayjs from 'dayjs';
	import {
		handleTime,
		handleBarMapOption,
		moneyNumberHandle,
		handleXAxisTime,
		handleBarMapSelection
	} from '/assets/js/commonTool.js';
	import { message } from 'ant-design-vue';
	import { InfoCircleOutlined } from '@ant-design/icons-vue';
	// ***********************************************************************************************************
	//                                                   共有参数                                              //
	// ***********************************************************************************************************

	const isContrast = ref(false);
	const isContrastCard = ref(false);
	const project_id = ref(null);
	const projectName = ref(null);
	const time = dayjs().format('YYYY-MM-DD');

	const form_state_echarts_line = reactive({
		time: [dayjs().startOf('month'), dayjs()],
		compareStartTime: dayjs().subtract(1, 'month')
	});

	const hasData = ref(true);
	const day_type = ref('month');
	const export_loading = ref(false);

	const isContrastChange = () => {
		if (isContrast.value) {
			form_state_echarts_line.compareStartTime = dayjs().subtract(1, day_type.value);
		} else {
			form_state_echarts_line.compareStartTime = null;
		}
	};

	watch(
		() => day_type.value,
		(newVal) => {
			if (isContrast.value) {
				form_state_echarts_line.compareStartTime = dayjs().subtract(1, newVal);
			} else {
				form_state_echarts_line.compareStartTime = null;
			}
		}
	);

	// 导出收益分析数据
	const exportFn = () => {
		if (day_type.value == 'month') {
			if (form_state_echarts_line.time[1].diff(form_state_echarts_line.time[0], 'month') >= 36) {
				message.error('最长可选的时间范围为36月，请修改');
				return;
			}
		} else {
			if (form_state_echarts_line.time[1].diff(form_state_echarts_line.time[0], 'year') >= 5) {
				message.error('最长可选的时间范围为5年，请修改');
				return;
			}
		}

		let params = hanldParams();
		export_loading.value = true;
		exportGroup(params).then((data) => {
			const url = window.URL.createObjectURL(new Blob([data], { type: 'application/zip' }));
			let startTime = '';
			let endTime = '';
			if (day_type.value == 'month') {
				startTime = dayjs(params.caclueDateStart).format('YYYYMM');
				endTime = dayjs(params.caclueDateEnd).format('YYYYMM');
			} else {
				startTime = dayjs(params.caclueDateStart).format('YYYY');
				endTime = dayjs(params.caclueDateEnd).format('YYYY');
			}

			let templateName = startTime + '-' + endTime + projectName.value + '收益曲线报表';
			let link = document.getElementById('download');
			link.href = url;
			link.setAttribute('download', `${templateName}.xlsx`);
			link.click();
			export_loading.value = false;
		});
	};

	// 收益分析数据
	const getEchartsBar = () => {
		let params = hanldParams();
		getRevenueAnalysis(params)
			.then((data) => {
				console.log(data, 'data收益');
				if (data && data.benefitList.length > 0) {
					handleDataEchartsBar(data);
				} else {
					hasData.value = false;
				}
			})
			.catch((error) => {
				console.log(error, '111111111111111111');
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	const colorMap = {
		光伏收益: '#2DCDFC',
		需量优化: '#03E3A1',
		需求侧响应: '#1181FD',
		储能峰谷套利: '#7C8DFF',
		现货交易: '#FEB56E',
		余光入储: '#D59DFD',
		光伏收益对比: hexToRgba('#2DCDFC', 0.5),
		需量优化对比: hexToRgba('#03E3A1', 0.5),
		需求侧响应对比: hexToRgba('#1181FD', 0.5),
		储能峰谷套利对比: hexToRgba('#7C8DFF', 0.5),
		现货交易对比: hexToRgba('#FEB56E', 0.5),
		余光入储对比: hexToRgba('#D59DFD', 0.5)
	};

	// 处理收益分析的数据
	const handleDataEchartsBar = (data) => {
		let x_axis = [];
		let y_axis = [];
		let x_axis_contrast = [];
		let y_axis_contrast = [];
		console.log(day_type, 'day_type');

		x_axis = handleXAxisTime(data.benefitList, day_type.value);
		if (isContrast.value) {
			x_axis_contrast = handleXAxisTime(data.compareBenefitList, day_type.value);
		}

		console.log(data, 'data');

		for (let i = 0; i < data.benefitList[0].detailList.length; i++) {
			let value_list = [];
			let value_list_contrast = [];
			for (let j = 0; j < data.benefitList.length; j++) {
				value_list.push(data.benefitList[j].detailList[i].value);
				if (isContrast.value) {
					// console.log(data.compareBenefitList[j], 'data.compareBenefitList[j]');
					if (data.compareBenefitList[j].detailList != null) {
						value_list_contrast.push(data.compareBenefitList[j].detailList[i].value);
					}
				}
			}
			y_axis.push({
				name: data.benefitList[0].detailList[i].benefitTypeDesc,
				value_list: value_list
			});
			if (isContrast.value) {
				y_axis_contrast.push({
					name: data.compareBenefitList[0].detailList[i].benefitTypeDesc + '对比',
					value_list: value_list_contrast
				});
			}
		}
		console.log(y_axis, 'y_axis');
		console.log(y_axis_contrast, 'y_axis_contrast');

		for (let i = 0; i < y_axis.length; i++) {
			if (y_axis[i].name == '光伏发电') {
				y_axis[i].name = '光伏收益';
			}
			if (isContrast.value) {
				if (y_axis_contrast[i].name == '光伏发电对比') {
					y_axis_contrast[i].name = '光伏收益对比';
				}
			}
		}

		console.log(y_axis, 'y_axis');
		console.log(y_axis_contrast, 'y_axis_contrast');

		let order = [
			'收益总计',
			'光伏收益',
			'储能峰谷套利',
			'余光入储',
			'现货交易',
			'需量优化',
			'需求侧响应'
		];
		let orderContrast = [
			'收益总计对比',
			'光伏收益对比',
			'储能峰谷套利对比',
			'余光入储对比',
			'现货交易对比',
			'需量优化对比',
			'需求侧响应对比'
		];
		y_axis = sortByOrder(y_axis, order);
		y_axis_contrast = sortByOrder(y_axis_contrast, orderContrast);

		yaxis_data_echarts_bar.value = y_axis;
		yaxis_data_echarts_bar_constract.value = y_axis_contrast;
		xaxis_data_echarts_bar.value = x_axis;
		xaxis_data_echarts_bar_constract.value = x_axis_contrast;

		drawMapEchartsBar();
	};

	function sortByOrder(data, order) {
		// 创建一个映射表，将每个 name 映射到其在 order 中的索引
		const orderMap = {};
		order.forEach((item, index) => {
			orderMap[item] = index;
		});

		// 使用 sort 方法根据 orderMap 中的索引排序
		return data.sort((a, b) => {
			return orderMap[a.name] - orderMap[b.name];
		});
	}

	const yaxis_data_echarts_bar = ref([]);
	const yaxis_data_echarts_bar_constract = ref([]);
	const unit_echarts_bar = ref('元');
	const xaxis_data_echarts_bar = ref([]);
	const xaxis_data_echarts_bar_constract = ref([]);
	const my_chart_bar = ref();

	const drawMapEchartsBar = () => {
		let result_option = [];
		let colorList = ['#2DCDFC', '#2DCDFC', '#7C8DFF', '#D59DFD', '#FEB56E', '#03E3A1', '#1181FD'];

		if (isContrast.value) {
			result_option = handleBarMapSelection(
				xaxis_data_echarts_bar.value,
				xaxis_data_echarts_bar_constract.value,
				yaxis_data_echarts_bar.value,
				yaxis_data_echarts_bar_constract.value,
				unit_echarts_bar.value,
				colorList
			);
		} else {
			result_option = handleBarMapSelection(
				xaxis_data_echarts_bar.value,
				'',
				yaxis_data_echarts_bar.value,
				[],
				unit_echarts_bar.value,
				colorList
			);
		}

		let sum = [];
		let sumContrast = [];

		for (let i = 0; i < result_option.series.length; i++) {
			if (colorMap[result_option.series[i].name]) {
				result_option.series[i].itemStyle.color = colorMap[result_option.series[i].name];
			}

			if (result_option.series[i].name == '收益总计') {
				sum = result_option.series[i].data;
			}
			if (result_option.series[i].name == '收益总计对比') {
				sumContrast = result_option.series[i].data;
			}
			if (result_option.series[i].name.includes('对比')) {
				result_option.series[i].stack = 'contrast';
			} else {
				result_option.series[i].stack = 'without';
			}
		}

		// for (let i = 0; i < result_option.series.length; i++) {
		//   if (result_option.series[i].name == '收益总计' || result_option.series[i].name == '收益总计对比') {

		//     delete result_option.series[i]
		//   }
		// }

		result_option.series = result_option.series.filter(
			(item) => item.name !== '收益总计' && item.name !== '收益总计对比'
		);
		result_option.legend.data = result_option.legend.data.filter(
			(item) => item !== '收益总计' && item !== '收益总计对比'
		);

		console.log(result_option.series, 'result_option.series');

		result_option.tooltip = {
			trigger: 'axis',
			confine: true,
			formatter: function (params) {
				let html = '';
				let index = params[0].dataIndex;
				let time = xaxis_data_echarts_bar.value[index];
				let timeCon = xaxis_data_echarts_bar_constract.value[index];

				let sumNum = Number(parseFloat(sum[index]).toFixed(2));
				let sumConNum = Number(parseFloat(sumContrast[index]).toFixed(2));

				if (isNaN(sumNum)) {
					sumNum = '-';
				}

				if (isNaN(sumConNum)) {
					sumConNum = '-';
				}

				html += `<div><span style="font-weight: bold;">总收益 <span>${time}  ${sumNum} 元</div>`;
				if (isContrastCard.value) {
					html += `<div><span style="font-weight: bold;">总收益对比 <span>${timeCon}  ${sumConNum} 元</div>`;
				}
				params.forEach((v) => {
					let value = v.value;
					let unit = '元';
					let time = xaxis_data_echarts_bar.value[v.dataIndex];
					if (v.seriesName.includes('对比')) {
						time = xaxis_data_echarts_bar_constract.value[v.dataIndex];
					}
					if (value != 0 && !value) {
						value = '-';
					} else {
						value = Number(parseFloat(v.value).toFixed(2));
					}

					html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${v.color};"></span>
                    ${v.seriesName} : ${time} <span style="color:${v.color};font-weight:600;font-size: 14px"> ${value} </span>   ${unit} 
                    `;
				});
				return html;
			},
			extraCssText:
				'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;'
		};

		if (my_chart_bar.value) {
			my_chart_bar.value.dispose();
		}

		console.log(result_option, 'result_optionresult_option');

		my_chart_bar.value = markRaw(echarts.init(document.getElementById('echarts_bar_id')));
		my_chart_bar.value.setOption(result_option);
	};

	// 重置查询条件
	const resetInput = () => {
		Object.assign(form_state_echarts_line, {
			time: [dayjs().startOf('day'), dayjs()],
			compareStartTime: null
		});
		day_type.value = 'month';
		isContrast.value = false;
		isContrastCard.value = false;
		getData();
	};
	// const contrast = ref(false);

	const hanldParams = () => {
		let time_result = handleTime(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			day_type.value
		);
		console.log(time_result);
		let caclueDateStart = dayjs(time_result.startTime).startOf(day_type.value).format('YYYY-MM-DD');
		// 因为handleTime的函数处理的结果为标准结果
		//  在为年的时候  结果为YYYY-MM  再次转为YYYY-MM-DD 的时候结束时间存在问题
		let caclueDateEnd = dayjs(time_result.endTime).endOf(day_type.value).format('YYYY-MM-DD');
		if (dayjs(caclueDateEnd).isAfter(dayjs())) {
			// 结束时间如果在当前时间之后则选择当前时间作为结束时间
			caclueDateEnd = dayjs().format('YYYY-MM-DD');
		}

		let compareCaclueDateStart = handleTime(
			form_state_echarts_line.compareStartTime,
			form_state_echarts_line.compareStartTime,
			day_type.value
		).startTime;

		if (isContrast.value) {
			compareCaclueDateStart = dayjs(compareCaclueDateStart)
				.startOf(day_type.value)
				.format('YYYY-MM-DD');
		} else {
			compareCaclueDateStart = null;
		}

		let dateParticle = 2;
		day_type.value == 'month' ? (dateParticle = 2) : (dateParticle = 3);

		let params = {
			caclueDateEnd,
			caclueDateStart,
			compareCaclueDateStart,
			dateParticle,
			projectId: Number(project_id.value)
		};
		return params;
	};

	// ***********************************************************************************************************
	//                                                   共有参数                                              //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                   收益总览列表                                            //
	// ***********************************************************************************************************
	// 原始的数据
	const income_list = ref([
		{ name: '收益总计', status: 1, cla_name: 'income_0', url: '/assets/icon/income_icon3.png' },
		{ name: '光伏发电', status: 1, cla_name: 'income_1', url: '/assets/icon/income_icon2.png' },
		{ name: '储能套利', status: 1, cla_name: 'income_2', url: '/assets/icon/income_icon1.png' },
		{ name: '需量控制', status: 1, cla_name: 'income_3', url: '/assets/icon/income_icon4.png' },
		{ name: '需求侧响应', status: 1, cla_name: 'income_4', url: '/assets/icon/income_icon5.png' }
	]);
	// 展示的数据
	const show_list = ref([]);
	// 最后一个点击添加的可选数据
	const select_list = ref([]);

	// 初始化赋值
	const initHandleData = () => {
		select_list.value = [];
		show_list.value = [];
		for (let i = 0; i < income_list.value.length; i++) {
			if (i > 0) {
				select_list.value.push(income_list.value[i]);
				closeMap(Number(income_list.value[i].type) - 1);
			}
			show_list.value.push(income_list.value[i]);
		}
		console.log(show_list.value, 'show_list.value');
		show_list.value = updateTypeDesc(show_list.value, customTypeMap);
	};

	const updateTypeDesc = (data, typeMap) => {
		data.forEach((item) => {
			if (typeMap[item.type]) {
				item.typeDesc = typeMap[item.type];
			}
		});
		return data;
	};

	let customTypeMap = { 0: '收益总计', 1: '光伏收益', 2: '储能收益', 7: 'EaaS协同增益' };

	// 点击后删除点击的条目
	const deleteItem = (type) => {
		if (type == 0) {
			message.error('收益总计不能被隐藏！');
			return;
		}
		income_list.value.forEach((item) => {
			if (type == item.type) {
				item.status = 0;
			}
		});
		show_list.value = income_list.value.filter(function (item) {
			return item.status != 0;
		});
		select_list.value = income_list.value.filter(function (item) {
			return item.type != 0;
		});
	};

	// 点击添加展示的数据
	const add_item_status = ref(false);
	const addItemShow = (mess) => {
		if (mess) {
			// 标准点击正常执行
			add_item_status.value = !add_item_status.value;
		} else {
			add_item_status.value = false;
		}
	};
	// 增加一个展示的条目
	const addItem = (type) => {
		income_list.value.forEach((item) => {
			if (type == item.type) {
				item.status = 1;
			}
		});
		show_list.value = income_list.value.filter(function (item) {
			return item.status != 0;
		});
		select_list.value = income_list.value.filter(function (item) {
			return item.type != 0;
		});

		add_item_status.value = false;
	};

	const getIncomeOverview = () => {
		let params = hanldParams();
		sumBenefitAmountByType(params)
			.then((data) => {
				handlIncomeData(data);
			})
			.catch((error) => {
				console.log(error, '111111111');
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};
	const countPercent = (current, old) => {
		let numerator = Math.abs(current - old);
		let denominator = Math.abs(old);
		let percent = Math.trunc((numerator / denominator) * 10000) / 100;
		console.log(numerator, 'numerator');

		return percent;
	};

	const handlIncomeData = (data) => {
		let temp_data = [];
		let status = 1;

		if (data.detailList.length == 0) {
			// 处理初始数据
			select_list.value = [];
			show_list.value = [];
			return;
		}
		let total = 0;
		for (let i = 0; i < data.detailList.length; i++) {
			if (
				data.detailList[i].type != 0 &&
				data.detailList[i].type != 7 &&
				data.detailList[i].totalAmount > 0
			) {
				total = total + data.detailList[i].totalAmount;
			}
			// if (data.detailList[i].type == 7 && data.detailList[i].totalIncreaseAmount > 0) {
			// 	total = total + data.detailList[i].totalIncreaseAmount;
			// }
		}

		console.log(total, 'total');

		for (let i = 0; i < data.detailList.length; i++) {
			let item = data.detailList[i];
			let { type, typeDesc, totalAmount, totalIncreaseAmount, upgradeRate } = item;
			let described = tooltipList.value[i];
			if (item.type != 7) {
				totalAmount = totalAmount;
			} else {
				totalAmount = totalIncreaseAmount;
			}
			// totalIncreaseAmount = totalIncreaseAmount;
			let percent = Number(parseFloat(Math.abs(totalAmount * 100) / total).toFixed(2));
			if (upgradeRate == null) {
				upgradeRate = '-';
			} else {
				upgradeRate = Number(upgradeRate * 100).toFixed(2);
			}
			if (totalAmount < 0 || Number.isNaN(percent) || totalAmount == null) {
				percent = '-';
			}
			let totalAmountF = '-';
			let percentF = '-';

			for (let j = 0; j < data.compareDetailList.length; j++) {
				let item1 = data.compareDetailList[j];
				if (item.type == item1.type && item.type != 7) {
					totalAmountF = item1.totalAmount;
					let singal_p_n = '';
					if (item.totalAmount - item1.totalAmount < 0) {
						singal_p_n = '-';
					}
					percentF = singal_p_n + countPercent(item.totalAmount, item1.totalAmount);
					break;
				} else if (item.type == item1.type && item.type == 7) {
					totalAmountF = item1.totalIncreaseAmount;
					let singal_p_n = '';
					if (item.totalIncreaseAmount - item1.totalIncreaseAmount < 0) {
						singal_p_n = '-';
					}
					percentF = singal_p_n + countPercent(item.totalIncreaseAmount, item1.totalIncreaseAmount);
					break;
				}
			}

			if (
				Number.isNaN(percentF) ||
				percentF == 'NaN' ||
				percentF == 'Infinity' ||
				percentF == '-Infinity'
			) {
				percentF = '-';
				console.log(percentF, 'percentFNaN');
			}

			temp_data.push({
				typeDesc,
				status,
				type,
				totalAmount,
				upgradeRate,
				// totalIncreaseAmount,
				percent,
				totalAmountF,
				percentF,
				described
			});

			console.log(temp_data, 'temp_data');
		}
		temp_data.sort((a, b) => {
			return a.type - b.type;
		});

		income_list.value = temp_data;
		initHandleData();
	};

	const getData = () => {
		if (day_type.value == 'month') {
			if (form_state_echarts_line.time[1].diff(form_state_echarts_line.time[0], 'month') >= 36) {
				message.error('最长可选的时间范围为36月，请修改');
				return;
			}
		} else {
			if (form_state_echarts_line.time[1].diff(form_state_echarts_line.time[0], 'year') >= 5) {
				message.error('最长可选的时间范围为5年，请修改');
				return;
			}
		}

		title_close_list.value = [true, true, true, true];
		getIncomeOverview();
		getEchartsBar();

		if (isContrast.value) {
			isContrastCard.value = true;
		} else {
			isContrastCard.value = false;
		}
		// getGfData();
		// getCnData();
		// getZhData();
		// getXqData();
	};
	// ***********************************************************************************************************
	//                                                 收益总览列表                                              //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                 光伏发电                                                //
	// ***********************************************************************************************************

	// const getGfData = () => {
	//   let params = hanldParams();
	//   statPhotovoltaicPower(params)
	//     .then((data) => {
	//       drawMapEchartsBarGf(data);
	//     })
	//     .catch((error) => {
	//       message.error(error.message);
	//       setTimeout(() => {
	//         message.destroy();
	//       }, 1500);
	//     });
	// };

	const my_chart_bar_gf = ref();
	const drawMapEchartsBarGf = (data) => {
		let y_axis = [];
		let x_axis = [];
		let y_axis_contrast = [];
		let x_axis_contrast = [];
		let unit = '元';
		// 原始值
		for (let i = 0; i < data.benefitList.length; i++) {
			let item = data.benefitList[i];
			x_axis.push(item.timePointer);
			y_axis.push(item.value);
		}
		// 对比值
		for (let j = 0; j < data.compareBenefitList.length; j++) {
			let item = data.compareBenefitList[j];
			x_axis_contrast.push(item.timePointer);
			y_axis_contrast.push(item.value);
		}

		let result_option = handleBarMapOption(
			x_axis,
			x_axis_contrast,
			[{ name: '光伏', value_list: y_axis }],
			[{ name: '光伏对比', value_list: y_axis_contrast }],
			unit,
			'#03E3A1',
			'#cadbee'
		);
		if (my_chart_bar_gf.value) {
			my_chart_bar_gf.value.dispose();
		}
		my_chart_bar_gf.value = markRaw(echarts.init(document.getElementById('echarts_gf')));
		my_chart_bar_gf.value.setOption(result_option);
	};

	// ***********************************************************************************************************
	//                                                 光伏发电                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                 储能套利                                                //
	// ***********************************************************************************************************

	// const getCnData = () => {
	//   let params = hanldParams();
	//   statEnergyStorageArbitrage(params)
	//     .then((data) => {
	//       drawMapEchartsBarCn(data);
	//     })
	//     .catch((error) => {
	//       message.error(error.message);
	//       setTimeout(() => {
	//         message.destroy();
	//       }, 1500);
	//     });
	// };
	const my_chart_bar_cn = ref();
	const drawMapEchartsBarCn = (data) => {
		let y_axis = [];
		let x_axis = [];
		let y_axis_contrast = [];
		let x_axis_contrast = [];
		let unit = '元';
		// 原始值
		for (let i = 0; i < data.benefitList.length; i++) {
			let item = data.benefitList[i];
			x_axis.push(item.timePointer);
			y_axis.push(item.value);
		}
		// 对比值
		for (let j = 0; j < data.compareBenefitList.length; j++) {
			let item = data.compareBenefitList[j];
			x_axis_contrast.push(item.timePointer);
			y_axis_contrast.push(item.value);
		}

		let result_option = handleBarMapOption(
			x_axis,
			x_axis_contrast,
			[{ name: '储能', value_list: y_axis }],
			[{ name: '储能对比', value_list: y_axis_contrast }],
			unit,
			'#1181FD'
		);
		if (my_chart_bar_cn.value) {
			my_chart_bar_cn.value.dispose();
		}
		my_chart_bar_cn.value = markRaw(echarts.init(document.getElementById('echarts_cn')));
		my_chart_bar_cn.value.setOption(result_option);
	};
	// ***********************************************************************************************************
	//                                                 储能套利                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                 需求侧响应                                                //
	// ***********************************************************************************************************

	// const getZhData = () => {
	//   let params = hanldParams();
	//   statComprehensiveOptimization(params)
	//     .then((data) => {
	//       drawMapEchartsBarZh(data);
	//     })
	//     .catch((error) => {
	//       message.error(error.message);
	//       setTimeout(() => {
	//         message.destroy();
	//       }, 1500);
	//     });
	// };
	const tooltipList = ref([
		'项目总收益包含光伏收益、储能收益',
		'光伏收益包含余光入储（光伏侧）部分收益',
		'储能收益包含余光入储（储能侧）部分收益',
		'EaaS协同增益是指微电网运行策略相较于传统储能模式下的多种策略增益总和，即整体增益 = 光储协同增益 + 现货增益 + 需求侧响应增益+ 需量增益'
	]);
	const my_chart_bar_zh = ref();
	const drawMapEchartsBarZh = (data) => {
		let y_axis = [];
		let x_axis = [];
		let y_axis_contrast = [];
		let x_axis_contrast = [];
		let unit = '元';
		// 原始值
		for (let i = 0; i < data.benefitList.length; i++) {
			let item = data.benefitList[i];
			x_axis.push(item.timePointer);
			y_axis.push(item.value);
		}
		// 对比值
		for (let j = 0; j < data.compareBenefitList.length; j++) {
			let item = data.compareBenefitList[j];
			x_axis_contrast.push(item.timePointer);
			y_axis_contrast.push(item.value);
		}

		let result_option = handleBarMapOption(
			x_axis,
			x_axis_contrast,
			[{ name: '需求侧响应', value_list: y_axis }],
			[{ name: '需求侧响应对比', value_list: y_axis_contrast }],
			unit,
			'#03E3A1'
		);
		if (my_chart_bar_zh.value) {
			my_chart_bar_zh.value.dispose();
		}
		my_chart_bar_zh.value = markRaw(echarts.init(document.getElementById('echarts_zh')));
		my_chart_bar_zh.value.setOption(result_option);
	};
	// ***********************************************************************************************************
	//                                                 需求侧响应                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                 需量控制                                                //
	// ***********************************************************************************************************

	// const getXqData = () => {
	//   let params = hanldParams();
	//   statDemandResponse(params)
	//     .then((data) => {
	//       drawMapEchartsBarXq(data);
	//     })
	//     .catch((error) => {
	//       message.error(error.message);
	//       setTimeout(() => {
	//         message.destroy();
	//       }, 1500);
	//     });
	// };
	const my_chart_bar_xq = ref();
	const drawMapEchartsBarXq = (data) => {
		let y_axis = [];
		let x_axis = [];
		let y_axis_contrast = [];
		let x_axis_contrast = [];
		let unit = '元';
		// 原始值
		for (let i = 0; i < data.benefitList.length; i++) {
			let item = data.benefitList[i];
			x_axis.push(item.timePointer);
			y_axis.push(item.value);
		}
		// 对比值
		for (let j = 0; j < data.compareBenefitList.length; j++) {
			let item = data.compareBenefitList[j];
			x_axis_contrast.push(item.timePointer);
			y_axis_contrast.push(item.value);
		}

		let result_option = handleBarMapOption(
			x_axis,
			x_axis_contrast,
			[{ name: '需量控制', value_list: y_axis }],
			[{ name: '需求响应对比', value_list: y_axis_contrast }],
			unit,
			'#1181FD'
		);
		if (my_chart_bar_xq.value) {
			my_chart_bar_xq.value.dispose();
		}
		my_chart_bar_xq.value = markRaw(echarts.init(document.getElementById('echarts_xq')));
		my_chart_bar_xq.value.setOption(result_option);
	};

	// ***********************************************************************************************************
	//                                                 需量控制                                                //
	// ***********************************************************************************************************
	const title_close_list = ref([true, true, true, true]);
	const closeMap = (mess) => {
		title_close_list.value[mess] = !title_close_list.value[mess];
	};

	// ***********************************************************************************************************
	//                                                 需量控制                                                //
	// ***********************************************************************************************************

	onMounted(() => {
		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			project_id.value = project_mess.project_id;
			projectName.value = project_mess.project_title;
		} else {
			message.error('未选择项目，请重新选择！');
		}
		getData();
		// getGfData();
		// getCnData();
		// getZhData();
		// getXqData();
	});
</script>

<style lang="less" scoped>
	.current_page {
		padding-top: 0 !important;
	}

	.income_overview {
		padding-bottom: 12px !important;
	}

	.income_list_item {
		padding: 32px;
		box-sizing: border-box;
		background-color: rgba(255, 255, 255, 1);
		margin-bottom: 32px;
		border-radius: 8px;

		.title {
			width: 100%;
			height: 36px;
			font-family: SourceHanSansCN-Regular;
			font-weight: bold;
			font-size: 18px;
			color: #092649;
			margin-bottom: 32px;
			overflow: hidden;

			span {
				font-family: SourceHanSansCN-Regular;
				font-weight: 400;
				font-size: 14px;
				color: #9ca2af;
			}

			button {
				float: right;

				&:last-child {
					margin-right: 15px;
				}
			}
		}

		.title_close_list {
			margin-bottom: 0px;
		}

		.serach_params_list {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
		}
	}

	.map_list {
		width: 100%;
		position: relative;
		box-sizing: border-box;
	}

	.tap_title {
		box-sizing: border-box;
		padding: 21px;
		height: 56px;
		background: rgba(242, 248, 255, 1);
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		margin-bottom: 32px;

		span {
			font-family: SourceHanSansCN-Bold;
			font-weight: bold;
			font-size: 16px;
			height: 16px;
			line-height: 16px;
			color: #092649;
		}

		i {
			font-style: normal;
			font-size: 12px;
			height: 12px;
			line-height: 12px;
		}
	}

	.money_list {
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		flex-wrap: wrap;

		.money_list_item {
			// min-width: calc(20% - 24px);
			width: calc(25% - 24px);
			// width: max-content;
			box-sizing: border-box;
			margin-bottom: 34px;
			margin-right: 30px;
			padding: 24px 32px 0 32px;
			border-radius: 8px;
			// min-height: 126px;
			position: relative;
			top: 0;
			transition: all 0.5s ease-in-out;

			&:last-child {
				margin-right: 0 !important;
			}

			.sub_title,
			.contrast_title {
				height: 16px;
				font-family: SourceHanSansCN-Regular;
				font-weight: 400;
				font-size: 16px;
				line-height: 16px;
				color: #9ca2af;
				margin-bottom: 15px;
			}

			.num_detail,
			.contrast_detail {
				height: 25px;
				font-size: 25px;
				margin-bottom: 32px;

				i {
					font-style: normal;
					font-size: 12px;
				}
			}

			.total {
				display: block;
				height: 32px;
				line-height: 32px;
				box-sizing: border-box;

				// padding-right: 101px;
				.total_num {
					font-family: D-DIN-PRO;
					font-weight: bold;
					font-size: 32px;
					color: #092649;
				}

				.unit {
					font-family: SourceHanSansCN-Regular;
					font-weight: 400;
					font-size: 14px;
					color: #9ca2af;
				}

				.contrast {
					font-family: SourceHanSansCN-Regular;
					font-weight: 400;
					font-size: 14px;
					color: #9ca2af;
					margin-right: 6px;
				}

				.percent {
					margin-left: 24px;
					font-family: D-DIN-PRO;
					font-weight: bold;
					font-size: 18px;
					color: #092649;

					i {
						font-family: D-DIN-PRO;
						font-weight: 400;
						font-size: 14px;
						color: #9ca2af;
					}
				}
			}

			img {
				height: 101px;
				width: 101px;
				position: absolute;
				top: 18px;
				right: 4%;
			}

			.delete_icon {
				display: none;
				height: 27px;
				width: 27px;
				border-radius: 50%;
				position: absolute;
				top: -16px;
				right: -16px;
				cursor: pointer;
				background-image: url('/assets/icon/rule_delete.png');
				background-repeat: no-repeat;
				background-size: 100%;
			}

			&:hover {
				.delete_icon {
					display: block;
					background-image: url('/assets/icon/rule_delete_hover.png');
					background-repeat: no-repeat;
					background-size: 100%;
				}

				top: -5px;
			}
		}

		.add_item {
			position: relative;
			background: linear-gradient(90deg, rgba(17, 129, 253, 0.12), rgba(17, 129, 253, 0.02));

			.add_show {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				cursor: pointer;
			}

			.select_list {
				cursor: pointer;
				height: 160px;
				width: 148px;
				position: absolute;
				top: 50%;
				right: -20px;
				background: #ffffff;
				box-shadow: 0px 6px 21px 0px rgba(12, 44, 109, 0.27);
				border-radius: 8px;
				transform: translate(0%, -50%);
				padding: 8px 0;
				box-sizing: border-box;
				display: flex;
				flex-direction: column;
				justify-content: flex-start;

				.disabled {
					background: rgba(156, 162, 175, 0.3);
					cursor: not-allowed;
				}

				span {
					display: block;
					height: 36px;
					line-height: 36px;
					text-align: center;

					&:hover {
						background: #edf4fc;
					}
				}
			}
		}

		.income_0 {
			background: linear-gradient(90deg, rgba(17, 129, 253, 0.12), rgba(17, 129, 253, 0.02));
		}

		.income_1 {
			background: linear-gradient(90deg, rgba(17, 129, 253, 0.12), rgba(17, 129, 253, 0.02));
		}

		// .income_2 {
		//   background: linear-gradient(90deg,
		//       rgba(121, 133, 252, 0.12),
		//       rgba(121, 133, 252, 0.02));
		// }

		// .income_3 {
		//   background: linear-gradient(90deg,
		//       rgba(17, 129, 253, 0.12),
		//       rgba(17, 129, 253, 0.02));
		// }

		// .income_4 {
		//   background: linear-gradient(90deg, rgba(3, 227, 161, 0.12), rgba(3, 227, 161, 0.02));
		// }
	}

	.map_charts {
		height: 414px;
		width: 100%;
	}

	.map_charts_hidden {
		opacity: 0;
		float: right;
		position: absolute !important;
		right: 2000px;
	}

	.icon_img_expand_map {
		background-image: url('/assets/icon/expand_map.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		height: 18px;
		width: 18px;
	}

	.icon_img_close_map {
		background-image: url('/assets/icon/close_map.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		height: 18px;
		width: 18px;
	}

	.export_icon {
		display: block;
		height: 18px;
		width: 18px;
		background-image: url('/assets/icon/export_icon.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
	}

	.ai_icon {
		//background-image: url('/assets/icon/ai_icon.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		height: 18px;
		width: 18px;
	}

	.up_img {
		width: 10px;
		height: 14px;
		display: inline-block;
		background: url('/assets/icon/up_percent.png');
		background-repeat: no-repeat;
		background-size: 100%;
		margin-left: 5px;
		position: relative;
		top: 2px;
	}

	.down_img {
		width: 10px;
		height: 14px;
		display: inline-block;
		background: url('/assets/icon/down_percent.png');
		background-repeat: no-repeat;
		background-size: 100%;
		margin-left: 5px;
		position: relative;
		top: 2px;
	}

	.map_line_contain {
		height: 426px;
		width: 100%;
		position: relative;

		.unit_mess {
			color: #949aa6;
			font-size: 14px;
			font-family: 'shsr';
			position: absolute;
			top: 5px;
		}

		.map_line {
			height: 100%;
			width: 100%;
		}

		.no_data {
			position: absolute;
			top: -5%;
			left: 0;
			right: 0;
			bottom: 0;
			padding-top: 0px;
		}
	}
</style>
