import http from '/tool/http.js'


// 查询综合优化
export const statComprehensiveOptimization = (params) => {
    let url = "/api/overview/statComprehensiveOptimization";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            ////console.log(data)
            if (data.code == 200) {
                resolve(data.data)
            } else {
                reject({
                    message:data.msg
                })
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}

// 查询需求响应
export const statDemandResponse = (params) => {
    let url = "/api/overview/statDemandResponse";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200 ) {
                resolve(data.data)
            } else {
                reject({
                    message:data.msg
                })
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}

// 查询储能套利
export const statEnergyStorageArbitrage = (params) => {
    let url = "/api/overview/statEnergyStorageArbitrage";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            ////console.log(data)
            if (data.code == 200) {
                resolve(data.data)
            } else {
                reject({
                    message:data.msg
                })
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}

// 查询光伏发电
export const statPhotovoltaicPower = (params) => {
    
    let url = "/api/overview/statPhotovoltaicPower";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            ////console.log(data)
            if (data.code == 200 ) {
                // ////console.log(data.dataList)
                resolve(data.data)
            } else {
                reject({
                    message:data.msg
                })
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}


// 查询收益总览
export const sumBenefitAmountByType = (params) => {
    
    let url = "/api/overview/sumBenefitAmountByType";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            ////console.log(data)
            if (data.code == 200 ) {
                //console.log(data.dataList)
                resolve(data.data)
            } else {
                reject({
                    message:data.msg
                })
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}

// 查询收益分析数据
export const getRevenueAnalysis = (params) => {

    let url = "/api/overview/statGroup";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200 ) {
                resolve(data.data)
            } else {
                reject({
                    message:data.msg
                })
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}

// 导出收益分析数据

export const exportGroup = (params) => {
    let url = '/api/overview/exportGroup';
    return new Promise((resolve, reject) => {
            http.post(url, params, {
                headers: {
                    "content-type": "application/json; charset=utf-8"
                },
                responseType: "blob",
            }).then((data) => {
                resolve(data)
            }).catch((errorInfo) => {
                reject({
                    message: errorInfo
                })
            });
        })
}