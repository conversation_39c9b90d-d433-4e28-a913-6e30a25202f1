<template>
	<div class="login_form">
		<a-form
			id="login"
			ref="login"
			:model="formState"
			name="basic"
			:label-col="{ span: 6 }"
			:wrapper-col="{ span: 18 }"
			autocomplete="off"
		>
			<a-form-item
				label="userNo"
				name="userNo"
				:rules="[{ required: true, message: 'Please input your username!' }]"
			>
				<a-input
					autocomplete="off"
					placeholder="请输入账号"
					v-model:value="formState.userNo"
				/>
			</a-form-item>

			<a-form-item
				label="Password"
				name="password"
				:rules="[{ required: true, message: 'Please input your password!' }]"
			>
				<a-input-password
					autocomplete="off"
					placeholder="请输入密码"
					v-model:value="formState.password"
				/>
			</a-form-item>
			<a-row>
				<a-col :span="18">
					<a-form-item
						:label-col="{ span: 8 }"
						:wrapper-col="{ span: 15 }"
						name="captcha"
						label="验证码"
						:rules="[{ required: true, message: '请输入验证码!' }]"
					>
						<a-input
							autocomplete="off"
							placeholder="请输入验证码"
							v-model:value="formState.captcha"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="6">
					<a-form-item>
						<a-button
							style="width: 100px"
							@click="getRefreshCode"
							type="primary"
						>
							{{ formState.codeRefresh }}
						</a-button>
					</a-form-item>
				</a-col>
			</a-row>

			<a-form-item :wrapper-col="{ offset: 8, span: 16 }">
				<a-button
					type="primary"
					@click="submit"
				>
					登录
				</a-button>
			</a-form-item>
		</a-form>
		<!-- <ProjectSelect
      v-if="showHideModalStatus"
      @showHideModal="showHideModal"
      :showHideModalStatus="showHideModalStatus"
    ></ProjectSelect> -->

		<!-- <Loading :has_data="has_data"></Loading> -->
	</div>
</template>

<script setup>
	import { reactive, ref } from 'vue';
	import { getCode, loginFn } from './loginHttp.js';
	import { message } from 'ant-design-vue';
	// import ProjectSelect from '/components/ProjectSelect.vue'
	import {
		getAllAreaHttp,
		projectidGetMenuHttp,
		getUserInfo,
		getAuthProjects
	} from '/components/commonApi.js';
	import { handleProjectTree, getFirstProject, projectChangeMenu } from './tool.js';
	// import Loading from '/components/Loading.vue'

	// const has_data = ref(false)

	const formState = reactive({
		userNo: null,
		password: null,
		captcha: null,
		codeRefresh: '获取验证码'
	});

	// 获取验证码
	const getRefreshCode = () => {
		getCode().then((data) => {
			formState.codeRefresh = data;
		});
	};

	const login = ref();
	const submit = () => {
		login.value.validateFields().then((data) => {
			let { captcha, userNo, password } = data;
			let params = { captcha, userNo, password };
			loginFn(params)
				.then((data) => {
					if (data.code == 200) {
						//  获取到token
						localStorage.setItem('token', data.data);
						// 获取用户信息
						// /user/getUserInfo
						getUserInfo().then((data) => {
							if (data) {
								localStorage.setItem('operaType', data.operaType);
							} else {
								localStorage.setItem('operaType', 0);
							}
						});
						// *******************模拟选中了一个项目 默认选中第一个
						getAuthProjects()
							.then((data) => {
								let project_mess = {};
								let url = null;
								if (data[0].bigscreenUrl) {
									url = data[0].bigscreenUrl;
								}
								if (data.length > 0) {
									project_mess = {
										project_id: data[0].projectId,
										project_title: data[0].projectName,
										project_bigscreenUrl: url,
										collectionInterval: data[0].collectionInterval
									};
								}
								//   存储project_id
								localStorage.setItem('project_mess', JSON.stringify(project_mess));
								// / 处理项目的树状结构并保存本地存储
								// let project_tree = handleProjectTree(data)
								// localStorage.setItem('project_tree', JSON.stringify(project_tree))
								return project_mess.project_id;
							})
							.then((project_id) => {
								// 查询菜单
								projectidGetMenuHttp(project_id)
									.then((data) => {
										console.log(data);
										let current_menu_localstorage = projectChangeMenu(data);
										localStorage.setItem(
											'current_menu_localstorage',
											JSON.stringify(current_menu_localstorage)
										);
										// 跳转到选中的菜单
										message.success('登录成功！');
										setTimeout(() => {
											window.location.href = current_menu_localstorage.current_skip_url;
										}, 1000);
									})
									.catch((error) => {
										console.log(error);
										message.error(error.message);
										setTimeout(() => {
											message.destroy();
										}, 1500);
									});
							});

						// *******************模拟选中了一个项目 默认选中第一个
					} else {
						message.error(data.msg);
					}
				})
				.catch((error) => {
					message.error(error.message);
				});
		});
	};

	// const showHideModalStatus = ref(false)
	// const showHideModal = () => {
	//   // 选中项目后的回调
	//   showHideModalStatus.value = !showHideModalStatus.value
	// }
</script>
<style lang="less" scoped>
	.login_form {
		padding: 20px;
		background-color: rgba(255, 255, 255, 0.3);
		position: absolute;
		top: 50%;
		left: 50%;
		min-width: 400px;
		transform: translate(-50%, -50%);
		border-radius: 5px;
	}
</style>
