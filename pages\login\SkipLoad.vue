<template>
  <div>
    <img
      style="
        margin: 0 auto;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      "
      src="/assets/icon/loading-big.gif"
      alt="loading"
    />
    <ProjectSelect
      v-if="showHideModalStatus"
      @showHideModal="showHideModal"
      :showHideModalStatus="showHideModalStatus"
    ></ProjectSelect>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import ProjectSelect from '/components/ProjectSelect.vue'
import { getAllAreaHttp, projectidGetMenuHttp, getUserInfo, getAuthProjects } from '/components/commonApi.js'
import { handleProjectTree, getFirstProject, projectChangeMenu } from './tool.js'
import { ssoLogin, getUserAuth, execUrl } from './loginHttp.js'
import { message } from 'ant-design-vue'
const showHideModalStatus = ref(false)
const showHideModal = () => {
  // 选中项目后的回调
  showHideModalStatus.value = !showHideModalStatus.value
}

onMounted(() => {
  console.log(window.location.hash)
  let paramsString = window.location.hash.split('?')[1]
  // console.log("xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx")
  // console.log(paramsString);
  // 没有传递参数的情况需要跳转到登录页
  if (!paramsString) {
    console.log('地址路径有问题，需要跳转生态平台')

    // execUrl().then((data) => {
    //   console.log(data)
    //   window.location.href = data
    // })
    return
  }
  let paramsArr = paramsString.split('&')
  let userId = null,
    random = null,
    type = null
  for (let index = 0; index < paramsArr.length; index++) {
    let params = paramsArr[index].split('=')
    if (params[0] == 'userId') {
      userId = params[1]
    }
    if (params[0] == 'random') {
      random = params[1]
    }
    if (params[0] == 'type') {
      type = params[1]
    }
  }
  // 没有传递参数的情况需要跳转到登录页
  if (!userId || !random || !type) {
    console.log('缺少参数')

    // execUrl().then((data) => {
    //   window.location.href = data
    // })
    return
    console.log('缺少参数结果')
  }
  // console.log(userId, random,type);
  // 获取数据并调用 同步操作
  let params = {
    userId,
    random
  }
  getUserAuth(params)
    .then((response) => {
      // return
      const data = response.data
      const status = response.code

      if (status == 401) {
        window.location.replace(data)
        return
      } else {
        // 登录
        console.log(data)
        localStorage.setItem('token', data)
        getUserInfo().then((data) => {
          if (data) {
            localStorage.setItem('operaType', data.operaType)
          } else {
            localStorage.setItem('operaType', 0)
          }
        })
        // *******************模拟选中了一个项目 默认选中第一个
        getAuthProjects()
            .then((data) => {
              // 存储状态是否来自生态平台
              localStorage.setItem('stpt', type);
              let project_mess = {};
              let url = null;
              if (data[0].bigscreenUrl) {
                url = data[0].bigscreenUrl;
              }
              if (data.length > 0) {
                project_mess = {
                  project_id: data[0].projectId,
                  project_title: data[0].projectName,
                  project_bigscreenUrl: url,
                  collectionInterval: data[0].collectionInterval
                };
              }
              //   存储project_id
              localStorage.setItem('project_mess', JSON.stringify(project_mess));
              // let project_mess = getFirstProject(data)
              // //   存储project_id
              // localStorage.setItem('project_mess', JSON.stringify(project_mess))
              // // / 处理项目的树状结构并保存本地存储
              // let project_tree = handleProjectTree(data)
              // localStorage.setItem('project_tree', JSON.stringify(project_tree))
              return project_mess.project_id;
            })
          .then((project_id) => {
            // 查询菜单
            projectidGetMenuHttp(project_id)
              .then((data) => {
                let current_menu_localstorage = projectChangeMenu(data)
                localStorage.setItem(
                  'current_menu_localstorage',
                  JSON.stringify(current_menu_localstorage)
                )
                // 跳转到选中的菜单
                message.success('登录成功！')
                setTimeout(() => {
                  window.location.href = current_menu_localstorage.current_skip_url
                }, 1000)
              })
              .catch((error) => {
                message.error(error.message)
                setTimeout(() => {
                  message.destroy()
                }, 1500)
              })
          })

        // *******************模拟选中了一个项目 默认选中第一个
        // showHideModal()
      }
    })
    .catch(() => {
      message.error('登录失败！')
      setTimeout(() => {
        message.destroy()
      }, 1500)
      //   execUrl().then((data) => {
      //     window.location.href = data
      //   })
    })
})
</script>
<style lang="less" scoped></style>
