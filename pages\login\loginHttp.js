
import axios from "axios";
import http from '/tool/http.js'
import httpUnique from '/tool/httpUnique.js'
export const getCode = () => {
    return new Promise((resolve, reject) => {
        axios({
            method: 'post',
            url: '/api/auth/captcha',
            data: {}
        }).then((data) => {
            resolve(data.data.data)
        }).catch((err) => {
            reject({
                message: err
            })
        });
    })
}

export const loginFn = (params) => {
    return new Promise((resolve, reject) => {
        axios({
            method: 'post',
            url: '/api/auth/login',
            data: {
                "captcha": params.captcha,
                "password": params.password,
                "userNo": params.userNo
            }
        }).then((data) => {
            resolve(data.data)
        }).catch((err) => {
            reject({
                message: err
            })
        });
    })

}


// 跳转单点登录的页面

export const ssoLogin = () => {
    return new Promise((resolve, reject) => {
        axios({
            method: 'get',
            url: '/api/sso/login'
        }).then((data) => {
            resolve(data.data)
        }).catch((err) => {
            reject({
                message: err
            })
        });
    })

}

// 根据生态平台回调，获取本地saas用户的权限
export const getUserAuth = (params) => {
    return new Promise((resolve, reject) => {
        axios({
            method: 'post',
            url: `/api/sso/getUserAuth?userId=${params.userId}&random=${params.random}`,
            // params: {
            //     "userId": params.userId,
            //     "random": params.random
            // }
        }).then((data) => {
            resolve(data.data)
        }).catch((err) => {
            reject({
                message: err
            })
        });
    })

}




// 返回登录地址 
export const execUrl = () => {
    return new Promise((resolve, reject) => {
        axios({
            method: 'post',
            url: '/api/sso/execUrl'
        }).then((data) => {
            if (data.data.code == 200) {
                resolve(data.data.data)
            }
        }).catch((err) => {
            reject({
                message: err
            })
        });
    })

}

// 退出登陆
export const logout = () => {
    return new Promise((resolve, reject) => {
        let url = '/api/sso/logout'
        httpUnique.post(url, {}).then((data) => {
            console.log(data);
            if (data.data.code == 200) {
                localStorage.clear()
                resolve(data.data.data)
            } else {
                reject({
                    message: data.msg
                })
            }
        }).catch((errorInfo) => {
            reject({
                message: errorInfo
            })
        });

    })

}

//    非生态平台退出
export const logout1 = () => {
    return new Promise((resolve, reject) => {
        let url = '/api/auth/logout'
        console.log("xguoqi2");
        httpUnique.post(url, {}).then(() => {
            localStorage.clear()
            resolve()
        }).catch((errorInfo) => {
            reject({
                message: errorInfo
            })
        });

    })

}


// export const STPTLoginOut = ()=>{
//     let params = {

//     }
//     axios({
//         method: 'post',
//         url: '/api/auth/login',
//         data: {
//             "captcha": params.captcha,
//             "password": params.password,
//             "userNo": params.userNo
//         }
//     }).then((data) => {
//         resolve(data.data)
//     }).catch((err) => {
//         reject({
//             message: err
//         })
//     });
// }