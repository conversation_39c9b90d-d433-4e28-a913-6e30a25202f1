
// 获取第一个可选中的项目
export const getFirstProject = (data) => {
    let project_id = null
    let project_title = null
    for (let i = 0; i < data.length; i++) {
        const area = data[i]
        if (area.parkList.length == 0) {
            continue
        }
        for (let j = 0; j < area.parkList.length; j++) {
            const park = area.parkList[j]
            if (park.proList.length == 0) {
                continue
            }
            project_id = park.proList[0].projectId
            project_title = park.proList[0].projectName
            break
        }
        if (project_id) {
            break
        }
    }

    return {
        project_id,
        project_title
    }

}

// 处理项目的树状结构并保存本地存储
export const handleProjectTree = (data) => {
    let all_list = []
    data.forEach((item) => {
        let { regionName: title, regionId: key } = item

        let temp_park_list = []
        item.parkList.forEach((item1) => {
            let { parkId: key_park, parkName: title } = item1
            let temp_project_list = []
            item1.proList.forEach((item2) => {
                let { projectId: key_pro, projectName: title } = item2
                temp_project_list.push({
                    title,
                    key: key + '-' + key_park + '-' + key_pro
                })
            })
            temp_park_list.push({
                title,
                key: key + '-' + key_park,
                children: temp_project_list
            })
        })
        all_list.push({
            title,
            key: key + '',
            children: temp_park_list
        })
    })
    // 获取到的项目结构保存
    return all_list

}


// 切换项目后对页面菜单进行重置
export const projectChangeMenu = (current) => {
    console.log(current);
    let first = 0
    let second = null
    // 全部设置为未选中状态并初始化赋值
    let current_skip_url = null
    let skip_url_list = []
    for (let i = 0; i < current.length; i++) {
      let item = current[i]
      item.active = 0
      // 默认选中第一个
      i == 0 ? (item.active = 1) : null
      if (Object.hasOwn(item, 'children')) {
        second = 0
        for (let j = 0; j < item.children.length; j++) {
          let item1 = item[j]
          item1.active = 0
          // 默认选中第一个
          j == 0 ? (item1.active = 1) : null
        }
      }
    }
    current_skip_url = current[0].menuUrl
    // 存在二级菜单则跳转二级菜单的url
    second ? (current_skip_url = current[0].children[0].menuUrl) : null
    skip_url_list.push(current_skip_url)
    // 当前跳转的路径链接
    // 存储选中菜单信息
    let current_menu_localstorage = {
      all_menu: current,
      current_menu: {
        // 当前菜单标记
        first,
        second
      },
      current_skip_url,
      skip_url_list
    }
   
    return current_menu_localstorage
  }