import { createRouter, createWebHashHistory } from 'vue-router'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'operationCenter',
      component: () => import('../views/OperationCenterContain.vue')
    },
    {
      path: '/reportAll',
      name: 'reportAll',
      component: () => import('../views/ReportAll.vue')
    },
    
  ]
})

export default router