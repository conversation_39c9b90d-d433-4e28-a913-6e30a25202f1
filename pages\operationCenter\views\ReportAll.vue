<template>
	<div class="page_all report_all">
		<a
			href="#"
			id="download"
			style="position: absolute; top: -1000px; right: -1000px"
		></a>
		<div class="main_contain">
			<div class="main_contain_scroll_item_list">
				<div class="mess_all">
					<div class="title">运营报告</div>
					<div class="report_all_form">
						<a-form>
							<a-form-item
								label="时间选择"
								name="time_select"
							>
								<a-date-picker
									:allowClear="false"
									style="width: 300px"
									v-model:value="time_month"
									picker="month"
									:disabled-date="
										(current) => {
											return current && current > dayjs().endOf('month');
										}
									"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/picker_month.png"
											alt="#"
										/>
									</template>
								</a-date-picker>
							</a-form-item>
						</a-form>
						<a-button
							@click="search"
							style="margin-left: 16px"
							type="primary"
						>
							查询
						</a-button>
						<a-button
							@click="resetInput"
							style="margin-left: 16px"
						>
							重置
						</a-button>
						<a-button
							class="export_erport"
							:disabled="dataSource.length > 0 ? false : true"
							@click="exportFn"
							type="primary"
						>
							下载详细报告
							<template #icon>
								<img
									style="width: 18px; height: 18px; position: relative; top: 2px; margin-right: 5px"
									src="/assets/icon/xiafa.png"
									alt="#"
								/>
							</template>
						</a-button>
					</div>
					<div class="tap_title">
						<img
							src="/assets/icon/report_icon1.png"
							alt=""
						/>
						<span>收益分析</span>
					</div>
					<div
						class="money_list"
						v-if="show_list.length"
					>
						<div
							v-for="(item, index) in show_list"
							:key="index"
							:class="[
								'money_list_item',
								item.type == 1 ? 'gf_income' : '',
								item.type == 2 ? 'cn_income' : '',
								item.type == 3 ? 'xq_income' : '',
								item.type == 4 ? 'zh_income' : ''
							]"
						>
							<div class="sub_title">{{ item.typeDesc }}</div>
							<div class="num_detail">
								<span class="total">
									<i class="total_num">{{ moneyNumberHandle(item.totalAmount) }}</i>
									<i class="unit">元</i>
								</span>
							</div>
							<img
								v-if="item.type == 1"
								class="icon"
								src="/assets/icon/report_gf.png"
								alt="#"
							/>
							<img
								v-if="item.type == 2"
								class="icon"
								src="/assets/icon/report_cn.png"
								alt="#"
							/>
							<img
								v-if="item.type == 3"
								class="icon"
								src="/assets/icon/report_xq.png"
								alt="#"
							/>
							<img
								v-if="item.type == 4"
								class="icon"
								src="/assets/icon/report_xl.png"
								alt="#"
							/>
							<div class="percent_up_down">
								<div>
									<span>环比</span>
									<span>
										{{ item.last_month_status == 2 ? '-' : null }}
										{{ item.last_month_percent }}
									</span>
									<span>%</span>
									<span
										v-if="item.last_month_status == 1 ? true : false"
										class="up_img"
									></span>
									<span
										v-if="item.last_month_status == 2 ? true : false"
										class="down_img"
									></span>
								</div>
								<div>
									<span>同比</span>
									<span>
										{{ item.last_year_status == 2 ? '-' : null }}{{ item.last_year_percent }}
									</span>
									<span>%</span>
									<span
										v-if="item.last_year_status == 1 ? true : false"
										class="up_img"
									></span>
									<span
										v-if="item.last_year_status == 2 ? true : false"
										class="down_img"
									></span>
								</div>
							</div>
						</div>
					</div>
					<div class="income_map_table">
						<div class="income_map">
							<div
								class="echart_pie_total"
								id="income_map"
							></div>
						</div>
						<div class="income_table">
							<a-table
								:pagination="false"
								:columns="columns"
								:data-source="dataSource"
								:bordered="false"
							></a-table>
						</div>
					</div>

					<div class="tap_title">
						<img
							src="/assets/icon/report_icon2.png"
							alt=""
						/>
						<span>异常分析</span>
						<!-- （历史累计：{{ unnrmal_total }}  <i>条</i> ）-->
					</div>
					<div class="money_list unnormal_analysis">
						<div
							v-for="(item, index) in warning_list"
							:key="index"
							:class="[
								'money_list_item',
								item.warnLevel == 1 ? 'gf_income' : '',
								item.warnLevel == 2 ? 'cn_income' : '',
								item.warnLevel == 3 ? 'xq_income' : '',
								item.warnLevel == 4 ? 'zh_income' : ''
							]"
						>
							<div class="sub_title">{{ item.warnLevelDesc }}</div>
							<div class="num_detail">
								<span class="total">
									<i class="total_num">{{ item.totalNum }}</i>
									<i class="unit">条</i>
								</span>
							</div>
							<img
								v-if="item.warnLevel == 1"
								class="icon"
								src="/assets/icon/report_jj.png"
								alt="#"
							/>
							<img
								v-if="item.warnLevel == 2"
								class="icon"
								src="/assets/icon/report_zy.png"
								alt="#"
							/>
							<img
								v-if="item.warnLevel == 3"
								class="icon"
								src="/assets/icon/report_cy.png"
								alt="#"
							/>
							<img
								v-if="item.warnLevel == 4"
								class="icon"
								src="/assets/icon/report_ts.png"
								alt="#"
							/>
							<div class="percent_up_down">
								<div>
									<span>环比</span>
									<span>{{ item.last_month_percent }}</span>
									<span>%</span>
									<span
										v-if="item.last_month_status == 1 ? true : false"
										class="up_img"
									></span>
									<span
										v-if="item.last_month_status == 2 ? true : false"
										class="down_img"
									></span>
								</div>
								<div>
									<span>同比</span>
									<span>{{ item.last_year_percent }}</span>
									<span>%</span>
									<span
										v-if="item.last_year_status == 1 ? true : false"
										class="up_img"
									></span>
									<span
										v-if="item.last_year_status == 2 ? true : false"
										class="down_img"
									></span>
								</div>
							</div>
						</div>
					</div>

					<div class="income_map_table">
						<div class="warn_map">
							<div
								class="echart_pie_total"
								id="warn_map"
							></div>
						</div>
						<div class="warn_table">
							<div
								class="status_list"
								v-for="(item, index) in unnormal_list"
								:key="index"
							>
								<div>
									<span>{{ item.warnStatusDesc }}({{ item.totalNum }}条)</span>
									<span>
										{{ item.percent }}
										<i>%</i>
									</span>
								</div>
								<div class="percent_status">
									<span :style="{ width: item.percent + '%' }"></span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, markRaw, onMounted } from 'vue';

	// import zhCN from 'ant-design-vue/es/locale/zh_CN'
	import dayjs from 'dayjs';
	// import 'dayjs/locale/zh-cn'
	// dayjs.locale('zh-cn')

	import { colorList } from '/assets/js/echartsConfig.js';
	import {
		monthBenefitAmountByType,
		sumWarnNum,
		countWarnNumByWarnStatus,
		countWarnNumByWarnLevel,
		exportComprehensive
	} from './api.js';
	import { moneyNumberHandle } from '/assets/js/commonTool.js';
	import * as echarts from 'echarts';
	import { message } from 'ant-design-vue';

	const project_id = ref();
	const project_name = ref();

	const time_month = ref(dayjs());

	const search = () => {
		// 收益分析
		getIncomeType();
		// 异常条数统计
		getUnormalSum();
		// 预警分析
		getWarnLevel();
		// 预警处理状态
		getWarnStatus();
	};

	const resetInput = () => {
		time_month.value = dayjs();
		search();
	};

	const export_loading = ref(false);
	const exportFn = () => {
		let params = {
			chooseMonthStart: time_month.value.startOf('month').format('YYYY-MM-DD'),
			projectId: project_id.value
		};
		export_loading.value = true;
		exportComprehensive(params)
			.then((data) => {
				////console.log(data)
				if (!data) {
					export_loading.value = false;
					return;
				}
				const url = window.URL.createObjectURL(new Blob([data], { type: 'application/zip' }));
				let link = document.getElementById('download');
				link.href = url;
				link.setAttribute(
					'download',
					`${time_month.value.startOf('month').format('YYYY-MM-DD')}-${project_name.value}.docx`
				);
				link.click();
				export_loading.value = false;
			})
			.catch((error) => {
				message.error(error.message);
				export_loading.value = false;
			});
	};

	// 各类型收益分析
	const show_list = ref([]);

	const getIncomeType = () => {
		let params = {
			chooseMonthStart: time_month.value.startOf('month').format('YYYY-MM-DD'),
			projectId: project_id.value
		};
		monthBenefitAmountByType(params)
			.then((data) => {
				////console.log(data)
				handleTypeData(data);
				// handleTypeData(temp_data)
				// 处理饼图的数据 当前月份
				handeleMapIncome(data.currentMonthList);
				// 处理table的额数据
				hadleTableData(data.currentMonthList, data.lastMonthList);
			})
			.catch((error) => {
				message.error(error.message);
			});
	};

	const countPercent = (current, old) => {
		let status = null;
		current - old > 0 ? (status = 1) : (status = 2);
		let numerator = Math.abs(current - old);
		let denominator = Math.abs(old);
		let percent = Math.trunc((numerator / denominator) * 10000) / 100;
		if (old == 0) {
			percent = '-';
			status = null;
		}

		return {
			percent,
			status
		};
	};

	const handleTypeData = (data) => {
		if (data.currentMonthList.length == 0) {
			// 当前数据为空
			// message.warning('当前数据为空！')
			show_list.value = [];
			return;
		}
		let temp_data = [];
		for (let index = 0; index < data.currentMonthList.length; index++) {
			const item1 = data.currentMonthList[index];
			if (item1.type == 0) {
				continue;
			}
			let temp_item = {
				type: item1.type,
				typeDesc: item1.typeDesc,
				totalAmount: Number(parseFloat(item1.totalAmount).toFixed(2))
			};

			temp_item.last_month_percent = '-';
			temp_item.last_month_status = null;
			data.lastMonthList.forEach((item2, index2) => {
				if (item1.type == item2.type) {
					// 类型相同进行数据处理
					let result2 = countPercent(item1.totalAmount, item2.totalAmount);
					console.log(result2, 'result2');
					if (result2.percent == Infinity || isNaN(result2.percent)) {
						result2.percent = '-';
						result2.status = null;
					}
					temp_item.last_month_percent = result2.percent;
					temp_item.last_month_status = result2.status;
					return;
				}
			});

			temp_item.last_year_percent = '-';
			temp_item.last_year_status = null;
			data.lastYearList.forEach((item3, index3) => {
				if (item1.type == item3.type) {
					// 类型相同进行数据处理
					let result3 = countPercent(item1.totalAmount, item3.totalAmount);
          if (result3.percent == Infinity || isNaN(result3.percent)) {
						result3.percent = '-';
						result3.status = null;
					}
					temp_item.last_year_percent = result3.percent;
					temp_item.last_year_status = result3.status;
					return;
				}
			});

			temp_data.push(temp_item);
		}
		//console.log(temp_data)
		show_list.value = temp_data;
	};

	// 收益饼图
	const total_sum = ref(0);
	// 收益饼图
	const map_pie_sum = ref();
	const handeleMapIncome = (data) => {
		//console.log(data)
		total_sum.value = 0;
		if (data.length == 0) {
			if (map_pie_sum.value) {
				map_pie_sum.value.dispose();
			}
			return;
		}
		let temp_series_data = [];
		for (let i = 0; i < data.length; i++) {
			//console.log(data[i])
			if (data[i].type != 0 && data[i].totalAmount >= 0) {
				// total_sum.value = data[i].totalAmount
				// //console.log(total_sum.value)
				// break
				let number_handle = Number(parseFloat(data[i].totalAmount).toFixed(4));
				total_sum.value = total_sum.value + number_handle;
			}
		}
		//console.log()
		for (let i = 0; i < data.length; i++) {
			if (data[i].type == 0) {
				continue;
			}
			if (data[i].totalAmount < 0) {
				continue;
			}
			let current_number = Number(parseFloat(data[i].totalAmount).toFixed(4));
			console.log(current_number);
			console.log(total_sum.value);

			let percnet = Number((current_number / Number(total_sum.value)).toFixed(4)) * 100 + '%';
			if (Number(total_sum.value) == 0) {
				percnet = 0;
			}
			if (data[i].typeDesc != 0) {
				temp_series_data.push({
					value: parseFloat(data[i].totalAmount).toFixed(2),
					name: data[i].typeDesc,
					label: {
						formatter: [`{a|${data[i].typeDesc}}`, `{b|${parseFloat(percnet).toFixed(2)}}%`].join(
							'\n'
						),
						rich: {
							a: {
								color: '#092649',
								fontSize: 14,
								lineHeight: 20,
								fontFamily: 'SourceHanSansCN-Regular',
								textAlign: 'left'
							},
							b: {
								color: '#092649',
								fontSize: 14,
								lineHeight: 20,
								fontFamily: 'SourceHanSansCN-Regular',
								textAlign: 'left'
							}
						}
					},
					labelLine: {
						show: true,
						smooth: false,
						// length: 15,
						// length2: 24,

						lineStyle: {
							type: 'dashed',
							color: colorList[i]
						}
					},
					itemStyle: {
						color: colorList[i]
					}
				});
			}
		}
		//console.log(temp_series_data)
		let option = {
			tooltip: {
				trigger: 'item'
			},
			legend: { show: false },
			grid: {
				left: 0,
				right: 0,
				top: 0,
				bottom: 50
			},
			series: [
				{
					name: '',
					type: 'pie',
					radius: ['40%', '55%'],
					avoidLabelOverlap: true,

					labelLine: {
						smooth: false,
						show: true
						// length:5,
						// length2: 24,
						// lineStyle: {
						//   color: 'red'
						// }
					},
					emphasis: {
						itemStyle: {
							shadowBlur: 10,
							shadowOffsetX: 0,
							shadowColor: 'rgba(0, 0, 0, 0.5)'
						}
					},
					data: temp_series_data
				}
			]
		};
		if (map_pie_sum.value) {
			map_pie_sum.value.dispose();
		}
		map_pie_sum.value = markRaw(echarts.init(document.getElementById('income_map')));
		map_pie_sum.value.setOption(option);
	};

	// ---------------------------收益table
	const columns = ref([]);
	const dataSource = ref([]);

	const hadleTableData = (current, last_Month) => {
		current = JSON.parse(JSON.stringify(current));
		last_Month = JSON.parse(JSON.stringify(last_Month));
		if (!current.length && !last_Month.length) {
			dataSource.value = [];
			return;
		}

		let columns_temp = [];
		let data = [{}, {}];
		columns_temp.push({
			title: '',
			key: 'name',
			dataIndex: 'name',
			align: 'center'
		});

		for (let index = 0; index < current.length; index++) {
			const item = current[index];
			if (item.type == 0) {
				continue;
			}
			columns_temp.push({
				title: item.typeDesc,
				key: 'key' + String(item.type),
				dataIndex: 'key' + String(item.type)
			});
			data[0]['key' + item.type] = moneyNumberHandle(
				Number(parseFloat(item.totalAmount).toFixed(2))
			);
			data[0]['name'] = '本月';
		}
		columns.value = columns_temp;

		for (let index = 0; index < last_Month.length; index++) {
			const item = last_Month[index];
			if (item.type == 0) {
				continue;
			}
			data[1]['key' + item.type] = moneyNumberHandle(
				Number(parseFloat(item.totalAmount).toFixed(2))
			);
			data[1]['name'] = '上月';
		}

		dataSource.value = data;
		console.log(dataSource.value, "dataSource.value")
		//////console.log(columns_temp)
	};

	// -------------------------------异常条数统计
	const unnrmal_total = ref('-');
	const getUnormalSum = () => {
		let params = project_id.value;
		sumWarnNum(params)
			.then((data) => {
				//////console.log(data)
				unnrmal_total.value = data;
			})
			.catch((error) => {
				message.error(error.message);
			});
	};

	// ------------------------------------ 获取分类报警的同环比 和饼图

	const warning_list = ref([]);

	const handleWarnLevelData = (data) => {
		if (data.currentMonthList.length == 0) {
			// 当前数据为空
			// message.warning('当前数据为空！')
		}
		let temp_data = [];
		for (let index = 0; index < data.currentMonthList.length; index++) {
			const item1 = data.currentMonthList[index];
			if (item1.warnLevel == 0) {
				continue;
			}
			let temp_item = {
				warnLevel: item1.warnLevel,
				warnLevelDesc: item1.warnLevelDesc,
				totalNum: item1.totalNum
			};

			temp_item.last_month_percent = '-';
			temp_item.last_month_status = null;
			data.lastMonthList.forEach((item2, index2) => {
				if (item1.warnLevel == item2.warnLevel) {
					// 类型相同进行数据处理
					let result2 = countPercent(item1.totalNum, item2.totalNum);
					temp_item.last_month_percent = result2.percent;
					temp_item.last_month_status = result2.status;
					return;
				}
			});

			temp_item.last_year_percent = '-';
			temp_item.last_year_status = null;
			data.lastYearList.forEach((item3, index3) => {
				if (item1.warnLevel == item3.warnLevel) {
					// 类型相同进行数据处理
					let result3 = countPercent(item1.totalNum, item3.totalNum);
					temp_item.last_year_percent = result3.percent;
					temp_item.last_year_status = result3.status;
					return;
				}
			});

			temp_data.push(temp_item);
		}
		////console.log('xxxxxxxxxxxxxxxxxyujing')
		////console.log(temp_data)
		warning_list.value = temp_data;
	};

	const getWarnLevel = () => {
		let params = {
			chooseMonthStart: time_month.value.startOf('month').format('YYYY-MM-DD'),
			projectId: project_id.value
		};
		countWarnNumByWarnLevel(params)
			.then((data) => {
				////console.log(data)
				handleWarnLevelData(data);
				// 处理饼图的数据 当前月份
				handeleMapWarn(data.currentMonthList);
				// // 处理table的额数据
				// hadleTableDataWarn(temp_data.currentMonthList, temp_data.lastMonthList)
			})
			.catch((error) => {
				message.error(error.message);
			});
	};

	// 预警饼图
	const map_pie_warn = ref();
	const total_warn = ref(0);

	const handeleMapWarn = (data) => {
		console.log(data);
		let temp_series_data = [];
		total_warn.value = 0;
		for (let i = 0; i < data.length; i++) {
			total_warn.value = total_warn.value + Number(data[i].totalNum);
			// if (data[i].warnLevel == 0) {
			//   break
			// }
		}
		for (let i = 0; i < data.length; i++) {
			if (data[i].warnLevel == 0) {
				continue;
			}
			let percnet = parseFloat(
				Number((data[i].totalNum / Number(total_warn.value)).toFixed(4)) * 100
			).toFixed(2);
			if (Number(total_warn.value) == 0) {
				percnet = 0;
			}
			////console.log(data[i].totalNum)
			// if (data[i].typeDesc != 0) {
			temp_series_data.push({
				value: parseFloat(data[i].totalNum).toFixed(2),
				name: data[i].warnLevelDesc,
				label: {
					formatter: [
						`{a|${data[i].warnLevelDesc}}`,
						`{b|${parseFloat(percnet).toFixed(2)}%}`
					].join('\n'),
					rich: {
						a: {
							color: '#092649',
							fontSize: 14,
							lineHeight: 20,
							fontFamily: 'SourceHanSansCN-Regular',
							textAlign: 'left'
						},
						b: {
							color: '#092649',
							fontSize: 14,
							lineHeight: 20,
							fontFamily: 'SourceHanSansCN-Regular',
							textAlign: 'left'
						}
					}
				},
				labelLine: {
					show: true,
					smooth: false,
					// length: 15,
					// length2: 24,

					lineStyle: {
						type: 'dashed',
						color: colorList[i]
					}
				},
				itemStyle: {
					color: colorList[i]
				}
			});
			// }
		}
		let option = {
			tooltip: {
				trigger: 'item'
			},
			legend: { show: false },
			grid: {
				left: 0,
				right: 0,
				top: 0,
				bottom: 50
			},
			series: [
				{
					name: '',
					type: 'pie',
					radius: ['40%', '55%'],
					avoidLabelOverlap: true,

					labelLine: {
						smooth: false,
						show: true
						// length:5,
						// length2: 24,
						// lineStyle: {
						//   color: 'red'
						// }
					},
					emphasis: {
						itemStyle: {
							shadowBlur: 10,
							shadowOffsetX: 0,
							shadowColor: 'rgba(0, 0, 0, 0.5)'
						}
					},
					data: temp_series_data
				}
			]
		};
		if (map_pie_warn.value) {
			map_pie_warn.value.dispose();
		}
		////console.log(option)
		map_pie_warn.value = markRaw(echarts.init(document.getElementById('warn_map')));
		map_pie_warn.value.setOption(option);
	};
	// -----------------------------------预警处理百分比
	const total_unnormal = ref(0);
	const unnormal_list = ref([]);

	const handlewarnStatusData = (current) => {
		if (current.length == 0) {
			message.warning('数据为空');
			return;
		}
		let data = current.currentMonthList;
		if (total_unnormal.value == '-') {
			total_unnormal.value = 0;
		}
		for (let i = 0; i < data.length; i++) {
			if (data[i].warnStatus == 0) {
				continue;
			}
			total_unnormal.value = total_unnormal.value + Number(data[i].totalNum);
		}
		//console.log()
		unnormal_list.value = [];

		for (let i = 0; i < data.length; i++) {
			if (data[i].warnStatus == 0) {
				continue;
			}
			let percent = parseFloat((100 * data[i].totalNum) / total_unnormal.value).toFixed(2);
			//console.log(percent)
			let totalNum = data[i].totalNum;
			if (total_unnormal.value == 0) {
				percent = '-';
				totalNum = '-';
			}
			unnormal_list.value.push({
				percent: percent,
				totalNum: totalNum,
				warnStatusDesc: data[i].warnStatusDesc
			});
		}
		total_unnormal.value == 0 ? (total_unnormal.value = '-') : null;
	};

	const getWarnStatus = () => {
		let params = {
			chooseMonthStart: time_month.value.startOf('month').format('YYYY-MM-DD'),
			projectId: project_id.value
		};
		countWarnNumByWarnStatus(params)
			.then((data) => {
				let temp_data = {
					currentMonthList: [
						{
							totalNum: 400,
							warnStatus: 0,
							warnStatusDesc: '总计'
						},
						{
							totalNum: 100,
							warnStatus: 1,
							warnStatusDesc: '已处理'
						},
						{
							totalNum: 100,
							warnStatus: 2,
							warnStatusDesc: '未处理'
						}
					]
				};
				handlewarnStatusData(data);
			})
			.catch((error) => {
				message.error(error.message);
			});
	};

	onMounted(() => {
		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			project_id.value = project_mess.project_id;
			project_name.value = project_mess.project_title;
		} else {
			message.error('未选择项目，请重新选择！');
		}

		// 收益分析
		getIncomeType();
		// 异常条数统计
		getUnormalSum();
		// 预警分析
		getWarnLevel();
		// 预警处理状态
		getWarnStatus();
	});
</script>
<style lang="less" scoped>
	.mess_all {
		background: #ffffff;
		border-radius: 8px;
		padding: 32px;
		.title {
			font-family: SourceHanSansCN-Bold;
			font-weight: bold;
			font-size: 18px;
			color: #092649;
			height: 18px;
			line-height: 18px;
			margin-bottom: 16px;
		}
	}
	.tap_title {
		box-sizing: border-box;
		padding: 21px;
		height: 64px;
		background: rgba(242, 248, 255, 1);
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		margin-bottom: 32px;
		img {
			height: 22px;
			width: 22px;
			margin-right: 13px;
		}
		span {
			font-family: SourceHanSansCN-Bold;
			font-weight: bold;
			font-size: 16px;
			height: 16px;
			line-height: 16px;
			color: #092649;
		}
		i {
			font-style: normal;
			font-size: 12px;
			height: 12px;
			line-height: 12px;
		}
	}
	.money_list {
		width: 100%;
		height: 140px;
		box-sizing: border-box;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		.money_list_item {
			width: calc(24% - 24px);
			box-sizing: border-box;
			margin-right: 30px;
			padding-left: 32px;
			border-radius: 8px;
			// min-height: 126px;
			display: flex;
			flex-direction: column;
			justify-content: center;
			position: relative;

			&:last-child {
				margin-right: 0 !important;
			}
			.sub_title {
				font-family: SourceHanSansCN-Regular;
				font-weight: 400;
				font-size: 16px;
				height: 16px;
				line-height: 16px;
				color: #9ca2af;
				margin-bottom: 16px;
			}
			.num_detail {
				margin-bottom: 20px;
			}
			.total {
				display: block;
				height: 32px;
				line-height: 32px;
				.total_num {
					font-family: D-DIN-PRO;
					font-weight: bold;
					font-style: normal;
					font-size: 32px;
					color: #092649;
				}
				.unit {
					font-family: SourceHanSansCN-Regular;
					font-weight: 400;
					font-size: 14px;
					color: #9ca2af;
				}
				.contrast {
					font-family: SourceHanSansCN-Regular;
					font-weight: 400;
					font-size: 14px;
					color: #9ca2af;
					margin-right: 6px;
				}
			}

			img {
				height: 72px;
				width: 72px;
				position: absolute;
				top: 50%;
				right: 4%;
				transform: translate(0%, -50%);
			}
		}
		.total_income {
			background: linear-gradient(90deg, rgba(17, 129, 253, 0.12), rgba(17, 129, 253, 0.02));
		}
		.gf_income {
			background: linear-gradient(90deg, rgba(44, 199, 244, 0.12), rgba(44, 199, 244, 0.02));
		}
		.cn_income {
			background: linear-gradient(90deg, rgba(121, 133, 252, 0.12), rgba(121, 133, 252, 0.02));
		}
		.xq_income {
			background: linear-gradient(90deg, rgba(17, 129, 253, 0.12), rgba(17, 129, 253, 0.02));
		}
		.zh_income {
			background: linear-gradient(90deg, rgba(3, 227, 161, 0.12), rgba(3, 227, 161, 0.02));
		}
	}
	.money_list.unnormal_analysis {
		justify-content: space-between;
	}
	.percent_up_down {
		height: 18px;
		width: 100%;
		div {
			width: 50% !important;
			float: left;
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;
		}
		span {
			font-family: SourceHanSansCN-Regular;
			font-weight: 400;
			font-size: 14px;
			color: #9ca2af;
			line-height: 14px;
			&:nth-child(1) {
				margin-right: 9px;
			}
			&:nth-child(2) {
				font-family: D-DIN-PRO-Bold;
				font-weight: bold;
				font-size: 18px;
				color: #092649;
				line-height: 18px;
			}
			&:nth-child(3) {
				margin-right: 9px;
			}
		}
	}
	.up_img {
		width: 10px;
		height: 14px;
		display: inline-block;
		background: url('/assets/icon/up_percent.png');
		background-repeat: no-repeat;
		background-size: 100%;
	}
	.down_img {
		width: 10px;
		height: 14px;
		display: inline-block;
		background: url('/assets/icon/down_percent.png');
		background-repeat: no-repeat;
		background-size: 100%;
	}

	.income_map_table {
		height: 325px;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		.income_map {
			height: 100%;
			width: 37%;

			.echart_pie_total {
				height: 100%;
				width: 100%;
			}
		}
		.income_table {
			margin-right: 128px;
			width: calc(63% - 160px);
		}
		.warn_map {
			height: 100%;
			width: 37%;
			.echart_pie_total {
				height: 100%;
				width: 100%;
			}
		}
		.warn_table {
			width: calc(63% - 136px);
			margin-right: 100px;
			box-sizing: border-box;
			padding-left: 13%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			height: 60%;
		}
	}
	.status_list {
		height: 39px;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		div {
			&:nth-child(1) {
				height: 16px;
				line-height: 16px;
				font-family: SourceHanSansCN-Regular;
				font-weight: 400;
				font-size: 14px;
				color: #9ca2af;
				// line-height: 20px;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: flex-end;
			}
			&:nth-child(2) {
				height: 16px;
			}
			span {
				&:nth-child(2) {
					font-family: D-DIN-PRO;
					font-weight: 600;
					font-size: 20px;
					color: #092649;
					// line-height: 20px;
					i {
						color: rgba(156, 162, 175, 1);
						font-size: 14px;
						font-style: normal;
					}
				}
			}
		}
		&:nth-child(1) {
			margin-bottom: 75px;
		}
	}
	.percent_status {
		height: 10px;
		background: #edf4fc;

		span {
			height: 100%;
			float: left;
			display: block;
			background: rgba(17, 129, 253, 1);
		}
	}
</style>
<style lang="less">
	.report_all_form {
		height: 36px;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: top;
		margin-bottom: 32px;
		position: relative;
		.ant-form-item .ant-form-item-label > label {
			min-width: 80px !important;
		}
		.export_erport {
			position: absolute;
			right: 0;
			top: 0;
		}
	}
</style>
