import http from '/tool/http.js'


// 根据收益类型查询收益金额(当前月、上一月、去年当前月)
export const monthBenefitAmountByType = (params) => {
    let url = "/api/comprehensive/monthBenefitAmountByType";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            //console.log(data)
            if (data.code == 200) {
                resolve(data.data)
            } else {
                reject({
                    message: data.msg
                })
            }
        }).catch((errorInfo) => {
            reject({
                message: errorInfo
            })
        });
    })
}


// 统计异常累计总数
export const sumWarnNum = (params) => {
    let url = `/api/comprehensive/sumWarnNum/${params}`;
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            // //console.log(data)
            if (data.code == 200) {
                resolve(data.data)
            } else {
                reject({
                    message: data.msg
                })
            }
        }).catch((errorInfo) => {
            reject({
                message: errorInfo
            })
        });
    })
}



// 根据处理状态统计预警数(当前月、上一月、去年当前月)
export const countWarnNumByWarnStatus = (params) => {
    let url = "/api/comprehensive/countWarnNumByWarnStatus";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            //console.log(data)
            if (data.code == 200) {
                resolve(data.data)
            } else {
                reject({
                    message: data.msg
                })
            }
        }).catch((errorInfo) => {
            reject({
                message: errorInfo
            })
        });
    })
}


// 根据预警类型统计预警数(当前月、上一月、去年当前月)
export const countWarnNumByWarnLevel = (params) => {
    let url = "/api/comprehensive/countWarnNumByWarnLevel";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            //console.log(data)
            if (data.code == 200) {
                resolve(data.data)
            } else {
                reject({
                    message: data.msg
                })
            }
        }).catch((errorInfo) => {
            reject({
                message: errorInfo
            })
        });
    })
}

// 导出综合报告
export const exportComprehensive = (params) => {
    let url = `/api/comprehensive/export`;
    return new Promise((resolve, reject) => {
        http.post(url, params, {
            headers: {
                "content-type": "application/json; charset=utf-8"
            },
            responseType: "blob",
        }).then((data) => {
            resolve(data)
        }).catch((errorInfo) => {
            reject({
                message: errorInfo
            })
        });
    })
}
