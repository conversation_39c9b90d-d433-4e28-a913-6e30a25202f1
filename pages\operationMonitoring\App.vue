<template>
  <a-config-provider :theme="themeSelf" :locale="zhCN">
    <Header></Header>
    <RouterView />
  </a-config-provider>
</template>
<script setup>
import { RouterView } from "vue-router";
import { themeSelf } from "/assets/js/theme.js";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
import Header from "/components/Header.vue"

import { getCurrentInstance } from 'vue';
	import { onMounted, onBeforeUnmount } from 'vue';

	const { proxy } = getCurrentInstance();
	const handleResize = () => {
		proxy.$forceUpdate(); // 强制重新渲染组件
		// 另外一种实现全局刷新的方法
		window.location.reload();
	};

	onMounted(() => {
		window.addEventListener('resize', handleResize); // 添加监听
	});

	onBeforeUnmount(() => {
		window.removeEventListener('resize', handleResize); // 移除监听
	});




</script>
<style scoped></style>
