import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPersist from 'pinia-plugin-persist'
import App from './App.vue'
import router from './router'
// 全局样式的设置
import '/assets/css/common.less'
// 全局antd修改样式
import '/assets/css/antdCommonChange.less'
// 全局字体样式的引入
import '/assets/font/font.less'
const pinia = createPinia()
pinia.use(piniaPersist)
const app = createApp(App)
app.use(pinia)
app.use(router)
app.mount('#app')
