import { createRouter, createWebHashHistory } from 'vue-router';

const router = createRouter({
	history: createWebHashHistory(import.meta.env.BASE_URL),
	routes: [
		{
			path: '/',
			name: 'deviceControl',
			component: () => import('../views/DeviceControl.vue')
		},
		{
			path: '/deviceControl',
			name: 'deviceControl',
			component: () => import('../views/DeviceControl.vue')
		},
		{
			path: '/unNormalControl',
			name: 'unNormalControl',
			component: () => import('../views/unNormalControl.vue')
		},
		{
			path: '/ruleSetting',
			name: 'ruleSetting',
			component: () => import('../views/ruleSetting.vue')
		},
		{
			path: '/analysis',
			name: 'analysis',
			redirect: '/analysis/analysis_all',
			component: () => import('../views/AnalysisContain.vue'),
			children: [
				{
					path: 'analysis_all',
					name: 'analysis_all',
					component: () => import('../views/AnalysisAll.vue')
				},
				{
					path: 'analysis_gf',
					name: 'analysis_gf',
					component: () => import('../views/AnalysisGF.vue')
				},
				{
					path: 'analysis_cn',
					name: 'analysis_cn',
					component: () => import('../views/AnalysisCN.vue')
				},
				{
					path: 'analysis_cdz',
					name: 'analysis_cdz',
					component: () => import('../views/AnalysisCDZ.vue')
				},
				{
					path: 'analysis_yn',
					name: 'analysis_yn',
					component: () => import('../views/AnalysisYN.vue')
				},
				{
					path: 'analysis_yc',
					name: 'analysis_yc',
					component: () => import('../views/AnalysisYC.vue')
				}
			]
		}
	]
});

export default router;
