<template>
	<div class="main_contain main_contain_thirdMenu">
		<a
			href="#"
			id="download"
			style="position: absolute; top: -1000px; right: -1000px"
		></a>
		<div class="main_contain_scroll">
			<div class="map_item">
				<div class="map_title">微网功率曲线</div>
				<div class="params_list">
					<div class="params_list_left">
						<a-form
							:model="form_state_echarts_line"
							name="horizontal_login"
							layout="inline"
							autocomplete="off"
						>
							<a-form-item
								label="时间选择"
								name="time"
							>
								<a-range-picker
									:getPopupContainer="(e) => e.parentNode"
									:status="time_status_echarts_line ? 'error' : ''"
									:allowClear="false"
									v-model:value="form_state_echarts_line.time"
									style="width: 300px"
									:disabledDate="
										(current) => {
											return current && current > dayjs().endOf('day');
										}
									"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/clock.png"
											alt="#"
										/>
									</template>
								</a-range-picker>
							</a-form-item>

							<!-- <a-form-item label="添加对比" name="compareStartTime">
                <a-date-picker
                  :allowClear="false"
                  :disabled-date="
                    (current) => {
                      return current && current > dayjs().endOf('day')
                    }
                  "
                  v-model:value="form_state_echarts_line.compareStartTime"
                />
              </a-form-item> -->

							<a-form-item>
								<a-button
									type="primary"
									@click="getEchartsLine"
									html-type="submit"
								>
									查询
								</a-button>
							</a-form-item>
							<a-form-item>
								<a-button @click="resetEchartsLineInput">重置</a-button>
							</a-form-item>
							<a-form-item style="width: 90px">
								<a-select
									v-model:value="statisType"
									@change="changeTimeInterval"
								>
									<a-select-option
										v-for="option in time_interval_options"
										:key="option.value"
										:value="option.value"
									>
										{{ option.label }}
									</a-select-option>
								</a-select>
							</a-form-item>
						</a-form>
					</div>
					<div class="params_list_right">
						<a-button
							type="primary"
							@click="skipUnnormal"
							style="margin-right: 16px; display: flex"
						>
							<div class="icon_button">
								<img
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/unnormal_setting.png"
									alt=""
								/>
								<span>异常规则配置</span>
							</div>
						</a-button>
						<a-button
							:loading="export_loading"
							@click="exportFn"
							:disabled="!hasData"
							@mouseenter="hover1 = true"
							@mouseleave="hover1 = false"
							style="margin-right: 16px; display: flex"
						>
							<div class="icon_button">
								<img
									v-if="hover1 && hasData"
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_hover.png"
									alt=""
								/>
								<img
									v-else
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_dark.png"
									alt=""
								/>
								<span>导出</span>
							</div>
						</a-button>
						<a-button
							@click="selectFn"
							style="display: flex"
							id="selectBt"
							@mouseenter="hover2 = true"
							@mouseleave="hover2 = false"
						>
							<div class="icon_button">
								<img
									style="
										display: inline-block;
										height: 18px;
										width: 18px;
										position: relative;
										top: 1px;
									"
									v-if="!hover2"
									src="/assets/icon/shaixuan.png"
									alt=""
								/>
								<img
									style="
										display: inline-block;
										height: 18px;
										width: 18px;
										position: relative;
										top: 1px;
									"
									v-else
									src="/assets/icon/shaixuan_hover.png"
									alt=""
								/>
								<span>筛选</span>
							</div>
						</a-button>
					</div>
				</div>
				<div class="map_line_contain">
					<!-- <span class="unit_mess">kW</span> -->
					<div
						v-if="hasData"
						class="map_line"
						id="echarts_line_id"
					></div>
					<a-empty
						v-if="!hasData"
						style="margin: 0 auto; position: relative; top: 30%"
					/>
				</div>
			</div>
			<a-modal
				v-model:open="selectModel"
				title="拓展曲线"
				:getContainer="false"
				:mask="false"
				@cancel="handleCancel"
				@ok="handleOk"
				:style="{ top: `${buttonTop}px` }"
				style="position: absolute; width: 230px; right: 30px; height: 258px; padding: 0"
			>
				<!-- @change="handleChange" -->
				<a-checkbox-group
					v-model:value="checkedValues"
					style="display: grid; margin-left: 10px; font-size: 14px; margin-bottom: 20px"
				>
					<a-checkbox
						value="1"
						:disabled="dis1"
						style="margin-bottom: 10px; margin-top: 10px"
					>
						计费需量
					</a-checkbox>
					<a-checkbox
						value="2"
						:disabled="dis2"
						v-if="isElectricityShow"
						style="margin-bottom: 10px"
					>
						分时电价
					</a-checkbox>
					<a-checkbox
						value="3"
						:disabled="dis2"
						v-if="isElectricityShow"
						style="margin-bottom: 10px"
					>
						现货电价
					</a-checkbox>
					<a-checkbox
						value="4"
						v-if="isSpecialPro"
						style="margin-bottom: 10px"
					>
						关口+储能功率
					</a-checkbox>
					<a-checkbox
						value="5"
						v-if="isSpecialPro"
						style="margin-bottom: 10px"
					>
						月度最大需量-理论
					</a-checkbox>
					<a-checkbox
						value="6"
						v-if="isSpecialPro"
						style="margin-bottom: 10px"
					>
						月度最大需量-实缴
					</a-checkbox>
				</a-checkbox-group>
			</a-modal>
		</div>
	</div>
</template>

<script setup>
	import { ref, reactive, onMounted, markRaw, nextTick } from 'vue';
	import * as echarts from 'echarts';
	import { getStrategyByProjectId } from '/pages/configurationManagement/views/api.js';
	import {
		handleTimeParams,
		handleLineMapOptionSingal,
		init_x_y,
		handleEchartsLineBg,
		timeRange,
		handleMax
	} from '/assets/js/commonTool.js';
	// import zhCN from 'ant-design-vue/es/locale/zh_CN'
	import { statMinuteActivePowerHttp, exportMinuteActivePowerHttp } from './api.js';
	import dayjs from 'dayjs';
	import { message } from 'ant-design-vue';
	import { eventBus } from '/assets/js/eventBus'; // 导入事件总线实例
	const disabledDate = (current) => {
		// Can not select days before today and today
		return current && current < dayjs().endOf('day');
	};
	const exportIcon = ref('/assets/icon/export_icon_dark.png'); // 默认图片
	const changeExportIcon = (type) => {
		if (type === 'hover' && hasData.value) {
			exportIcon.value = '/assets/icon/export_icon_hover.png'; // 悬停时的图片
		} else {
			exportIcon.value = '/assets/icon/export_icon_dark.png'; // 默认图片
		}
	};
	// ***********************************************************************************************************
	//                                                   全局参数配置                                            //
	// ***********************************************************************************************************

	const buttonTop = ref(200);
	const checkedValues = ref([]);
	const hover2 = ref(false);
	const hover1 = ref(false);

	const selectModel = ref(false);
	const dis1 = ref(false);
	const dis2 = ref(false);
	const selectFn = () => {
		selectModel.value = true;
		previousCheckedValues.value = checkedValues.value;
		if (checkedValues.value.length == 0) {
			dis1.value = false;
			dis2.value = false;
		}
	};
	const previousCheckedValues = ref([]);
	const isSpecialPro = ref(false);
	const isElectricityShow = ref(true);
	const hasSpotTrading = ref(false);

	const handleOk = () => {
		selectModel.value = false;
		getEchartsLine();
	};

	const handleCancel = () => {
		selectModel.value = false;
		checkedValues.value = previousCheckedValues.value;
	};

	const handleChange = (value) => {
		console.log('选中的值:', value);
		if (value.includes('1')) {
			dis2.value = true;
		} else if (value.length == 0) {
			dis1.value = false;
			dis2.value = false;
		} else {
			dis1.value = true;
		}
	};

	const project_id = ref(null);
	const project_title = ref(null);
	const project_collectionInterval = ref(null);
	const skipUnnormal = () => {
		eventBus.emit('menu-change', [1, 2, '/pages/operationMonitoring/#/ruleSetting']); // 触发事件
	};

	// 时间范围限制30天
	const disabledDate30 = (current) => {
		form_state_echarts_line.time;
		if (!form_state_echarts_line.time || form_state_echarts_line.time.length === 0) {
			return false;
		}
		const tooLate =
			form_state_echarts_line.time[0] && current.diff(form_state_echarts_line.time[0], 'days') > 30;
		const tooEarly =
			form_state_echarts_line.time[1] && form_state_echarts_line.time[1].diff(current, 'days') > 30;
		return tooEarly || tooLate;
	};

	// ***********************************************************************************************************
	//                                                   全局参数配置                                            //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                   微网功率曲线                                            //
	// ***********************************************************************************************************

	const hasData = ref(true);

	// 时间间隔选择，默认1分钟
	const statisType = ref(1);

	// 时间间隔选项
	const time_interval_options = ref([
		{ value: 1, label: '1分钟' },
		{ value: 15, label: '15分钟' }
	]);

	// 时间参数
	const form_state_echarts_line = reactive({
		time: [dayjs().startOf('day'), dayjs()],
		compareStartTime: null
	});

	// 重置查询条件
	const resetEchartsLineInput = () => {
		checkedValues.value = [];
		Object.assign(form_state_echarts_line, {
			time: [dayjs().startOf('day'), dayjs()],
			compareStartTime: null
		});
		if (time_interval_options.value.length > 1) {
			statisType.value = 1;
		}
		searchAll();
	};

	// 时间插件
	const time_status_echarts_line = ref(false);

	// 绘图的基本数据
	const xaxis_data_echarts_line = ref([]);
	const xaxis_data_echarts_line_contrast = ref([]);
	const yaxis_data_echarts_line = ref([]);
	const yaxis_data_echarts_line_contrast = ref([]);
	const unit_echarts_line = ref('kW');
	const my_chart_line = ref();

	// 根据条件查询曲线数据
	const getEchartsLine = () => {
		let time_judge = timeRange(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'date'
		);

		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}
		let time_result = handleTimeParams(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'day'
		);
		// if (form_state_echarts_line.time[1].diff(form_state_echarts_line.time[0], 'days') > 30) {
		//   message.error('最长可选的时间范围为30天！，请修改后再查询')
		//   return
		// }
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;
		console.log(endTime);
		let params = {
			dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: project_id.value,
			startTime
		};
		form_state_echarts_line.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_line.compareStartTime)
					.startOf('day')
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;

		params.extTypes = checkedValues.value.map((item) => Number(item));
		params.statisType = statisType.value;

		statMinuteActivePowerHttp(params)
			.then((data) => {
				if (data && data.statisticList.length > 0) {
					handleDataEchartsLine(data.statisticList, data.timeList, data.extStatisticList);
				} else {
					hasData.value = false;
				}
			})
			.catch((error) => {
				console.log(error);
				message.error(error.message);
			});
		// 功率曲线
		// Promise.all([
		//   statMinuteActivePowerHttp(params), // 功率曲线
		//   getStandardValueByElement() // 参考线
		// ])
		//   .then(([data1, data2]) => {
		//     handleDataEchartsLine(data1.statisticList)
		//     //console.log(data2)
		//   })
		//   .catch((error) => {
		//     message.error(error.message)
		//   })
	};

	const handleDataEchartsLine = (data, timeList, extStatisticList) => {
		// 正常值
		if (data[0].elementList.length > 0) {
			let result = {};
			if (data.length > 1) {
				let result1 = init_x_y(data[0].elementList);
				console.log(data[0].elementList, 'data[0].elementList');
				console.log(result1.y_axis, 'result1.y_axis');
				result1.y_axis[0].name = '计费需量';
				let result2 = init_x_y(data[1].elementList);
				result.x_axis = result1.x_axis;
				result.y_axis = result2.y_axis.concat(result1.y_axis);
			} else {
				result = init_x_y(data[0].elementList);
				console.log(data[0].elementList, 'data[0].elementList');
			}

			if (extStatisticList.length > 0) {
				for (let i = 0; i < extStatisticList.length; i++) {
					extStatisticList[i].elementCodeDesc = extStatisticList[i].extName;
				}
				let res = init_x_y(extStatisticList);
				result.y_axis = result.y_axis.concat(res.y_axis);
			}

			console.log(result, 'result');

			xaxis_data_echarts_line.value = result.x_axis;
			yaxis_data_echarts_line.value = result.y_axis;
		}
		// 对比值
		if (data[0].compareElementList.length > 0) {
			let result_contrast = init_x_y(data[0].compareElementList);
			xaxis_data_echarts_line_contrast.value = result_contrast.x_axis;
			yaxis_data_echarts_line_contrast.value = result_contrast.y_axis;
		}
		let markArea = handleEchartsLineBg(timeList, xaxis_data_echarts_line.value);

		let option_result = handleLineMapOptionSingal(
			xaxis_data_echarts_line.value,
			yaxis_data_echarts_line.value,
			unit_echarts_line.value
		);

		option_result.series[0].markArea = markArea;

		const colorMap = {
			负荷: '#03E3A1',
			储能: '#7C8DFF',
			关口表: '#1181FD',
			光伏: '#2DCDFC',
			计费需量: '#FEB56E',
			分时电价: '#D59DFD',
			现货电价: '#FB6BEF',
			'关口+储能功率': '#23d9de',
			'月度最大需量-理论': '#775dfc',
			'月度最大需量-实缴': '#92da6d'
		};

		function adjustMarkPointPosition(params, dom, rect, size) {
			// 获取标记点的数据值
			const value = params.data.value;
			// 获取 x 轴和 y 轴的坐标转换函数
			const xAxis = params.seriesIndex >= 0 ? params.series.getAxis('x') : null;
			const yAxis = params.seriesIndex >= 0 ? params.series.getAxis('y') : null;
			if (!xAxis || !yAxis) {
				return [0, 0];
			}
			// 计算标记点在图表中的理论位置
			const point = [xAxis.dataToCoord(value[0]), yAxis.dataToCoord(value[1])];

			// 标记点的大小
			let symbolSize = params.data.symbolSize;
			if (typeof symbolSize === 'number') {
				symbolSize = [symbolSize, symbolSize];
			}

			// 图表的边界信息
			const xMin = rect.x;
			const xMax = rect.x + rect.width;
			const yMin = rect.y;
			const yMax = rect.y + rect.height;

			// 标记点顶部显示时的理论位置
			const topPosition = [point[0], point[1] - symbolSize[1] / 2];
			// 检查顶部空间是否足够
			if (topPosition[1] >= yMin) {
				return topPosition;
			} else {
				// 顶部空间不足，显示在右侧
				return [point[0] + symbolSize[0] / 2, point[1]];
			}
		}

		// delete option_result.yAxis[0].max
		let num = 0;
		option_result.series.forEach((seriesItem, i) => {
			// console.log(seriesItem, 'seriesItem');
			if (colorMap[seriesItem.name]) {
				option_result.series[i].itemStyle.color = colorMap[seriesItem.name];
			}
			if (seriesItem.name == '计费需量') {
				option_result.series[i].markPoint = {
					data: [
						{
							type: 'max',
							name: 'Max',
							symbolOffset: [0, -6],
							symbolSize: 20,
							label: {
								position: 'inside',
								// offset: [-12, 0],
								formatter: function (params) {
									// // 获取图表实例
									// var chart = my_chart_line.value;
									// console.log(my_chart_line.value, 'my_chart_line.value1111');
									// // 获取标记点的位置
									// var pointPosition = chart.convertToPixel('grid', [
									// 	params.data.coord[0],
									// 	params.data.coord[1]
									// ]);
									// console.log(pointPosition, 'pointPosition111');
									// var grid = chart.getModel().getComponent('grid').coordinateSystem;
									// var gridTop = grid._rect.y;
									// console.log(grid._rect.y, 'gridTop111');
									// var labelHeight = 20; // 假设标签高度为 20px
									// // 判断标记点的标签是否会超出图表顶部
									// if (pointPosition[1] - labelHeight < gridTop) {
									// 	// 如果超出顶部，将标签位置调整为底部
									// 	console.log('顶部空间不够');
									// 	isChaoguo.value = true;
									// 	// option_result.series[4].markPoint.data[0].label.position = 'bottom';
									// 	// my_chart_line.value.setOption(option_result);
									// 	console.log(option_result.series, 'option_result.series2222');
									// } else {
									// 	isChaoguo.value = false;
									// }
									// return params.name + '\n' + params.value.toFixed(2) + 'kW';
									return '';
								},
								fontSize: 14,
								color: '#092649',
								fontWeight: 'bold'
							}
						}
					]
				};
				num = Math.ceil(Math.ceil(option_result.yAxis[0].num) / 5);
				// option_result.yAxis[0].max = handleMax(num * 8);
			}
			if (seriesItem.name.includes('电价')) {
				// seriesItem.lineStyle.type = 'dashed'
				if (option_result.yAxis.length == 1) {
					option_result.yAxis.push({
						type: 'value',
						name: '元/kWh',
						alignTicks: true,
						axisLabel: {
							formatter: function (value) {
								return value % 1 === 0 ? value : value.toFixed(1);
							}
						}
					});
					// option_result.yAxis[1].max = Number(option_result.yAxis[1].max).toFixed(1)
				}
				option_result.series[i].yAxisIndex = 1;
				option_result.series[i].step = 'start';
				option_result.series[i].lineStyle = {
					type: 'dashed' // 设置为虚线
				};
				console.log(option_result.series[i], 'option_result.series[i]');
			}
		});

		option_result.tooltip = {
			trigger: 'axis',
			formatter: function (params) {
				let html = '';
				params.forEach((v) => {
					let value = v.value;
					let unit = '';
					if (v.seriesName.includes('电价')) {
						unit = '元/kWh';
					} else {
						unit = 'kW';
					}
					if (value != 0 && !value) {
						value = '-';
					} else {
						value = Number(parseFloat(v.value).toFixed(2));
					}
					html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${v.color};"></span>
                    ${v.seriesName} : ${v.name} <span style="color:${v.color};font-weight:600;font-size: 14px"> ${value} </span>   ${unit} 
                    `;
				});
				return html;
			},
			extraCssText:
				'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;'
		};

		if (my_chart_line.value) {
			my_chart_line.value.dispose();
		}
		if (document.getElementById('echarts_line_id')) {
			my_chart_line.value = markRaw(echarts.init(document.getElementById('echarts_line_id')));
			my_chart_line.value.setOption(option_result);
		}

		// my_chart_line.value.on('finished', function () {
		// 	if (isChaoguo.value) {
		// 		console.log('超过');
		// 		option_result.series[4].markPoint.data[0].label.position = 'bottom';
		// 		// my_chart_line.value.setOption(option_result);
		// 	}
		// });

		// my_chart_line.value.on('legendselectchanged', function (params) {
		// 	// params.name 是被点击的图例名称
		// 	// params.selected 是所有图例的选中状态（布尔值对象）

		// 	// 根据选中状态执行操作
		// 	if (!params.selected[params.name]) {
		// 		console.log('图例 "' + params.name + '" 被取消选中');
		// 		// 在这里可以执行选中后的操作，例如更新图表或触发其他逻辑
		// 		if (params.name == '计费需量') {
		// 			my_chart_line.value.setOption({
		// 				yAxis: {
		// 					max: null // 删除最大值限制
		// 				}
		// 			});
		// 		}
		// 	} else {
		// 		console.log('图例 "' + params.name + '" 被选中');
		// 		// 在这里可以执行取消选中后的操作
		// 		if (params.name == '计费需量') {
		// 			my_chart_line.value.setOption({
		// 				yAxis: {
		// 					max: handleMax(num * 8) // 删除最大值限制
		// 				}
		// 			});
		// 		}
		// 	}
		// });

		// console.log(my_chart_line.value, 'my_chart_line.value');
	};

	// ***********************************************************************************************************
	//                                                   微网功率曲线                                            //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                              微网功率导出                                                //
	// ***********************************************************************************************************
	const export_loading = ref(false);
	const exportFn = () => {
		let time_judge = timeRange(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'date'
		);

		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}
		let time_result = handleTimeParams(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'day'
		);
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;
		let params = {
			dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: Number(project_id.value),
			startTime
		};
		form_state_echarts_line.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_line.compareStartTime)
					.startOf('day')
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;
		params.extTypes = checkedValues.value.map((item) => Number(item));

		export_loading.value = true;
		exportMinuteActivePowerHttp(params)
			.then((data) => {
				let templateName =
					dayjs(startTime).format('YYYYMMDD') +
					'-' +
					dayjs(endTime).format('YYYYMMDD') +
					'微网功率曲线报表';
				if (!data) {
					return;
				}
				const url = window.URL.createObjectURL(new Blob([data], { type: 'application/zip' }));
				let link = document.getElementById('download');
				link.href = url;
				link.setAttribute('download', `${templateName}.xlsx`);
				link.click();
				export_loading.value = false;
			})
			.catch((error) => {
				// message.error(error.message)
				export_loading.value = false;
			});
	};
	// ***********************************************************************************************************
	//                                              微网功率导出                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                              需量和储能功率曲线                                            //
	// ***********************************************************************************************************

	// // 时间参数
	// const form_state_echarts_line_1 = reactive({
	//   time: [dayjs().startOf('day'), dayjs()],
	//   compareStartTime: dayjs().subtract(1, 'day')
	// })

	// // 重置查询条件
	// const resetEchartsLineInput_1 = () => {
	//   Object.assign(form_state_echarts_line_1, {
	//     time: [dayjs().startOf('day'), dayjs()],
	//     compareStartTime: dayjs().subtract(1, 'day')
	//   })
	// }

	// // 时间插件
	// const time_status_echarts_line_1 = ref(false)

	// // 查询
	// const xaxis_data_echarts_line_1 = ref([])
	// const yaxis_data_echarts_line_1 = ref([])
	// const xaxis_data_echarts_line_contrast_1 = ref([])
	// const yaxis_data_echarts_line_contrast_1 = ref([])
	// const unit_echarts_line_1 = ref('KW')
	// const my_chart_line_1 = ref()
	// const capacity_echarts_line_1 = ref(null)
	// // 根据条件查询需量和储能功率
	// const getEchartsLine_1 = () => {
	//   let time_result = handleTimeParams(
	//     form_state_echarts_line_1.time[0],
	//     form_state_echarts_line_1.time[1],
	//     'day'
	//   )
	//   let startTime = time_result.startTime
	//   let endTime = time_result.endTime

	//   let params = {
	//     dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
	//     endTime,
	//     projectId: project_id.value,
	//     startTime
	//   }
	//   form_state_echarts_line_1.compareStartTime
	//     ? (params.compareStartTime = dayjs(form_state_echarts_line_1.compareStartTime)
	//         .startOf('day')
	//         .format('YYYY-MM-DD HH:mm:ss'))
	//     : null

	//   // statMinuteRealDemandAndActivePowerHttp(params)
	//   // .then((data) => {
	//   //   handleDataEchartsLine_1(data.statisticList)
	//   // })
	//   // .catch((error) => {
	//   //   message.error(error.message)
	//   // })
	//   let params1 = {
	//     elementCode: 'CN',
	//     projectId: project_id.value
	//   }
	//   // let params1 = {
	//   //   endTime,
	//   //   projectId: project_id.value,
	//   //   startTime
	//   // }
	//   // 功率曲线
	//   Promise.all([
	//     statMinuteRealDemandAndActivePowerHttp(params), // 功率曲线
	//     getStandardValueByElement(params1) // 参考线
	//   ])
	//     .then(([data1, data2]) => {
	//       console.log(111111111);
	//       if (data2) {
	//         capacity_echarts_line_1.value = data2.capacity
	//       } else {
	//         capacity_echarts_line_1.value = 0
	//       }

	//       handleDataEchartsLine_1(data1.statisticList)
	//     })
	//     .catch((error) => {
	//       // message.error(error.message)
	//     })
	// }

	// const handleDataEchartsLine_1 = (data) => {
	//   // let x_axis = []
	//   // let y_axis = []

	//   if (data[0].elementList.length > 0) {
	//     let result = init_x_y(data[0].elementList)
	//     xaxis_data_echarts_line_1.value = result.x_axis
	//     yaxis_data_echarts_line_1.value = result.y_axis
	//   }
	//   if (data[0].compareElementList.length > 0) {
	//     let result_contrast = init_x_y(data[0].compareElementList)
	//     xaxis_data_echarts_line_contrast_1.value = result_contrast.x_axis
	//     yaxis_data_echarts_line_contrast_1.value = result_contrast.y_axis
	//   }

	//   drawMapEchartsLine_1()
	// }

	// const drawMapEchartsLine_1 = () => {
	//   if (my_chart_line_1.value) {
	//     my_chart_line_1.value.dispose()
	//   }
	//   my_chart_line_1.value = markRaw(echarts.init(document.getElementById('echarts_line_id_1')))

	//   let option_result = handleLineMapOption(
	//     xaxis_data_echarts_line_1.value,
	//     yaxis_data_echarts_line_1.value,
	//     xaxis_data_echarts_line_contrast_1.value,
	//     yaxis_data_echarts_line_contrast_1.value
	//   )
	//   // 增加一个参考线
	//   // console.log(capacity_echarts_line_1.value)
	//   option_result.series[0].markLine = {
	//     silent: false,
	//     lineStyle: {
	//       color: 'red'
	//     },
	//     label: {
	//       position: 'middle',
	//       formatter: [`{a|基准值:${capacity_echarts_line_1.value}}`].join('\n'),
	//       rich: {
	//         a: {
	//           color: 'red',
	//           lineHeight: 10
	//         }
	//       },
	//       color: 'red'
	//     },

	//     data: [
	//       {
	//         yAxis: capacity_echarts_line_1.value
	//       }
	//     ]
	//   }
	//   // 判断参考线的值和最大值之间的大小
	//   let max = Math.max(option_result.yAxis[0].max, capacity_echarts_line_1.value)
	//   max = handleMax(max)

	//   option_result.yAxis[0].max = max
	//   my_chart_line_1.value.setOption(option_result)
	// }

	// // ***********************************************************************************************************
	// //                                              需量和储能功率曲线                                            //
	// // ***********************************************************************************************************

	// // ***********************************************************************************************************
	// //                                             需量和储能功率导出                                                //
	// // ***********************************************************************************************************
	// const export_loading_1 = ref(false)
	// const exportFn_1 = () => {
	//   let time_result = handleTimeParams(
	//     form_state_echarts_line_1.time[0],
	//     form_state_echarts_line_1.time[1],
	//     'day'
	//   )
	//   let startTime = time_result.startTime
	//   let endTime = time_result.endTime
	//   let params = {
	//     dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
	//     endTime,
	//     projectId: Number(project_id.value),
	//     startTime
	//   }
	//   form_state_echarts_line_1.compareStartTime
	//     ? (params.compareStartTime = dayjs(form_state_echarts_line_1.compareStartTime)
	//         .startOf('day')
	//         .format('YYYY-MM-DD HH:mm:ss'))
	//     : null
	//   export_loading_1.value = true
	//   exportMiuteRealDemandAndActivePower(params)
	//     .then((data) => {
	//       let templateName = dayjs(startTime).format('YYYY-MM-DD') + '-' + dayjs(endTime).format('YYYY-MM-DD') + '需量和储能功率'
	//       if (!data) {
	//         return
	//       }
	//       const url = window.URL.createObjectURL(new Blob([data], { type: 'application/zip' }))
	//       let link = document.getElementById('download')
	//       link.href = url
	//       link.setAttribute('download', `${templateName}.xlsx`)
	//       link.click()
	//       export_loading_1.value = false
	//     })
	//     .catch((error) => {
	//       // message.error(error.message)
	//       export_loading_1.value = false
	//     })
	// }
	// // ***********************************************************************************************************
	// //                                              需量和储能功率导出                                                //
	// // ***********************************************************************************************************

	// // ***********************************************************************************************************
	// //                                              微网电量曲线                                                //
	// // ***********************************************************************************************************

	// // 时间参数
	// const form_state_echarts_bar = reactive({
	//   time: [dayjs().startOf('day'), dayjs()],
	//   compareStartTime: dayjs().subtract(1, 'day')
	// })

	// // 重置查询条件
	// const resetInputEchartsBar = () => {
	//   Object.assign(form_state_echarts_bar, {
	//     time: [dayjs().startOf('day'), dayjs()],
	//     compareStartTime: dayjs().subtract(1, 'day')
	//   })
	// }

	// // 时间插件
	// const time_status_echarts_bar = ref(false)

	// // 查询微网电量曲线
	// const getEchartsBar = () => {
	//   let time_result = handleTimeParams(
	//     form_state_echarts_bar.time[0],
	//     form_state_echarts_bar.time[1],
	//     'day'
	//   )
	//   let startTime = time_result.startTime
	//   let endTime = time_result.endTime

	//   let params = {
	//     dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
	//     endTime,
	//     projectId: project_id.value,
	//     startTime
	//   }
	//   form_state_echarts_bar.compareStartTime
	//     ? (params.compareStartTime = dayjs(form_state_echarts_bar.compareStartTime)
	//         .startOf('day')
	//         .format('YYYY-MM-DD HH:mm:ss'))
	//     : null

	//   statPositiveAndReverseActivePowerHttp(params)
	//     .then((data) => {
	//       handleDataEchartsBar(data.statisticList)
	//     })
	//     .catch((error) => {
	//       // message.error(error.message)
	//     })
	// }

	// const handleDataEchartsBar = (data) => {
	//   let x_axis = []
	//   let y_axis = []
	//   let y_axis_fan = []
	//   for (let i = 0; i < data.length; i++) {
	//     // 正向有功和反向有功的处理
	//     let item_i = data[i]
	//     let name_i = data[i].paramName

	//     for (let j = 0; j < item_i.elementList.length; j++) {
	//       // 处理元素列表的数据
	//       let item_j = item_i.elementList[j]
	//       let name_j = item_i.elementList[j].elementCodeDesc
	//       if (j == 0 && item_j.detailList.length > 0) {
	//         // 仅执行一次就行
	//         x_axis = handleXAxisTime(item_j.detailList)
	//       }
	//       let value_list = []
	//       for (let k = 0; k < item_j.detailList.length; k++) {
	//         // 具体的时间值
	//         let item_k = item_j.detailList[k]
	//         value_list.push(item_k.value)
	//         // value_list.push(Math.ceil(200 * Math.random()))
	//       }
	//       y_axis.push({
	//         name: name_i + '-' + name_j,
	//         value_list: value_list
	//       })
	//       y_axis_fan.push({
	//         name: name_i + '-' + name_j + 'fan',
	//         value_list: value_list
	//       })
	//     }
	//   }
	//   yaxis_data_echarts_bar.value = y_axis
	//   yaxis_data_echarts_bar_constract.value = y_axis_fan
	//   xaxis_data_echarts_bar.value = x_axis
	//   xaxis_data_echarts_bar_constract.value = x_axis
	//   drawMapEchartsBar()
	// }

	// const yaxis_data_echarts_bar = ref([])
	// const yaxis_data_echarts_bar_constract = ref([])
	// const unit_echarts_bar = ref('cn')
	// const xaxis_data_echarts_bar = ref([])
	// const xaxis_data_echarts_bar_constract = ref([])
	// const my_chart_bar = ref()

	// const drawMapEchartsBar = () => {
	//   let result_option = handleBarMapOption(
	//     xaxis_data_echarts_bar.value,
	//     xaxis_data_echarts_bar_constract.value,
	//     yaxis_data_echarts_bar.value,
	//     yaxis_data_echarts_bar_constract.value,
	//     unit_echarts_bar.value
	//   )
	//   if (my_chart_bar.value) {
	//     my_chart_bar.value.dispose()
	//   }
	//   my_chart_bar.value = markRaw(echarts.init(document.getElementById('echarts_bar_id')))
	//   my_chart_bar.value.setOption(result_option)
	// }

	// // ***********************************************************************************************************
	// //                                              微网电量曲线                                                //
	// // ***********************************************************************************************************

	// // ***********************************************************************************************************
	// //                                             微网电量导出                                                //
	// // ***********************************************************************************************************
	// const export_loading_2 = ref(false)
	// const exportFn_2 = () => {
	//   let time_result = handleTimeParams(
	//     form_state_echarts_bar.time[0],
	//     form_state_echarts_bar.time[1],
	//     'day'
	//   )
	//   let startTime = time_result.startTime
	//   let endTime = time_result.endTime
	//   let params = {
	//     dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
	//     endTime,
	//     projectId: Number(project_id.value),
	//     startTime
	//   }
	//   form_state_echarts_bar.compareStartTime
	//     ? (params.compareStartTime = dayjs(form_state_echarts_bar.compareStartTime)
	//         .startOf('day')
	//         .format('YYYY-MM-DD HH:mm:ss'))
	//     : null
	//   export_loading_2.value = true
	//   exportPositiveAndReverseActivePower(params)
	//     .then((data) => {
	//       let templateName = dayjs(startTime).format('YYYY-MM-DD') + '-' + dayjs(endTime).format('YYYY-MM-DD') + '微网电量'
	//       if (!data) {
	//         return
	//       }
	//       const url = window.URL.createObjectURL(new Blob([data], { type: 'application/zip' }))
	//       let link = document.getElementById('download')
	//       link.href = url
	//       link.setAttribute('download', `${templateName}.xlsx`)
	//       link.click()
	//       export_loading_2.value = false
	//     })
	//     .catch((error) => {
	//       // message.error(error.message)
	//       export_loading_2.value = false
	//     })
	// }
	// ***********************************************************************************************************
	//                                              微网电量导出                                                //
	// ***********************************************************************************************************

	// 时间间隔改变处理
	const changeTimeInterval = () => {
		console.log('时间间隔改变为:', statisType.value);
		// 根据时间间隔的变化进行相应的处理
		if (statisType.value === 15) {
			// debugger
			if (project_collectionInterval.value === 1) {
				if (hasSpotTrading.value) {
					isElectricityShow.value = true;
				} else {
					isElectricityShow.value = false;
				}
			} else {
				isElectricityShow.value = true;
			}
		} else {
			isElectricityShow.value = false;
		}
		console.log('isElectricityShow', isElectricityShow.value);
		searchAll();
	};

	// 全页面查询
	const searchAll = () => {
		// 查询微网功率
		getEchartsLine();
		// 查询需量和储能功率
		// getEchartsLine_1()
		// 查询微网电量
		// getEchartsBar()
	};

	onMounted(() => {
		const button = document.getElementById('selectBt');
		buttonTop.value = button.getBoundingClientRect().bottom + 30;
		console.log(buttonTop.value);

		// 项目id
		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			project_id.value = project_mess.project_id;
			project_title.value = project_mess.project_title;
			project_collectionInterval.value = project_mess.collectionInterval;
			if (project_collectionInterval.value == 1) {
				time_interval_options.value = [
					{ value: 1, label: '1分钟' },
					{ value: 15, label: '15分钟' }
				];
				statisType.value = 1;
				isSpecialPro.value = true;
				isElectricityShow.value = false;
			} else {
				time_interval_options.value = [{ value: 15, label: '15分钟' }];
				statisType.value = 15;
				isSpecialPro.value = false;
				isElectricityShow.value = true;
			}
			getStrategyByProjectId(project_id.value).then((data) => {
				if (JSON.stringify(data) !== '{}') {
					if (data.economicStrategy.includes('SPOT_TRADING')) {
						hasSpotTrading.value = true;
					} else {
						hasSpotTrading.value = false;
					}
				}
			});
		} else {
			message.error('未选择项目，请重新选择！');
		}
		//
		// drawMapEchartsBar()
		searchAll();
	});
</script>
<style lang="less" scoped>
	.main_contain_scroll {
		background-color: transparent !important;
		padding: 0;
		overflow: hidden;
	}

	:deep(.ant-picker-dropdown) {
		left: 0px !important;
		top: 38px !important;
	}

	.map_item {
		height: 100%;
		padding: 32px;
		background-color: #ffffff;
		margin-bottom: 0px;
		box-sizing: border-box;

		.map_title {
			height: 18px;
			line-height: 18px;
			font-family: SourceHanSansCN-Regular;
			font-weight: bold;
			font-size: 18px;
			color: #092649;
		}

		.params_list {
			height: 36px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			margin-top: 32px;
			margin-bottom: 20px;
			// align-items: center;

			// .params_list_left {
			// 	height: 36px;
			// 	line-height: 36px;
			// 	display: flex;
			// 	flex-direction: row;
			// 	justify-content: space-between;
			// 	align-items: center;
			// }
		}

		&:last-child {
			margin-bottom: 0px;
		}
	}

	.map_line_contain {
		height: calc(100% - 35px - 32px - 20px - 17.5px);
		width: 100%;
		position: relative;

		.unit_mess {
			color: #949aa6;
			font-size: 14px;
			font-family: 'shsr';
			position: absolute;
			top: 5px;
		}

		.map_line {
			height: 100%;
			width: 100%;
		}

		.no_data {
			position: absolute;
			top: -5%;
			left: 0;
			right: 0;
			bottom: 0;
			padding-top: 0px;
		}
	}

	.params_list_right {
		display: flex;
	}

	:deep(.ant-modal-header) {
		border-bottom: none;
	}
</style>
