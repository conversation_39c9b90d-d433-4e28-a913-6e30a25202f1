<template>
	<div class="main_contain main_contain_thirdMenu">
		<a
			href="#"
			id="download"
			style="position: absolute; top: -1000px; right: -1000px"
		></a>
		<div class="main_contain_scroll">
			<div class="map_item">
				<div class="map_title">储能功率&SOC</div>
				<div class="params_list">
					<div class="params_list_left">
						<a-form
							:model="form_state_echarts_line"
							name="form_gl"
							layout="inline"
							autocomplete="off"
						>
							<a-form-item
								label="时间选择"
								name="time"
							>
								<a-range-picker
									:status="time_status_echarts_line ? 'error' : ''"
									:allowClear="false"
									v-model:value="form_state_echarts_line.time"
									:getPopupContainer="(e) => e.parentNode"
									style="width: 300px"
									:disabledDate="
										(current) => {
											return current && current > dayjs().endOf('day');
										}
									"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/clock.png"
											alt="#"
										/>
									</template>
								</a-range-picker>
							</a-form-item>
							<a-form-item>
								<a-checkbox
									v-model:checked="contrast"
									@change="isContrastChange"
									style="font-size: 16px"
								>
									对比
								</a-checkbox>
							</a-form-item>
							<a-form-item
								label="添加对比"
								name="compareStartTime"
								v-if="contrast"
							>
								<a-date-picker
									placeholder="请选择起始日"
									style="width: 200px"
									:allowClear="false"
									:disabled-date="
										(current) => {
											return current && current > dayjs().endOf('day');
										}
									"
									v-model:value="form_state_echarts_line.compareStartTime"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/picker_month.png"
											alt="#"
										/>
									</template>
								</a-date-picker>
							</a-form-item>

							<a-form-item>
								<a-button
									type="primary"
									@click="getEchartsLine"
									html-type="submit"
								>
									查询
								</a-button>
							</a-form-item>
							<a-form-item>
								<a-button @click="resetEchartsLineInput">重置</a-button>
							</a-form-item>
						</a-form>
					</div>
					<div class="params_list_right">
						<a-button
							type="primary"
							@click="skipUnnormal"
							style="margin-right: 16px; display: flex"
						>
							<div class="icon_button">
								<img
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/unnormal_setting.png"
									alt=""
								/>
								<span>异常规则配置</span>
							</div>
						</a-button>
						<a-button
							:loading="export_loading"
							@click="exportFn"
							:disabled="!hasData"
							style="display: flex"
							@mouseenter="hover1 = true"
							@mouseleave="hover1 = false"
						>
							<div class="icon_button">
								<img
									v-if="hover1 && hasData"
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_hover.png"
									alt=""
								/>
								<img
									v-else
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_dark.png"
									alt=""
								/>
								<span>导出</span>
							</div>
						</a-button>
					</div>
				</div>
				<div class="map_line_contain">
					<!-- <span class="unit_mess">kW</span> -->
					<span class="unit_mess1">%</span>
					<div
						v-if="hasData"
						class="map_line"
						id="echarts_line_id"
					></div>
					<a-empty
						v-if="!hasData"
						style="margin: 0 auto; position: relative; top: 30%"
					/>
				</div>
			</div>
			<!-- <div class="map_item">
        <div class="map_title">SoC/%</div>
        <div class="params_list">
          <div class="params_list_left">
            <a-form id="xuliang" :model="form_state_echarts_line" name="xuliang" layout="inline" autocomplete="off">
              <a-form-item label="时间选择" name="time">
                <a-range-picker :status="time_status_echarts_line_1 ? 'error' : ''" :allowClear="false"
                  v-model:value="form_state_echarts_line_1.time" style="width: 300px; margin-right: 15px">
                  <template #suffixIcon>
                    <img style="width: 18px; height: 18px" src="/assets/icon/clock.png" alt="#" />
                  </template>
                </a-range-picker>
              </a-form-item>

              <a-form-item label="添加对比" name="compareStartTime">
                <a-date-picker style="width: 200px" :allowClear="false" :disabled-date="(current) => {
                  return current && current > dayjs().endOf('day')
                }
                  " v-model:value="form_state_echarts_line_1.compareStartTime" />
              </a-form-item>

              <a-form-item>
                <a-button type="primary" @click="getEchartsLine_1">查询</a-button>
              </a-form-item>
              <a-form-item>
                <a-button @click="resetEchartsLineInput_1">重置</a-button>
              </a-form-item>
            </a-form>
          </div>
          <div class="params_list_right">

            <a-button type="primary" @click="skipUnnormal" style="margin-right: 16px;display:flex">
              <div class="icon_button">
                <img style="display: inline-block; height: 18px; width: 18px" src="/assets/icon/unnormal_setting.png"
                  alt="" />
                <span>异常规则配置</span>
              </div>
            </a-button>
            <a-button :loading="export_loading_1" @click="exportFn_1" :disabled="!hasData1">
              <div class="icon_button">
                <img style="display: inline-block; height: 18px; width: 18px;position: relative;top: 1px;"
                  src="/assets/icon/export_icon_dark.png" alt="" />
                <span>导出</span>
              </div>
            </a-button>
          </div>
        </div>
        <div class="map_line_contain">
          <span class="unit_mess">%</span>
          <div v-if="hasData1" class="map_line" id="echarts_line_id_1"></div>
          <a-empty v-if="!hasData1" style="margin: 0 auto; position: relative; top: 30%" />
        </div>
      </div> -->

			<div class="map_item">
				<div class="map_title">储能充放电量</div>
				<div class="params_list">
					<div class="params_list_left">
						<a-form
							id="cn_form"
							:model="form_state_echarts_bar"
							name="cn_form"
							layout="inline"
							autocomplete="off"
							@finish="onFinishElec"
						>
							<a-form-item
								label="粒度选择"
								name="time"
							>
								<a-select
									ref="select_time_type"
									:getPopupContainer="(e) => e.parentNode"
									v-model:value="form_state_echarts_bar.time_type"
									style="width: 120px"
								>
									<a-select-option value="1">日</a-select-option>
									<a-select-option value="2">月</a-select-option>
									<a-select-option value="3">年</a-select-option>
								</a-select>
							</a-form-item>
							<a-form-item
								label="时间选择"
								name="time"
							>
								<a-range-picker
									:allowClear="false"
									:status="time_status_echarts_bar ? 'error' : ''"
									:picker="time_picker"
									v-model:value="form_state_echarts_bar.time"
									:getPopupContainer="(e) => e.parentNode"
									style="width: 300px"
									:disabledDate="
										(current) => {
											return current && current > dayjs().endOf(time_picker);
										}
									"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/clock.png"
											alt="#"
										/>
									</template>
								</a-range-picker>
							</a-form-item>
							<a-form-item>
								<a-checkbox
									v-model:checked="powerContrast"
									@change="isPowerContrastChange"
									style="font-size: 16px"
								>
									对比
								</a-checkbox>
							</a-form-item>
							<a-form-item
								label="添加对比"
								name="compareStartTime"
								v-if="powerContrast"
							>
								<a-date-picker
									style="width: 200px"
									:disabled-date="
										(current) => {
											return current && current > dayjs().endOf('day');
										}
									"
									:allowClear="false"
									:picker="time_picker"
									v-model:value="form_state_echarts_bar.compareStartTime"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/picker_month.png"
											alt="#"
										/>
									</template>
								</a-date-picker>
							</a-form-item>

							<a-form-item>
								<a-button
									type="primary"
									@click="getEchartsBar"
								>
									查询
								</a-button>
							</a-form-item>
							<a-form-item>
								<a-button @click="resetInputEchartsBar">重置</a-button>
							</a-form-item>
						</a-form>
					</div>
					<div class="params_list_right">
						<a-button
							type="primary"
							@click="skipUnnormal"
							style="margin-right: 16px; display: flex"
						>
							<div class="icon_button">
								<img
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/unnormal_setting.png"
									alt=""
								/>
								<span>异常规则配置</span>
							</div>
						</a-button>
						<a-button
							:loading="export_loading_2"
							@click="exportFn_2"
							:disabled="!hasData2"
							style="display: flex"
							@mouseenter="hover2 = true"
							@mouseleave="hover2 = false"
						>
							<div class="icon_button">
								<img
									v-if="hover2 && hasData2"
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_hover.png"
									alt=""
								/>
								<img
									v-else
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_dark.png"
									alt=""
								/>
								<span>导出</span>
							</div>
						</a-button>
						<a-form-item>
							<a-checkbox
								v-if="hasResignal"
								v-model:checked="cornerDisplay"
								@change="getEchartsBar"
								style="position: absolute; top: 55px; right: 0px; width: 130px"
							>
								显示余光入储
							</a-checkbox>
						</a-form-item>
					</div>
				</div>
				<div class="map_line_contain">
					<!-- <span class="unit_mess">kWh</span> -->
					<div
						v-if="hasData2"
						class="map_line"
						id="echarts_bar_id"
					></div>
					<a-empty
						v-if="!hasData2"
						style="margin: 0 auto; position: relative; top: 30%"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, reactive, onMounted, markRaw, watch } from 'vue';
	import * as echarts from 'echarts';
	import {
		handleTime,
		timeRange,
		handleTimeParams,
		handleLineMapOption,
		handleNoContrastLineMapOption,
		handleLineMapSelection,
		handleBarMapOption,
		handleBarMapSelection,
		handleXAxisTime,
		init_x_y,
		init_x_y_device,
		handleMax
	} from '/assets/js/commonTool.js';

	import { getStrategyByProjectId } from '/pages/configurationManagement/views/api.js';

	import {
		cnStatMinuteActivePower,
		cnStatMinuteSoC,
		cnStatMinuteActivePowerAndSOC,
		cnStatPositiveAndReverseActivePower,
		getStandardValueByEleratePrice,
		getStandardValueByElement,
		exportMinuteActivePowerHttp,
		exportMiuteRealDemandAndActivePower,
		exportPositiveAndReverseActivePower,
		cnExportPositiveAndReverseActivePower,
		cnExportActivePowerAndSoC
	} from './api.js';
	import dayjs from 'dayjs';
	import { message } from 'ant-design-vue';
	import { eventBus } from '/assets/js/eventBus'; // 导入事件总线实例
	// ***********************************************************************************************************
	//                                                   全局参数配置                                            //
	// ***********************************************************************************************************

	const contrast = ref(false);
	const powerContrast = ref(false);
	const cornerDisplay = ref(false);
	const project_id = ref(null);
	const skipUnnormal = () => {
		eventBus.emit('menu-change', [1, 2, '/pages/operationMonitoring/#/ruleSetting']); // 触发事件
	};
	// ***********************************************************************************************************
	//                                                   全局参数配置                                            //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                   储能功率&控制曲线/kW                                            //
	// ***********************************************************************************************************
	const hasData = ref(true);
	// 时间参数
	const form_state_echarts_line = reactive({
		time: [dayjs().startOf('day'), dayjs()],
		compareStartTime: null
	});

	const hover1 = ref(false);
	const hover2 = ref(false);

	const isContrastChange = () => {
		if (contrast.value) {
			form_state_echarts_line.compareStartTime = dayjs().subtract(1, 'day');
		} else {
			form_state_echarts_line.compareStartTime = null;
		}
	};

	const isPowerContrastChange = () => {
		if (powerContrast.value) {
			let type = 'day';
			if (time_picker.value != 'date') {
				type = time_picker.value;
			}
			form_state_echarts_bar.compareStartTime = dayjs().subtract(1, type);
		} else {
			form_state_echarts_bar.compareStartTime = null;
		}
	};

	// 重置查询条件
	const resetEchartsLineInput = () => {
		contrast.value = false;
		Object.assign(form_state_echarts_line, {
			time: [dayjs().startOf('day'), dayjs()],
			compareStartTime: null
		});
		getEchartsLine();
	};

	// 时间插件
	const time_status_echarts_line = ref(false);

	// 绘图的基本数据
	const xaxis_data_echarts_line = ref([]);
	const xaxis_data_echarts_line_contrast = ref([]);
	const yaxis_data_echarts_line = ref([]);
	const yaxis_data_echarts_line_contrast = ref([]);
	const unit_echarts_line = ref('KW');
	const my_chart_line = ref();
	const capacity_echarts_line = ref(null);

	// 根据条件查询曲线数据
	const getEchartsLine = () => {
		if (!form_state_echarts_line.compareStartTime && contrast.value) {
			message.warn('请选择对比起始日');
			return;
		}

		let time_judge = timeRange(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'date'
		);

		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}
		let time_result = handleTimeParams(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'day'
		);
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;

		let params = {
			dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: project_id.value,
			startTime
		};

		form_state_echarts_line.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_line.compareStartTime)
					.startOf('day')
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;

		let params1 = { projectId: project_id.value, elementCode: 'CN' };

		Promise.all([
			cnStatMinuteActivePowerAndSOC(params), // 功率曲线
			getStandardValueByElement(params1) // 参考线
		])
			.then(([data1, data2]) => {
				if (data2) {
					capacity_echarts_line.value = data2.capacity;
					// console.log(capacity_echarts_line.value);
				} else {
					capacity_echarts_line.value = 0;
				}
				if (data1) {
					handlePowerAndSOCEchartsLine(data1.statisticList);
				} else {
					hasData.value = false;
				}
			})
			.catch((error) => {
				console.log(error, 123);
				message.error(error.message);
			});
	};

	const handleDataEchartsLine = (data) => {
		// 正常值
		if (data[0].elementList.length > 0) {
			let result = init_x_y(data[0].elementList);
			xaxis_data_echarts_line.value = result.x_axis;
			yaxis_data_echarts_line.value = result.y_axis;
		}
		// 对比值
		if (data[0].compareElementList.length > 0) {
			let result_contrast = init_x_y(data[0].compareElementList);
			xaxis_data_echarts_line_contrast.value = result_contrast.x_axis;
			yaxis_data_echarts_line_contrast.value = result_contrast.y_axis;
		}

		drawMapEchartsLine();
	};

	const drawMapEchartsLine = () => {
		if (my_chart_line.value) {
			my_chart_line.value.dispose();
		}
		my_chart_line.value = markRaw(echarts.init(document.getElementById('echarts_line_id')));
		let option_result = handleLineMapOption(
			xaxis_data_echarts_line.value,
			yaxis_data_echarts_line.value,
			xaxis_data_echarts_line_contrast.value,
			yaxis_data_echarts_line_contrast.value,
			unit_echarts_line.value
		);
		// console.log(option_result, 11111)
		// 增加一个参考线

		if (contrast.value && option_result.series[1].name == '储能总功率对比') {
			option_result.series[1].markLine = {
				silent: false,
				lineStyle: { color: 'red' },
				label: {
					position: 'middle',
					formatter: [`{a|额定功率:${capacity_echarts_line.value}}`].join('\n'),
					rich: { a: { color: 'red', lineHeight: 10 } },
					color: 'red'
				},
				data: [{ yAxis: capacity_echarts_line.value }]
			};
		}

		// 判断参考线的值和最大值之间的大小
		let max = Math.max(option_result.yAxis[0].max, capacity_echarts_line.value);
		max = handleMax(max);

		// console.log(capacity_echarts_line.value, 'capacity_echarts_line.value');
		// console.log(max, 'max');

		if (option_result.series[0].name == '储能总功率') {
			option_result.series[0].markLine = {
				silent: false,
				lineStyle: { color: 'red' },
				label: {
					position: 'middle',
					formatter: [`{a|额定功率:${capacity_echarts_line.value}}`].join('\n'),
					rich: { a: { color: 'red', lineHeight: 10 } },
					color: 'red'
				},
				data: [{ yAxis: capacity_echarts_line.value }]
			};
		}

		option_result.yAxis[0].max = max;
		// delete option_result.yAxis[0].max
		my_chart_line.value.setOption(option_result);
	};

	// ***********************************************************************************************************
	//                                                   储能功率&控制曲线/kW                                            //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                              储能功率导出                                                //
	// ***********************************************************************************************************
	const export_loading = ref(false);
	const exportFn = () => {
		if (!form_state_echarts_line.compareStartTime && contrast.value) {
			message.warn('请选择对比起始日');
			return;
		}

		let time_judge = timeRange(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'date'
		);

		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}
		let time_result = handleTimeParams(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'day'
		);
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;

		const now = dayjs();
		let targetTime = dayjs(startTime);
		if (targetTime.isAfter(now, 'day')) {
			message.error('所选时间范围不符合要求');
			return;
		}

		let params = {
			dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: Number(project_id.value),
			startTime
		};
		form_state_echarts_line.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_line.compareStartTime)
					.startOf('day')
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;
		export_loading.value = true;
		cnExportActivePowerAndSoC(params)
			.then((data) => {
				// console.log(data);
				let templateName =
					dayjs(startTime).format('YYYYMMDD') +
					'-' +
					dayjs(endTime).format('YYYYMMDD') +
					'储能功率和控制功率曲线报表';
				if (!data) {
					return;
				}
				const url = window.URL.createObjectURL(new Blob([data], { type: 'application/zip' }));
				let link = document.getElementById('download');
				link.href = url;
				link.setAttribute('download', `${templateName}.xlsx`);
				link.click();
				export_loading.value = false;
			})
			.catch((error) => {
				console.log(error);
				message.error(error.message);
				export_loading.value = false;
			});
	};
	// ***********************************************************************************************************
	//                                              储能功率导出                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                              SoC/%                                            //
	// ***********************************************************************************************************
	const hasData1 = ref(true);
	// 时间参数
	const form_state_echarts_line_1 = reactive({
		time: [dayjs().startOf('day'), dayjs()],
		compareStartTime: dayjs().subtract(1, 'day')
	});

	// 重置查询条件
	const resetEchartsLineInput_1 = () => {
		Object.assign(form_state_echarts_line_1, {
			time: [dayjs().startOf('day'), dayjs()],
			compareStartTime: dayjs().subtract(1, 'day')
		});
		getEchartsLine_1();
	};

	// 时间插件
	const time_status_echarts_line_1 = ref(false);

	// 查询
	const xaxis_data_echarts_line_1 = ref([]);
	const yaxis_data_echarts_line_1 = ref([]);
	const xaxis_data_echarts_line_contrast_1 = ref([]);
	const yaxis_data_echarts_line_contrast_1 = ref([]);
	const unit_echarts_line_1 = ref('%');
	const my_chart_line_1 = ref();

	// 根据条件查询需量和储能功率
	const getEchartsLine_1 = () => {
		let time_judge = timeRange(
			form_state_echarts_line_1.time[0],
			form_state_echarts_line_1.time[1],
			'date'
		);

		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}
		let time_result = handleTimeParams(
			form_state_echarts_line_1.time[0],
			form_state_echarts_line_1.time[1],
			'day'
		);
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;

		let params = {
			dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: project_id.value,
			startTime
		};
		form_state_echarts_line_1.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_line_1.compareStartTime)
					.startOf('day')
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;

		cnStatMinuteSoC(params)
			.then((data) => {
				//console.log(data.statisticList)
				if (data) {
					handleDataEchartsLine_1(data.statisticList);
				} else {
					hasData1.value = false;
				}
			})
			.catch((error) => {
				console.log(error);
				// message.error("数据处理问题")
			});
	};

	const handlePowerAndSOCEchartsLine = (data) => {
		if (data[0].elementList.length > 0) {
			let result = init_x_y(data[0].elementList);
			xaxis_data_echarts_line.value = result.x_axis;
			yaxis_data_echarts_line.value = result.y_axis;
		}

		if (contrast.value && data[0].compareElementList.length > 0) {
			let result_contrast = init_x_y(data[0].compareElementList);
			xaxis_data_echarts_line_contrast.value = result_contrast.x_axis;
			yaxis_data_echarts_line_contrast.value = result_contrast.y_axis;
		}

		if (data[1].elementList.length > 0) {
			let result = init_x_y_device(data[1].elementList);
			yaxis_data_echarts_line.value.push(...result.y_axis);
		}

		if (contrast.value && data[1].compareElementList.length > 0) {
			let result_contrast = init_x_y_device(data[1].compareElementList);
			yaxis_data_echarts_line_contrast.value.push(...result_contrast.y_axis);
		}

		yaxis_data_echarts_line.value.forEach((item) => {
			if (item.name === '储能') {
				item.name = '储能总功率';
			}
		});
		yaxis_data_echarts_line_contrast.value.forEach((item) => {
			if (item.name === '储能') {
				item.name = '储能总功率';
			}
		});
		drawEchartsLine();
	};

	const drawEchartsLine = () => {
		if (my_chart_line.value) {
			my_chart_line.value.dispose();
		}
		my_chart_line.value = markRaw(echarts.init(document.getElementById('echarts_line_id')));
		let option_result = {};
		if (contrast.value) {
			option_result = handleLineMapSelection(
				xaxis_data_echarts_line.value,
				yaxis_data_echarts_line.value,
				xaxis_data_echarts_line_contrast.value,
				yaxis_data_echarts_line_contrast.value,
				unit_echarts_line.value
			);

			option_result.series[0].markLine = {
				silent: false,
				lineStyle: { color: 'red' },
				label: {
					position: 'middle',
					formatter: [`{a|额定功率:${capacity_echarts_line.value}}`].join('\n'),
					rich: { a: { color: 'red', lineHeight: 10 } },
					color: 'red'
				},
				data: [{ yAxis: capacity_echarts_line.value }]
			};
			option_result.series[1].markLine = {
				silent: false,
				lineStyle: { color: 'red' },
				label: {
					position: 'middle',
					formatter: [`{a|额定功率:${capacity_echarts_line.value}}`].join('\n'),
					rich: { a: { color: 'red', lineHeight: 10 } },
					color: 'red'
				},
				data: [{ yAxis: capacity_echarts_line.value }]
			};

			let selected = {};
			if (option_result.legend.data.length >= 4) {
				for (let i = 0; i < option_result.legend.data.length; i++) {
					selected[option_result.legend.data[i]] = i < 4; // 前两个图例默认显示，其余置灰
				}
				option_result.legend.selected = selected;
			}
		} else {
			option_result = handleLineMapSelection(
				xaxis_data_echarts_line.value,
				yaxis_data_echarts_line.value,
				'',
				[],
				unit_echarts_line.value
			);
			option_result.series[0].markLine = {
				silent: false,
				lineStyle: { color: 'red' },
				label: {
					position: 'middle',
					formatter: [`{a|额定功率:${capacity_echarts_line.value}}`].join('\n'),
					rich: { a: { color: 'red', lineHeight: 10 } },
					color: 'red'
				},
				data: [{ yAxis: capacity_echarts_line.value }]
			};

			let selected = {};
			if (option_result.legend.data.length >= 4) {
				for (let i = 0; i < option_result.legend.data.length; i++) {
					selected[option_result.legend.data[i]] = i < 4; // 前两个图例默认显示，其余置灰
				}
				option_result.legend.selected = selected;
			}
		}

		// console.log(option_result, 11111);

		// 判断参考线的值和最大值之间的大小
		let max = Math.max(option_result.yAxis[0].max, capacity_echarts_line.value);
		max = handleMax(max);
		// console.log(option_result.yAxis[0].min, '之前的option_result.yAxis[0].max');
		option_result.yAxis[0].max = max;
		if (option_result.series[0].data.every((item) => item === null)) {
			option_result.yAxis[0].min = 0;
		}
		// delete option_result.yAxis[0].max

		option_result.yAxis[0].alignTicks = true;

		option_result.series.forEach((seriesItem, i) => {
			if (!seriesItem.name.includes('储能')) {
				if (option_result.yAxis.length == 1) {
					option_result.yAxis.push({
						type: 'value',
						name: '',
						max: 100,
						min: 0,
						// alignTicks: true, 这里才是影响坐标轴最大值没显示的原因，第一个y轴自动适应刻度就可
						axisLabel: {
							showMinLabel: true,
							showMaxLabel: true,
							formatter: function (value) {
								return value.toFixed(0);
							}
						}
					});
				}
				option_result.series[i].yAxisIndex = 1;
			}
		});
		(option_result.tooltip = {
			trigger: 'axis',
			confine: true,
			formatter: function (params) {
				let html = '';
				params.forEach((v) => {
					let value = v.value;
					let unit = '';
					let time = xaxis_data_echarts_line.value[v.dataIndex];
					if (v.seriesName.includes('对比')) {
						time = xaxis_data_echarts_line_contrast.value[v.dataIndex];
					}
					if (v.seriesName.includes('储能')) {
						unit = 'kW';
					} else {
						unit = '%';
					}
					if (value != 0 && !value) {
						value = '-';
					} else {
						value = Number(parseFloat(v.value).toFixed(2));
					}
					html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${v.color};"></span>
                    ${v.seriesName} : ${time} <span style="color:${v.color};font-weight:600;font-size: 14px"> ${value} </span>   ${unit} 
                    `;
				});
				return html;
			},
			extraCssText:
				'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;'
		}),
			// console.log(option_result.yAxis, 'option_result.yAxis');

			// if(option_result.yAxis.length > 1){
			//   option_result.yAxis[1].max = 100
			//   option_result.yAxis[1].min = 0
			// }

			// console.log(option_result.yAxis[1].max,'option_result.yAxis[1].max');
			// console.log(option_result.yAxis[0].max,'option_result.yAxis[0].max');

			my_chart_line.value.setOption(option_result);
	};

	const handleDataEchartsLine_1 = (data) => {
		// let x_axis = []
		// let y_axis = []
		if (data[0].elementList.length > 0) {
			let result = init_x_y_device(data[0].elementList);
			// console.log(result);
			xaxis_data_echarts_line_1.value = result.x_axis;
			yaxis_data_echarts_line_1.value = result.y_axis;
		}
		if (data[0].compareElementList.length > 0) {
			let result_contrast = init_x_y_device(data[0].compareElementList);
			// console.log(result_contrast);
			xaxis_data_echarts_line_contrast_1.value = result_contrast.x_axis;
			yaxis_data_echarts_line_contrast_1.value = result_contrast.y_axis;
		}

		drawMapEchartsLine_1();
	};

	const drawMapEchartsLine_1 = () => {
		// console.log('----------');
		// console.log(xaxis_data_echarts_line_1.value);
		// console.log(yaxis_data_echarts_line_1.value);
		// console.log(xaxis_data_echarts_line_contrast_1.value);
		// console.log(yaxis_data_echarts_line_contrast_1.value);
		// console.log('----------');
		let option_result = handleLineMapOption(
			xaxis_data_echarts_line_1.value,
			yaxis_data_echarts_line_1.value,
			xaxis_data_echarts_line_contrast_1.value,
			yaxis_data_echarts_line_contrast_1.value,
			unit_echarts_line_1.value
		);

		if (my_chart_line_1.value) {
			my_chart_line_1.value.dispose();
		}
		if (document.getElementById('echarts_line_id_1')) {
			my_chart_line_1.value = markRaw(echarts.init(document.getElementById('echarts_line_id_1')));
			my_chart_line_1.value.setOption(option_result);
		}
	};

	// ***********************************************************************************************************
	//                                              SoC/%                                            //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                              soc导出                                                //
	// ***********************************************************************************************************
	const export_loading_1 = ref(false);
	const exportFn_1 = () => {
		let time_judge = timeRange(
			form_state_echarts_line_1.time[0],
			form_state_echarts_line_1.time[1],
			'date'
		);

		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}
		let time_result = handleTimeParams(
			form_state_echarts_line_1.time[0],
			form_state_echarts_line_1.time[1],
			'day'
		);
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;
		let params = {
			dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: Number(project_id.value),
			startTime
		};
		form_state_echarts_line_1.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_line_1.compareStartTime)
					.startOf('day')
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;
		export_loading_1.value = true;
		cnExportActivePowerAndSoC(params)
			.then((data) => {
				let templateName =
					dayjs(startTime).format('YYYYMMDD') +
					'-' +
					dayjs(endTime).format('YYYYMMDD') +
					'储能SoC曲线报表';
				if (!data) {
					return;
				}
				const url = window.URL.createObjectURL(new Blob([data], { type: 'application/zip' }));
				let link = document.getElementById('download');
				link.href = url;
				link.setAttribute('download', `${templateName}.xlsx`);
				link.click();
				export_loading_1.value = false;
			})
			.catch((error) => {
				console.log(error);
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
				export_loading_1.value = false;
			});
	};
	// ***********************************************************************************************************
	//                                              soc导出                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                              储能充放电量                                                //
	// ***********************************************************************************************************
	const hasData2 = ref(true);
	// 时间参数
	const time_picker = ref('date');
	const form_state_echarts_bar = reactive({
		time: [dayjs().startOf('day'), dayjs()],
		compareStartTime: null,
		time_type: '1'
	});
	watch(
		() => form_state_echarts_bar.time_type,
		(newVal) => {
			if (newVal == 1) {
				time_picker.value = 'date';
			} else if (newVal == 2) {
				time_picker.value = 'month';
			} else if (newVal == 3) {
				time_picker.value = 'year';
			}
			isPowerContrastChange();
		},
		{ deep: true }
	);
	// 重置查询条件
	const resetInputEchartsBar = () => {
		cornerDisplay.value = false;
		powerContrast.value = false;
		Object.assign(form_state_echarts_bar, {
			time: [dayjs().startOf('day'), dayjs()],
			compareStartTime: null,
			time_type: '1'
		});
		time_picker.value = 'date';
		getEchartsBar();
	};

	// 时间插件
	const time_status_echarts_bar = ref(false);

	// 查询微网电量曲线
	const getEchartsBar = () => {
		let time_judge = timeRange(
			form_state_echarts_bar.time[0],
			form_state_echarts_bar.time[1],
			time_picker.value
		);

		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}
		let time_type_current = time_picker.value;

		let time_result = handleTimeParams(
			form_state_echarts_bar.time[0],
			form_state_echarts_bar.time[1],
			time_type_current
		);

		let startTime = time_result.startTime;
		let endTime = time_result.endTime;

		let params = {
			dateParticle: Number(form_state_echarts_bar.time_type), //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: project_id.value,
			startTime
		};
		params.photovoltaicIntoStorageFlag = cornerDisplay.value ? 1 : 0;
		form_state_echarts_bar.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_bar.compareStartTime)
					.startOf(time_type_current)
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;

		cnStatPositiveAndReverseActivePower(params)
			.then((data) => {
				if (data) {
					handleDataEchartsBar(data.statisticList);
				} else {
					hasData2.value = false;
				}
			})
			.catch((error) => {
				console.log(error);
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	// const handleDataEchartsBar = (data) => {
	//   let x_axis = []
	//   let x_axis_fan = []
	//   let y_axis = []
	//   let y_axis_fan = []
	//   for (let i = 0; i < data.length; i++) {
	//     // 正向有功和反向有功的处理
	//     let item_i = data[i]
	//     let name_i = data[i].paramName

	//     for (let j = 0; j < item_i.elementList.length; j++) {
	//       // 处理元素列表的数据
	//       let name_j = item_i.elementList[j].elementCodeDesc
	//       if (j == 0 && item_i.elementList[j].detailList.length > 0) {
	//         // 横坐标仅执行一次就行
	//         x_axis = handleXAxisTime(item_i.elementList[j].detailList)
	//         x_axis_fan = handleXAxisTime(item_i.compareElementList[j].detailList)
	//       }
	//       let value_list = []
	//       let value_list_fan = []
	//       for (let k = 0; k < item_i.elementList[j].detailList.length; k++) {
	//         // 具体的时间值
	//         value_list.push(item_i.elementList[j].detailList[k].value)
	//         value_list_fan.push(item_i.compareElementList[j].detailList[k].value)
	//         // value_list.push(Math.ceil(200 * Math.random()))
	//       }
	//       y_axis.push({
	//         name: name_i + '-' + name_j,
	//         value_list: value_list
	//       })
	//       y_axis_fan.push({
	//         name: name_i + '-' + name_j + '对比',
	//         value_list: value_list_fan
	//       })
	//     }
	//   }
	//   yaxis_data_echarts_bar.value = y_axis
	//   yaxis_data_echarts_bar_constract.value = y_axis_fan
	//   xaxis_data_echarts_bar.value = x_axis
	//   xaxis_data_echarts_bar_constract.value = x_axis_fan
	//   drawMapEchartsBar()
	// }

	const handleDataEchartsBar = (data) => {
		// console.log(data, 'data');

		let x_axis = [];
		let x_axis_fan = [];
		let y_axis = [];
		let y_axis_fan = [];

		for (let i = 0; i < data.length; i++) {
			// 正向有功和反向有功的处理
			let item_i = data[i];
			// let name_i = data[i].paramName

			for (let j = 0; j < item_i.elementList.length; j++) {
				// 处理元素列表的数据
				// let name_j = item_i.elementList[j].elementCodeDesc
				if (j == 0 && item_i.elementList[j].detailList.length > 0) {
					// 横坐标仅执行一次就行
					x_axis = handleXAxisTime(item_i.elementList[j].detailList, time_picker.value);
					if (powerContrast.value) {
						x_axis_fan = handleXAxisTime(
							item_i.compareElementList[j].detailList,
							time_picker.value
						);
					}
				}
				let value_list = [];
				let value_list_fan = [];
				for (let k = 0; k < item_i.elementList[j].detailList.length; k++) {
					// 具体的时间值
					value_list.push(item_i.elementList[j].detailList[k].value);
					if (powerContrast.value) {
						value_list_fan.push(item_i.compareElementList[j].detailList[k].value);
					}
				}
				let lengendName = '';
				if (data.length == 2) {
					if (item_i.chargeFlag == 1) {
						lengendName = '充电量';
					} else {
						lengendName = '放电量';
					}
				} else {
					if (item_i.paramName == '余光入储') {
						lengendName = '余光入储电量';
					} else if (item_i.paramName == '电网充电') {
						lengendName = '电网充电量';
					} else {
						lengendName = '放电量';
					}
				}

				y_axis.push({ name: lengendName, value_list: value_list });
				y_axis_fan.push({ name: lengendName + '-对比', value_list: value_list_fan });
			}
		}

		console.log(y_axis, 'y_axis');
		// console.log(y_axis_fan, 'y_axis_fan');

		if (y_axis.length == 2) {
			let order = ['充电量', '放电量'];
			let orderContrast = ['充电量-对比', '放电量-对比'];

			y_axis = sortByOrder(y_axis, order);
			y_axis_fan = sortByOrder(y_axis_fan, orderContrast);
		}

		if (y_axis.length == 3) {
			let order = ['电网充电量', '余光入储电量', '放电量'];
			let orderContrast = ['电网充电量-对比', '余光入储电量-对比', '放电量-对比'];

			y_axis = sortByOrder(y_axis, order);
			y_axis_fan = sortByOrder(y_axis_fan, orderContrast);
		}

		yaxis_data_echarts_bar.value = y_axis;
		yaxis_data_echarts_bar_constract.value = y_axis_fan;
		xaxis_data_echarts_bar.value = x_axis;
		xaxis_data_echarts_bar_constract.value = x_axis_fan;

		// console.log(x_axis, 'x_axis');
		// console.log(y_axis, 'y_axis');

		drawMapEchartsBar();
	};

	function sortByOrder(data, order) {
		// 创建一个映射表，将每个 name 映射到其在 order 中的索引
		const orderMap = {};
		order.forEach((item, index) => {
			orderMap[item] = index;
		});

		// 使用 sort 方法根据 orderMap 中的索引排序
		return data.sort((a, b) => {
			return orderMap[a.name] - orderMap[b.name];
		});
	}

	const yaxis_data_echarts_bar = ref([]);
	const yaxis_data_echarts_bar_constract = ref([]);
	const unit_echarts_bar = ref('kWh');
	const xaxis_data_echarts_bar = ref([]);
	const xaxis_data_echarts_bar_constract = ref([]);
	const my_chart_bar = ref();

	const drawMapEchartsBar = () => {
		// console.log(yaxis_data_echarts_bar.value, '数据');

		let result_option = [];
		let colorList = [];
		if (cornerDisplay.value) {
			colorList = ['#03E3A1', '#2DCDFC', '#1181FD'];
		} else {
			colorList = ['#03E3A1', '#1181FD'];
		}

		if (powerContrast.value) {
			result_option = handleBarMapSelection(
				xaxis_data_echarts_bar.value,
				xaxis_data_echarts_bar_constract.value,
				yaxis_data_echarts_bar.value,
				yaxis_data_echarts_bar_constract.value,
				unit_echarts_bar.value,
				colorList
			);
		} else {
			result_option = handleBarMapSelection(
				xaxis_data_echarts_bar.value,
				'',
				yaxis_data_echarts_bar.value,
				[],
				unit_echarts_bar.value,
				colorList
			);
		}

		if (cornerDisplay.value) {
			for (let i = 0; i < result_option.series.length; i++) {
				if (
					result_option.series[i].name == '余光入储电量' ||
					result_option.series[i].name == '电网充电量'
				) {
					result_option.series[i].stack = 'Ad';
				}
			}
		} else {
			// result_option.legend.data = ['充电量', '放电量'];
		}

		//   result_option.series.sort((a, b) => {
		//     const order = ['充电量', '放电量'];
		//     return order.indexOf(a.name) - order.indexOf(b.name);
		// });

		// console.log(result_option.series, 'result_option');
		if (my_chart_bar.value) {
			my_chart_bar.value.dispose();
		}
		if (document.getElementById('echarts_bar_id')) {
			my_chart_bar.value = markRaw(echarts.init(document.getElementById('echarts_bar_id')));
			my_chart_bar.value.setOption(result_option);
		}
	};

	// ***********************************************************************************************************
	//                                              储能充放电量                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                             储能充放电导出                                                //
	// ***********************************************************************************************************
	const export_loading_2 = ref(false);
	const exportFn_2 = () => {
		let time_judge = timeRange(
			form_state_echarts_bar.time[0],
			form_state_echarts_bar.time[1],
			time_picker.value
		);

		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}
		let time_type_current = time_picker.value;

		let time_result = handleTimeParams(
			form_state_echarts_bar.time[0],
			form_state_echarts_bar.time[1],
			time_type_current
		);
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;
		let params = {
			dateParticle: Number(form_state_echarts_bar.time_type), //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: Number(project_id.value),
			startTime
		};
		params.photovoltaicIntoStorageFlag = cornerDisplay.value ? 1 : 0;
		form_state_echarts_bar.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_bar.compareStartTime)
					.startOf(time_type_current)
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;
		export_loading_2.value = true;
		cnExportPositiveAndReverseActivePower(params)
			.then((data) => {
				let timeFormat = 'YYYYMMDD';
				if (time_picker.value == 'month') {
					timeFormat = 'YYYYMM';
				} else if (time_picker.value == 'year') {
					timeFormat = 'YYYY';
				}
				let templateName =
					dayjs(startTime).format(timeFormat) +
					'-' +
					dayjs(endTime).format(timeFormat) +
					'储能充放电量报表';
				if (!data) {
					return;
				}
				const url = window.URL.createObjectURL(new Blob([data], { type: 'application/zip' }));
				let link = document.getElementById('download');
				link.href = url;
				link.setAttribute('download', `${templateName}.xlsx`);
				link.click();
				export_loading_2.value = false;
			})
			.catch((error) => {
				console.log(error);
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
				export_loading_2.value = false;
			});
	};
	// ***********************************************************************************************************
	//                                              储能充放电导出                                                //
	// ***********************************************************************************************************

	// 全页面查询
	const searchAll = () => {
		// 查询微网功率
		getEchartsLine();
		// 查询需量和储能功率
		getEchartsLine_1();
		// 查询微网电量
		getEchartsBar();
	};

	// 判断项目是否配置余光入储
	const hasResignal = ref(false);

	onMounted(() => {
		// 项目id
		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			project_id.value = project_mess.project_id;
		} else {
			message.error('未选择项目，请重新选择！');
		}
		getStrategyByProjectId(project_id.value).then((data) => {
			if (JSON.stringify(data) !== '{}') {
				if (data.economicStrategy.includes('RESIGNAL_STORAGE')) {
					hasResignal.value = true;
				}
			}
		});
		drawMapEchartsBar();
		searchAll();
	});
</script>
<style lang="less" scoped>
	.main_contain_scroll {
		background-color: transparent !important;
		padding: 0 !important;
	}

	:deep(.ant-picker-dropdown) {
		left: 0px !important;
		top: 38px !important;
	}

	.map_item {
		height: 598px;
		padding: 32px;
		background-color: #ffffff;
		margin-bottom: 32px;
		box-sizing: border-box;

		.map_title {
			height: 18px;
			line-height: 18px;
			font-family: SourceHanSansCN-Regular;
			font-weight: bold;
			font-size: 18px;
			color: #092649;
		}

		.params_list {
			height: 36px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			margin-top: 32px;
			margin-bottom: 20px;
		}

		&:last-child {
			margin-bottom: 0px;
		}
	}

	.map_line_contain {
		height: 426px;
		width: 100%;
		position: relative;

		.unit_mess {
			color: #949aa6;
			font-size: 14px;
			font-family: 'shsr';
			position: absolute;
			top: 5px;
		}

		.unit_mess1 {
			color: #949aa6;
			font-size: 14px;
			font-family: 'shsr';
			position: absolute;
			top: 5px;
			right: 25px;
		}

		.map_line {
			height: 100%;
			width: 100%;
		}

		.no_data {
			position: absolute;
			top: -5%;
			left: 0;
			right: 0;
			bottom: 0;
			padding-top: 0px;
		}
	}

	.params_list_right {
		display: flex;
	}
</style>
