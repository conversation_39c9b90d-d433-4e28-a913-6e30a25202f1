<template>
  <div class="page_all analysis_all">
    <MenuThird :third_menu="third_menu"> </MenuThird>
    <RouterView></RouterView>
  </div>
</template>

<script setup>
import {ref} from 'vue'
import MenuThird from '/components/MenuThird.vue'
const third_menu = ref([
  {
    name: '运行总览',
    route_name: 'analysis_all',
    url: '/analysis/analysis_all'
  },
  {
    name: '光伏分析',
    route_name: 'analysis_gf',
    url: '/analysis/analysis_gf'
  },
  {
    name: '储能分析',
    route_name: 'analysis_cn',
    url: '/analysis/analysis_cn'
  },
  {
    name: '充电桩分析',
    route_name: 'analysis_cdz',
    url: '/analysis/analysis_cdz'
  },
  {
    name: '用能分析',
    route_name: 'analysis_yn',
    url: '/analysis/analysis_yn'
  },
  {
    name: '预测分析',
    route_name: 'analysis_yc',
    url: '/analysis/analysis_yc'
  }
]) 
</script>
<style lang="less" scoped>
.analysis_all {
  padding-top: 28px;
  box-sizing: border-box;
  box-sizing: border-box;
}
</style>
