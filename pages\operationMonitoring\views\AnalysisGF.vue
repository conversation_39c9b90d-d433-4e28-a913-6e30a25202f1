<template>
	<div class="main_contain main_contain_thirdMenu">
		<a
			href="#"
			id="download"
			style="position: absolute; top: -1000px; right: -1000px"
		></a>
		<div class="main_contain_scroll">
			<div class="map_item">
				<div class="map_title">光伏发电功率曲线</div>
				<div class="params_list">
					<div class="params_list_left">
						<a-form
							id="xuliang"
							:model="powerFormState"
							name="xuliang"
							layout="inline"
							autocomplete="off"
						>
							<a-form-item
								label="时间选择"
								name="time"
							>
								<a-range-picker
									name="time_gf"
									:status="time_status_echarts_line ? 'error' : ''"
									:allowClear="false"
									v-model:value="form_state_echarts_line.time"
									:getPopupContainer="(e) => e.parentNode"
									style="width: 300px"
									:disabledDate="
										(current) => {
											return current && current > dayjs().endOf('day');
										}
									"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/clock.png"
											alt="#"
										/>
									</template>
								</a-range-picker>
							</a-form-item>
							<a-form-item>
								<a-checkbox
									v-model:checked="isContrast"
									@change="isContrastChange"
									style="font-size: 16px"
								>
									对比
								</a-checkbox>
							</a-form-item>
							<a-form-item
								label="添加对比"
								name="compareStartTime"
								v-if="isContrast"
							>
								<a-date-picker
									style="width: 200px"
									:allowClear="false"
									:disabled-date="
										(current) => {
											return current && current > dayjs().endOf('day');
										}
									"
									v-model:value="form_state_echarts_line.compareStartTime"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/picker_month.png"
											alt="#"
										/>
									</template>
								</a-date-picker>
							</a-form-item>

							<a-form-item>
								<a-button
									type="primary"
									@click="getEchartsLine"
								>
									查询
								</a-button>
							</a-form-item>
							<a-form-item>
								<a-button @click="resetGfPowerInput">重置</a-button>
							</a-form-item>
						</a-form>
					</div>
					<div class="params_list_right">
						<a-button
							type="primary"
							@click="skipUnnormal"
							style="margin-right: 16px; display: flex"
						>
							<div class="icon_button">
								<img
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/unnormal_setting.png"
									alt=""
								/>
								异常规则配置
							</div>
						</a-button>
						<a-button
							@click="exportFn"
							:disabled="!hasData"
							:loading="export_loading"
							style="display: flex"
							@mouseenter="hover1 = true"
							@mouseleave="hover1 = false"
						>
							<div class="icon_button">
								<img
									v-if="hover1 && hasData"
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_hover.png"
									alt=""
								/>
								<img
									v-else
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_dark.png"
									alt=""
								/>
								<span>导出</span>
							</div>
						</a-button>
					</div>
				</div>
				<div class="map_line_contain">
					<!-- <span class="unit_mess">kW</span> -->
					<div
						v-if="hasData"
						class="map_line"
						id="echarts_line_id"
					></div>
					<a-empty
						v-if="!hasData"
						style="margin: 0 auto; position: relative; top: 30%"
					/>
				</div>
			</div>

			<div class="map_item">
				<div class="map_title">光伏发电量</div>
				<div class="params_list">
					<div class="params_list_left">
						<a-form
							id="horizontal_login"
							:model="form_state_echarts_bar"
							name="horizontal_login"
							layout="inline"
							autocomplete="off"
							@finish="onFinishElec"
						>
							<a-form-item
								label="粒度选择"
								name="time_type"
							>
								<a-select
									ref="select_time_type"
									:getPopupContainer="(e) => e.parentNode"
									v-model:value="form_state_echarts_bar.time_type"
									style="width: 120px"
								>
									<a-select-option value="1">日</a-select-option>
									<a-select-option value="2">月</a-select-option>
									<a-select-option value="3">年</a-select-option>
								</a-select>
							</a-form-item>
							<a-form-item
								label="时间选择"
								name="time"
							>
								<a-range-picker
									:allowClear="false"
									name="time_gf_ele"
									:status="time_status_echarts_bar ? 'error' : ''"
									v-model:value="form_state_echarts_bar.time"
									:getPopupContainer="(e) => e.parentNode"
									style="width: 300px"
									:disabledDate="
										(current) => {
											return current && current > dayjs().endOf(time_picker);
										}
									"
									:picker="time_picker"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/clock.png"
											alt="#"
										/>
									</template>
								</a-range-picker>
							</a-form-item>
							<a-form-item>
								<a-checkbox
									v-model:checked="powerContrast"
									@change="powerContrastChange"
									style="font-size: 16px"
								>
									对比
								</a-checkbox>
							</a-form-item>
							<a-form-item
								label="添加对比"
								name="compareStartTime"
								v-if="powerContrast"
							>
								<a-date-picker
									style="width: 200px"
									:disabled-date="
										(current) => {
											return current && current > dayjs().endOf('day');
										}
									"
									:allowClear="false"
									:picker="time_picker"
									v-model:value="form_state_echarts_bar.compareStartTime"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/picker_month.png"
											alt="#"
										/>
									</template>
								</a-date-picker>
							</a-form-item>

							<a-form-item>
								<a-button
									type="primary"
									@click="getEchartsBar"
								>
									查询
								</a-button>
							</a-form-item>
							<a-form-item>
								<a-button @click="resetInputEchartsBar">重置</a-button>
							</a-form-item>
						</a-form>
					</div>
					<div class="params_list_right">
						<a-button
							type="primary"
							@click="skipUnnormal"
							style="margin-right: 16px; display: flex"
						>
							<div class="icon_button">
								<img
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/unnormal_setting.png"
									alt=""
								/>
								<span>异常规则配置</span>
							</div>
						</a-button>
						<a-button
							:loading="export_loading_1"
							@click="exportFn_1"
							:disabled="!hasData1"
							style="display: flex"
							@mouseenter="hover2 = true"
							@mouseleave="hover2 = false"
						>
							<div class="icon_button">
								<img
									v-if="hover2 && hasData1"
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_hover.png"
									alt=""
								/>
								<img
									v-else
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_dark.png"
									alt=""
								/>
								<span>导出</span>
							</div>
						</a-button>
					</div>
				</div>
				<div class="map_line_contain">
					<!-- <span class="unit_mess">kWh</span> -->
					<div
						v-if="hasData1"
						class="map_line"
						id="elec_id"
					></div>
					<a-empty
						v-if="!hasData1"
						style="margin: 0 auto; position: relative; top: 30%"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, reactive, onMounted, markRaw, watch } from 'vue';
	import * as echarts from 'echarts';
	import { colorList, hexToRgba, colorList_mix } from '/assets/js/echartsConfig.js';
	import {
		handleTime,
		timeRange,
		handleTimeParams,
		handleLineMapOption,
		handleLineMapSelection,
		handleXAxisTime,
		init_x_y,
		handleEchartsLineBg,
		handleMax
	} from '/assets/js/commonTool.js';
	// import zhCN from 'ant-design-vue/es/locale/zh_CN'
	import {
		getStandardValueByElement,
		pvStatMinuteActivePowerHttp,
		pvStatPositiveAndReverseActivePower,
		pvExportMinuteActivePower,
		pvExportPositiveActivePower
	} from './api.js';
	import dayjs from 'dayjs';
	import { message } from 'ant-design-vue';
	import { eventBus } from '/assets/js/eventBus'; // 导入事件总线实例
	// ***********************************************************************************************************
	//                                                   全局参数配置                                            //
	// ***********************************************************************************************************

	const project_id = ref(null);
	const hover1 = ref(false);
	const hover2 = ref(false);
	const skipUnnormal = () => {
		eventBus.emit('menu-change', [1, 2, '/pages/operationMonitoring/#/ruleSetting']); // 触发事件
	};
	// ***********************************************************************************************************
	//                                                   全局参数配置                                            //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                              光伏发电功率                                            //
	// ***********************************************************************************************************

	const hasData = ref(true);
	// 时间参数
	const form_state_echarts_line = reactive({
		time: [dayjs().startOf('day'), dayjs()],
		compareStartTime: null
	});

	const powerContrastChange = () => {
		if (powerContrast.value) {
			let type = 'day';
			if (time_picker.value != 'date') {
				type = time_picker.value;
			}
			form_state_echarts_bar.compareStartTime = dayjs().subtract(1, type);
		} else {
			form_state_echarts_bar.compareStartTime = null;
		}
	};

	const isContrastChange = () => {
		if (isContrast.value) {
			form_state_echarts_line.compareStartTime = dayjs().subtract(1, 'day');
		} else {
			form_state_echarts_line.compareStartTime = null;
		}
	};

	// 重置查询条件
	const resetGfPowerInput = () => {
		Object.assign(form_state_echarts_line, {
			time: [dayjs().startOf('day'), dayjs()],
			compareStartTime: null
		});
		isContrast.value = false;
		getEchartsLine();
	};

	// 时间插件
	const time_status_echarts_line = ref(false);

	// 查询
	const xaxis_data_echarts_line = ref([]);
	const yaxis_data_echarts_line = ref([]);
	const xaxis_data_echarts_line_contrast = ref([]);
	const yaxis_data_echarts_line_contrast = ref([]);
	const timeList = ref([]);
	const unit_echarts_line = ref('kW');
	const my_chart_line = ref();
	const capacity_echarts_line = ref(null);
	// 根据条件查询需量和储能功率
	const getEchartsLine = () => {
		// if (form_state_echarts_line.time[1].diff(form_state_echarts_line.time[0], 'days') > 30) {
		//   message.error('最长可选的时间范围为30天！，请修改后再查询')
		//   return
		// }
		let time_judge = timeRange(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'date'
		);

		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}

		let time_result = handleTimeParams(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'day'
		);
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;

		let params = {
			dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: project_id.value,
			startTime
		};
		form_state_echarts_line.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_line.compareStartTime)
					.startOf('day')
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;

		// statMinuteRealDemandAndActivePowerHttp(params)
		// .then((data) => {
		//   handleDataGfPower(data.statisticList)
		// })
		// .catch((error) => {
		//   message.error(error.message)
		// })
		let params1 = { projectId: project_id.value, elementCode: 'GF' };

		// let params1 = {
		//   endTime,
		//   projectId: project_id.value,
		//   startTime
		// }

		// 功率曲线

		Promise.all([
			pvStatMinuteActivePowerHttp(params), // 功率曲线
			getStandardValueByElement(params1) // 参考线
		])
			.then(([data1, data2]) => {
				if (data2) {
					capacity_echarts_line.value = data2.capacity;
				} else {
					capacity_echarts_line.value = 0;
				}
				if (data1) {
					handleDataGfPower(data1.statisticList, data1.timeList);
				} else {
					hasData.value = false;
				}
			})
			.catch((error) => {
				console.log(error);

				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	const handleDataGfPower = (data, list) => {
		// let x_axis = []
		// let y_axis = []

		if (data[0].elementList.length > 0) {
			let result = init_x_y(data[0].elementList);
			result.y_axis[0].name = '光伏总发电功率';
			xaxis_data_echarts_line.value = result.x_axis;
			yaxis_data_echarts_line.value = result.y_axis;
		}
		if (data[0].compareElementList.length > 0) {
			let result_contrast = init_x_y(data[0].compareElementList);
			result_contrast.y_axis[0].name = '光伏总发电功率-';
			xaxis_data_echarts_line_contrast.value = result_contrast.x_axis;
			yaxis_data_echarts_line_contrast.value = result_contrast.y_axis;
		}
		timeList.value = list;
		drawMapgf();
	};

	const drawMapgf = () => {
		let markArea = handleEchartsLineBg(timeList.value, xaxis_data_echarts_line.value);
		let option_result = {};
		if (my_chart_line.value) {
			my_chart_line.value.dispose();
		}
		if (isContrast.value) {
			option_result = handleLineMapSelection(
				xaxis_data_echarts_line.value,
				yaxis_data_echarts_line.value,
				xaxis_data_echarts_line_contrast.value,
				yaxis_data_echarts_line_contrast.value,
				unit_echarts_line.value
			);
		} else {
			option_result = handleLineMapSelection(
				xaxis_data_echarts_line.value,
				yaxis_data_echarts_line.value,
				'',
				[],
				unit_echarts_line.value
			);
		}

		// 背景
		option_result.series[0].markArea = markArea;
		// 增加一个参考线
		// //console.log(capacity_echarts_line.value)
		option_result.series[0].markLine = {
			silent: false,
			lineStyle: { color: 'red' },
			label: {
				position: 'middle',
				formatter: [`{a|额定功率:${capacity_echarts_line.value}}`].join('\n'),
				rich: { a: { color: 'red', lineHeight: 10 } },
				color: 'red'
			},

			data: [{ yAxis: capacity_echarts_line.value }]
		};
		// 判断参考线的值和最大值之间的大小
		let max = Math.max(option_result.yAxis[0].max, capacity_echarts_line.value);
		max = handleMax(max);

		option_result.yAxis[0].max = max;
		if (option_result.series[0].data.every((item) => item === null)) {
			option_result.yAxis[0].min = 0;
		}
		// //console.log(option_result, 'option_result');
		// delete option_result.yAxis[0].max
		if (document.getElementById('echarts_line_id')) {
			my_chart_line.value = markRaw(echarts.init(document.getElementById('echarts_line_id')));
			my_chart_line.value.setOption(option_result);
		}
	};

	// ***********************************************************************************************************
	//                                              光伏发电功率                                            //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                              光伏发电功率导出                                                //
	// ***********************************************************************************************************
	const export_loading = ref(false);

	const exportFn = () => {
		let time_judge = timeRange(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'date'
		);

		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}
		let time_result = handleTimeParams(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'day'
		);
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;
		let params = {
			dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: Number(project_id.value),
			startTime
		};
		form_state_echarts_line.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_line.compareStartTime)
					.startOf('day')
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;
		export_loading.value = true;
		pvExportMinuteActivePower(params)
			.then((data) => {
				let templateName =
					dayjs(startTime).format('YYYYMMDD') +
					'-' +
					dayjs(endTime).format('YYYYMMDD') +
					'光伏发电功率曲线报表';
				if (!data) {
					return;
				}
				const url = window.URL.createObjectURL(new Blob([data], { type: 'application/zip' }));
				let link = document.getElementById('download');
				link.href = url;
				link.setAttribute('download', `${templateName}.xlsx`);
				link.click();
				export_loading.value = false;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
				export_loading.value = false;
			});
	};
	// ***********************************************************************************************************
	//                                              光伏发电功率导出                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                              光伏发电量                                                //
	// ***********************************************************************************************************
	const powerContrast = ref(false);
	const isContrast = ref(false);
	const hasData1 = ref(true);
	// 时间参数
	const time_picker = ref('date');
	const form_state_echarts_bar = reactive({
		time: [dayjs().startOf('day'), dayjs()],
		compareStartTime: null,
		time_type: '1'
	});
	watch(
		() => form_state_echarts_bar.time_type,
		(newVal) => {
			if (newVal == 1) {
				time_picker.value = 'date';
			} else if (newVal == 2) {
				time_picker.value = 'month';
			} else if (newVal == 3) {
				time_picker.value = 'year';
			}
			powerContrastChange();
		},
		{ deep: true }
	);

	// 重置查询条件
	const resetInputEchartsBar = () => {
		Object.assign(form_state_echarts_bar, {
			time: [dayjs().startOf('day'), dayjs()],
			compareStartTime: null,
			time_type: '1'
		});
		powerContrast.value = false;
		time_picker.value = 'date';
		getEchartsBar();
	};

	// 时间插件
	const time_status_echarts_bar = ref(false);

	// 查询光伏发电量
	const getEchartsBar = () => {
		let time_judge = timeRange(
			form_state_echarts_bar.time[0],
			form_state_echarts_bar.time[1],
			time_picker.value
		);
		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}
		let time_type_current = time_picker.value;

		let time_result = handleTimeParams(
			form_state_echarts_bar.time[0],
			form_state_echarts_bar.time[1],
			time_type_current
		);
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;

		let params = {
			dateParticle: Number(form_state_echarts_bar.time_type), //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: project_id.value,
			startTime
		};
		form_state_echarts_bar.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_bar.compareStartTime)
					.startOf(time_type_current)
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;

		pvStatPositiveAndReverseActivePower(params)
			.then((data) => {
				if (data) {
					handleDataEchartsBar(data.statisticList);
				} else {
					hasData1.value = false;
				}
			})
			.catch((error) => {
				console.log(error);
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	const handleDataEchartsBar = (data) => {
		let x_axis = [];
		let x_axis_fan = [];
		let y_axis = [];
		let y_axis_fan = [];
		for (let i = 0; i < data.length; i++) {
			// 正向有功和反向有功的处理
			let item_i = data[i];
			// let name_i = data[i].paramName;

			for (let j = 0; j < item_i.elementList.length; j++) {
				// 处理元素列表的数据
				// let name_j = item_i.elementList[j].elementCodeDesc;
				if (j == 0 && item_i.elementList[j].detailList.length > 0) {
					// 仅执行一次就行
					x_axis = handleXAxisTime(item_i.elementList[j].detailList, time_picker.value);
					if (powerContrast.value) {
						x_axis_fan = handleXAxisTime(
							item_i.compareElementList[j].detailList,
							time_picker.value
						);
					}
				}
				let value_list = [];
				let value_list_fan = [];
				for (let k = 0; k < item_i.elementList[j].detailList.length; k++) {
					// 具体的时间值
					value_list.push(item_i.elementList[j].detailList[k].value);
				}
				if (powerContrast.value) {
					for (let k = 0; k < item_i.compareElementList[j].detailList.length; k++) {
						value_list_fan.push(item_i.compareElementList[j].detailList[k].value);
					}
				}
				y_axis.push({ name: '光伏发电量', value_list: value_list });
				y_axis_fan.push({ name: '光伏发电量-对比', value_list: value_list_fan });
			}
		}
		yaxis_data_echarts_bar.value = y_axis;
		yaxis_data_echarts_bar_constract.value = y_axis_fan;
		xaxis_data_echarts_bar.value = x_axis;
		xaxis_data_echarts_bar_constract.value = x_axis_fan;
		drawMapEchartsBar();
	};

	const yaxis_data_echarts_bar = ref([]);
	const yaxis_data_echarts_bar_constract = ref([]);
	const unit_echarts_bar = ref('kWh');
	const xaxis_data_echarts_bar = ref([]);
	const xaxis_data_echarts_bar_constract = ref([]);
	const my_chart_bar = ref();

	const drawMapEchartsBar = () => {
		////console.log(yaxis_data_echarts_bar.value)
		////console.log(xaxis_data_echarts_bar.value)
		let legend_data = [];
		let series_list = [];

		for (let j = 0; j < yaxis_data_echarts_bar.value.length; j++) {
			legend_data.push(yaxis_data_echarts_bar.value[j].name);
			if (powerContrast.value) {
				legend_data.push(yaxis_data_echarts_bar_constract.value[j].name);
			}
			if (powerContrast.value) {
				series_list.push(
					{
						name: yaxis_data_echarts_bar.value[j].name,
						data: JSON.parse(JSON.stringify(yaxis_data_echarts_bar.value[j].value_list)),
						type: 'bar',
						itemStyle: { color: hexToRgba(colorList[j], 1) },
						barMaxWidth: 18,
						barMinWidth: 6,
						emphasis: { disabled: true, focus: 'none' }
						// xAxisIndex: 0,
					},
					{
						name: yaxis_data_echarts_bar_constract.value[j].name,
						data: JSON.parse(JSON.stringify(yaxis_data_echarts_bar_constract.value[j].value_list)),
						type: 'bar',
						itemStyle: { color: hexToRgba('#cadbee', 1) },
						barMaxWidth: 18,
						barMinWidth: 6,
						emphasis: { disabled: true, focus: 'none' }
						// xAxisIndex: 1,
					}
				);
			} else {
				series_list.push({
					name: yaxis_data_echarts_bar.value[j].name,
					data: JSON.parse(JSON.stringify(yaxis_data_echarts_bar.value[j].value_list)),
					type: 'bar',
					itemStyle: { color: hexToRgba(colorList[j], 1) },
					barMaxWidth: 18,
					barMinWidth: 6,
					emphasis: { disabled: true, focus: 'none' }
					// xAxisIndex: 0,
				});
			}
		}

		if (my_chart_bar.value) {
			my_chart_bar.value.dispose();
		}
		let option = {
			backgroundColor: 'transparent',
			// color: colorList,
			legend: {
				show: true,
				type: 'scroll',
				center: true,
				top: 0,
				width: 920,
				data: legend_data,
				itemWidth: 12, // 设置图例的宽度
				itemHeight: 12, // 设置图例的高度
				itemGap: 80, //图例之间的距离
				align: 'auto',
				textStyle: { color: '#092649' }
			},
			dataZoom: [
				{ start: 0, end: 100, bottom: 20, height: 30 }
				// {
				//   start: 0,
				//   end: 100
				// }
			],
			tooltip: {
				trigger: 'axis',
				formatter: function (params) {
					let html = '';

					params.forEach((v) => {
						// //console.log(v);
						// //console.log(xaxis_data_echarts_bar_constract);
						let time = xaxis_data_echarts_bar.value[v.dataIndex];
						if (v.seriesName.includes('对比')) {
							time = xaxis_data_echarts_bar_constract.value[v.dataIndex];
						}

						let value = v.value;
						// if (value === null) {
						//   value = '-'
						// }
						if (value != 0 && !value) {
							value = '-';
						} else {
							value = Number(parseFloat(v.value).toFixed(2));
						}
						html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                  <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
										v.color
									};"></span>
                  ${v.seriesName} : ${time} <span style="color:${
										v.color
									};font-weight:600;font-size: 14px"> ${value} </span>   ${unit_echarts_bar.value}
                  `;
					});
					return html;
				},
				extraCssText:
					'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;'
			},
			grid: {
				top: 50,
				containLabel: true, //区域是否包含坐标轴标签
				left: 40,
				right: 0,
				bottom: 60
			},
			xAxis: [
				{
					show: true,
					type: 'category',
					axisTick: { show: false },
					axisLine: { show: true, lineStyle: { type: 'dashed', color: '#DFE3EC' } },
					splitLine: {
						show: true,
						lineStyle: { type: 'dashed', color: ['transparent'], width: 1 }
					},
					axisLabel: {
						inside: false,
						color: '#9CA2AF',
						fontWeight: '100',
						fontSize: '14',
						fontFamily: 'shsr',
						lineHeight: 14,
						margin: 10
					},

					data: xaxis_data_echarts_bar.value
				},
				// 对比的值坐标轴
				{
					show: true,
					// gridIndex: 1,
					type: 'category',
					axisTick: { show: false },
					axisLine: { show: true, lineStyle: { type: 'dashed', color: '#DFE3EC' } },
					splitLine: {
						show: true,
						lineStyle: { type: 'dashed', color: ['transparent'], width: 1 }
					},
					axisLabel: {
						inside: false,
						color: '#9CA2AF',
						fontWeight: '100',
						fontSize: '14',
						fontFamily: 'shsr',
						lineHeight: 14,
						margin: 10
					},

					data: xaxis_data_echarts_bar_constract.value
				}
			],
			yAxis: [
				{
					type: 'value',
					name: 'kWh',
					offset: -2, //y轴坐标刻度偏移位置
					nameTextStyle: {
						color: '#949aa6',
						fontSize: 14,
						lineHeight: 40,
						fontWeight: 500,
						align: 'left',
						fontFamily: 'shsr',
						padding: [0, 0, 0, -40],
						width: 100
					},
					splitLine: { show: true, lineStyle: { type: 'dashed', color: '#DFE3EC', width: 1 } },
					axisLine: { show: true, lineStyle: { type: 'dashed', color: 'transparent' } },
					axisLabel: {
						inside: false,
						color: '#9CA2AF',
						fontWeight: '100',
						fontSize: '14',
						fontFamily: 'shsr',
						lineHeight: 14,
						margin: 10
					},
					axisTick: { show: false }
				}
			],
			series: series_list
		};
		if (document.getElementById('elec_id')) {
			my_chart_bar.value = markRaw(echarts.init(document.getElementById('elec_id')));
			my_chart_bar.value.setOption(option);
		}
	};

	// ***********************************************************************************************************
	//                                              光伏发电量                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                              光伏发电量导出                                                //
	// ***********************************************************************************************************
	const export_loading_1 = ref(false);
	const exportFn_1 = () => {
		let time_judge = timeRange(
			form_state_echarts_bar.time[0],
			form_state_echarts_bar.time[1],
			time_picker.value
		);
		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}
		let time_type_current = time_picker.value;

		let time_result = handleTimeParams(
			form_state_echarts_bar.time[0],
			form_state_echarts_bar.time[1],
			time_type_current
		);
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;

		let params = {
			dateParticle: Number(form_state_echarts_bar.time_type), //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: Number(project_id.value),
			startTime
		};
		form_state_echarts_bar.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_bar.compareStartTime)
					.startOf(time_type_current)
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;
		export_loading_1.value = true;
		pvExportPositiveActivePower(params)
			.then((data) => {
				let timeFormat = 'YYYYMMDD';
				if (time_picker.value == 'month') {
					timeFormat = 'YYYYMM';
				} else if (time_picker.value == 'year') {
					timeFormat = 'YYYY';
				}
				let templateName =
					dayjs(startTime).format(timeFormat) +
					'-' +
					dayjs(endTime).format(timeFormat) +
					'光伏发电量报表';
				if (!data) {
					return;
				}
				const url = window.URL.createObjectURL(new Blob([data], { type: 'application/zip' }));
				let link = document.getElementById('download');
				link.href = url;
				link.setAttribute('download', `${templateName}.xlsx`);
				link.click();
				export_loading_1.value = false;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
				export_loading_1.value = false;
			});
	};
	// ***********************************************************************************************************
	//                                              光伏发电量导出                                                //
	// ***********************************************************************************************************

	// 全页面查询
	const searchAll = () => {
		// 查询需量和储能功率
		getEchartsLine();
		// 查询微网电量
		getEchartsBar();
	};

	onMounted(() => {
		// 项目id
		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			project_id.value = project_mess.project_id;
		} else {
			message.error('未选择项目，请重新选择！');
		}
		//
		drawMapEchartsBar();
		searchAll();
	});
</script>
<style lang="less" scoped>
	.main_contain_scroll {
		background-color: transparent !important;
		padding: 0 !important;
	}

	:deep(.ant-picker-dropdown) {
		left: 0px !important;
		top: 38px !important;
	}

	.map_item {
		height: 598px;
		padding: 32px;
		background-color: #ffffff;
		margin-bottom: 32px;
		box-sizing: border-box;

		.map_title {
			height: 18px;
			line-height: 18px;
			font-family: SourceHanSansCN-Regular;
			font-weight: bold;
			font-size: 18px;
			color: #092649;
		}

		.params_list {
			height: 36px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			margin-top: 32px;
			margin-bottom: 20px;
		}

		&:last-child {
			margin-bottom: 0px;
		}
	}

	.map_line_contain {
		height: 426px;
		width: 100%;
		position: relative;

		.unit_mess {
			color: #949aa6;
			font-size: 14px;
			font-family: 'shsr';
			position: absolute;
			top: 5px;
		}

		.map_line {
			height: 100%;
			width: 100%;
		}

		.no_data {
			position: absolute;
			top: -5%;
			left: 0;
			right: 0;
			bottom: 0;
			padding-top: 0px;
		}
	}

	.params_list_right {
		display: flex;
	}
</style>
