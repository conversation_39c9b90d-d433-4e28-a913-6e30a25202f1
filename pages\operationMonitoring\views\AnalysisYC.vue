<template>
	<div class="main_contain main_contain_thirdMenu">
		<a
			href="#"
			id="download"
			style="position: absolute; top: -1000px; right: -1000px"
		></a>
		<div class="main_contain_scroll">
			<div class="map_item">
				<div
					class="map_title"
					v-if="selectShow == '2'"
				>
					计划曲线
				</div>
				<div
					class="map_title"
					v-else
				>
					预测曲线
				</div>
				<div class="params_list">
					<div class="params_list_left">
						<a-form
							:model="form_state_echarts_line"
							name="horizontal_login"
							layout="inline"
							autocomplete="off"
						>
							<a-form-item
								label="时间选择"
								name="time"
							>
								<a-range-picker
									:getPopupContainer="(e) => e.parentNode"
									:status="time_status_echarts_line ? 'error' : ''"
									:allowClear="false"
									v-model:value="form_state_echarts_line.time"
									style="width: 300px"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/clock.png"
											alt="#"
										/>
									</template>
								</a-range-picker>
							</a-form-item>

							<a-form-item>
								<a-button
									type="primary"
									@click="getEchartsLine"
									html-type="submit"
								>
									查询
								</a-button>
							</a-form-item>
							<a-form-item>
								<a-button @click="resetEchartsLineInput">重置</a-button>
							</a-form-item>
						</a-form>
					</div>
				</div>
				<div style="margin-bottom: 15px">
					<a-radio-group
						v-model:value="selectShow"
						button-style="solid"
						style="font-size: 16px"
						@change="getEchartsLine"
					>
						<a-radio-button value="2">储能计划功率</a-radio-button>
						<a-radio-button value="1">现货价格</a-radio-button>
					</a-radio-group>
				</div>
				<div class="map_line_contain">
					<!-- <span class="unit_mess">{{ unit_echarts_line }}</span> -->
					<div
						v-if="hasData"
						class="map_line"
						id="echarts_line_id"
					></div>
					<a-empty
						v-if="!hasData"
						style="margin: 0 auto; position: relative; top: 30%"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, reactive, onMounted, markRaw } from 'vue';
	import { message } from 'ant-design-vue';
	import * as echarts from 'echarts';
	import dayjs from 'dayjs';
	import {
		handleTimeParams,
		handleLineMapOptionSingal,
		handleEchartsLineBg,
		timeRange,
		handleXAxisTime
	} from '/assets/js/commonTool.js';
	import { statPrediction } from './api.js';

	const form_state_echarts_line = reactive({
		time: [dayjs().startOf('day'), dayjs().add(1, 'day')]
	});

	const project_id = ref(null);
	const selectShow = ref('2');
	const hasData = ref(true);

	const getEchartsLine = () => {
		let time_judge = timeRange(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'date'
		);

		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}

		let startTime = dayjs(form_state_echarts_line.time[0])
			.startOf('day')
			.format('YYYY-MM-DD HH:mm:ss');
		let endTime = dayjs(form_state_echarts_line.time[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss');

		let params = {
			endTime,
			predictionParam: Number(selectShow.value),
			projectId: project_id.value,
			startTime
		};

		statPrediction(params)
			.then((data) => {
				if (data) {
					handleDataEchartsLine(data);
				} else {
					hasData.value = false;
				}
			})
			.catch((error) => {
				// console.log(error, '222222222222');
				message.error(error.message);
			});
	};

	const init_x_y = (data) => {
		let x_axis = [];
		let y_axis = [];
		x_axis = handleXAxisTime(data.predictionList);

		let nameList = [];
		if (data.predictionParamName == '现货价格') {
			nameList = ['实际现货价格', '预测现货价格'];
		} else {
			nameList = ['储能实际总功率', '储能计划总功率'];
		}

		for (let j = 0; j < 2; j++) {
			let valueList = [];
			if (j == 1) {
				if (data.predictionList && data.predictionList.length > 0) {
					for (let i = 0; i < data.predictionList.length; i++) {
						valueList.push(data.predictionList[i].value);
					}
				}
			} else {
				if (data.actualList && data.actualList.length > 0) {
					for (let i = 0; i < data.actualList.length; i++) {
						valueList.push(data.actualList[i].value);
					}
				} else {
					for (let i = 0; i < x_axis.length; i++) {
						valueList.push(null);
					}
				}
			}

			y_axis.push({ name: nameList[j], valueList: valueList });
		}

		console.log(y_axis, 'y_axis');

		return { x_axis, y_axis };
	};

	// 绘图的基本数据
	const xaxis_data_echarts_line = ref([]);
	const yaxis_data_echarts_line = ref([]);
	const my_chart_line = ref();
	const unit_echarts_line = ref('kW');
	const colorList = ['#03E3A1', '#1181FD'];

	const handleDataEchartsLine = (data) => {
		let result = init_x_y(data);
		xaxis_data_echarts_line.value = result.x_axis;
		yaxis_data_echarts_line.value = result.y_axis;
		if (my_chart_line.value) {
			// 要在my_chart_line赋值之前释放值
			my_chart_line.value.dispose();
		}

		if (selectShow.value == '2') {
			unit_echarts_line.value = 'kW';
		} else {
			unit_echarts_line.value = '元/kWh';
		}

		let option_result = handleLineMapOptionSingal(
			xaxis_data_echarts_line.value,
			yaxis_data_echarts_line.value,
			unit_echarts_line.value,
			colorList
		);

		option_result.series.forEach((seriesItem, i) => {
			if (!seriesItem.name.includes('储能实际总功率')) {
				option_result.series[i].step = 'start';
			}
			// if (colorMap[seriesItem.name]) {
			// 	option_result.series[i].itemStyle.color = colorMap[seriesItem.name];
			// }
			// if (seriesItem.name.includes('价格')) {
			// 	option_result.series[i].lineStyle = {
			// 		type: 'dashed' // 设置为虚线
			// 	};
			// }
		});

		// console.log(option_result,'option_result');
		if (document.getElementById('echarts_line_id')) {
			my_chart_line.value = markRaw(echarts.init(document.getElementById('echarts_line_id')));
			my_chart_line.value.setOption(option_result);
		}
	};

	onMounted(() => {
		// 项目id
		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			project_id.value = project_mess.project_id;
		} else {
			message.error('未选择项目，请重新选择！');
		}
		getEchartsLine();
	});

	// 重置查询条件
	const resetEchartsLineInput = () => {
		Object.assign(form_state_echarts_line, {
			time: [dayjs().startOf('day'), dayjs().add(1, 'day')]
		});
		getEchartsLine(); // 调用查询接口
	};
</script>

<style lang="less" scoped>
	.main_contain_scroll {
		background-color: transparent !important;
		padding: 0 !important;
		overflow: hidden;
	}

	:deep(.ant-picker-dropdown) {
		left: 0px !important;
		top: 38px !important;
	}

	.map_item {
		height: 100%;
		padding: 32px;
		background-color: #ffffff;
		margin-bottom: 0;
		box-sizing: border-box;

		.map_title {
			height: 18px;
			line-height: 18px;
			font-family: SourceHanSansCN-Regular;
			font-weight: bold;
			font-size: 18px;
			color: #092649;
		}

		.params_list {
			height: 36px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			margin-top: 32px;
			margin-bottom: 20px;
			// align-items: center;

			// .params_list_left {
			// 	height: 36px;
			// 	line-height: 36px;
			// 	display: flex;
			// 	flex-direction: row;
			// 	justify-content: space-between;
			// 	align-items: center;
			// }
		}

		&:last-child {
			margin-bottom: 0px;
		}
	}

	.map_line_contain {
		height: calc(100% - 35px - 32px - 20px - 17.5px - 45px);
		width: 100%;
		position: relative;

		.unit_mess {
			color: #949aa6;
			font-size: 14px;
			font-family: 'shsr';
			position: absolute;
			top: 5px;
		}

		.map_line {
			height: 100%;
			width: 100%;
		}

		.no_data {
			position: absolute;
			top: -5%;
			left: 0;
			right: 0;
			bottom: 0;
			padding-top: 0px;
		}
	}

	.params_list_right {
		display: flex;
	}
</style>
