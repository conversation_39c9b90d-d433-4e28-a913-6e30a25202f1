<template>
	<div class="main_contain main_contain_thirdMenu">
		<a
			href="#"
			id="download"
			style="position: absolute; top: -1000px; right: -1000px"
		></a>
		<div class="main_contain_scroll">
			<div class="map_item">
				<div class="map_title">用能功率曲线</div>
				<div class="params_list">
					<div class="params_list_left">
						<a-form
							id="xuliang"
							:model="powerFormState"
							name="xuliang"
							layout="inline"
							autocomplete="off"
						>
							<a-form-item
								label="时间选择"
								name="time"
							>
								<a-range-picker
									:getPopupContainer="(e) => e.parentNode"
									name="time_select_yngl"
									:allowClear="false"
									v-model:value="form_state_echarts_line.time"
									style="width: 300px; margin-right: 15px"
									:disabledDate="
										(current) => {
											return current && current > dayjs().endOf('day');
										}
									"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/clock.png"
											alt="#"
										/>
									</template>
								</a-range-picker>
							</a-form-item>

							<a-form-item>
								<a-checkbox
									v-model:checked="contrast"
									@change="isContrastChange"
									style="font-size: 16px"
								>
									对比
								</a-checkbox>
							</a-form-item>

							<a-form-item
								label="添加对比"
								name="compareStartTime"
								v-if="contrast"
							>
								<a-date-picker
									style="width: 200px"
									:allowClear="false"
									:disabled-date="
										(current) => {
											return current && current > dayjs().endOf('day');
										}
									"
									v-model:value="form_state_echarts_line.compareStartTime"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/picker_month.png"
											alt="#"
										/>
									</template>
								</a-date-picker>
							</a-form-item>

							<a-form-item>
								<a-button
									type="primary"
									@click="getEchartsLine"
								>
									查询
								</a-button>
							</a-form-item>
							<a-form-item>
								<a-button @click="resetEchartsLineInput">重置</a-button>
							</a-form-item>
						</a-form>
					</div>
					<div class="params_list_right">
						<a-button
							type="primary"
							@click="skipUnnormal"
							style="margin-right: 16px; display: flex"
						>
							<div class="icon_button">
								<img
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/unnormal_setting.png"
									alt=""
								/>
								<span>异常规则配置</span>
							</div>
						</a-button>
						<a-button
							:loading="export_loading"
							@click="exportFn"
							:disabled="!hasData"
							style="display: flex"
							@mouseenter="hover1 = true"
							@mouseleave="hover1 = false"
						>
							<div class="icon_button">
								<img
								v-if="hover1 && hasData"
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_hover.png"
									alt=""
								/>
								<img
								v-else
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_dark.png"
									alt=""
								/>
								<span>导出</span>
							</div>
						</a-button>
					</div>
				</div>
				<div class="map_line_contain">
					<!-- <span class="unit_mess">kW</span> -->
					<div
						v-if="hasData"
						class="map_line"
						id="echarts_line_id"
					></div>
					<a-empty
						v-if="!hasData"
						style="margin: 0 auto; position: relative; top: 30%"
					/>
				</div>
			</div>

			<div class="map_item">
				<div class="map_title">用能电量</div>
				<div class="params_list">
					<div class="params_list_left">
						<a-form
							id="horizontal_login"
							:model="form_state_echarts_bar"
							name="horizontal_login"
							layout="inline"
							autocomplete="off"
							@finish="onFinishElec"
						>
							<a-form-item
								label="粒度选择"
								name="time"
							>
								<a-select
									ref="select_time_type"
									:getPopupContainer="(e) => e.parentNode"
									v-model:value="form_state_echarts_bar.time_type"
									style="width: 120px"
								>
									<a-select-option value="1">日</a-select-option>
									<a-select-option value="2">月</a-select-option>
									<a-select-option value="3">年</a-select-option>
								</a-select>
							</a-form-item>
							<a-form-item
								label="时间选择"
								name="time"
							>
								<a-range-picker
									:getPopupContainer="(e) => e.parentNode"
									name="time_select_yndl"
									v-model:value="form_state_echarts_bar.time"
									:allowClear="false"
									:picker="time_picker"
									style="width: 300px; margin-right: 15px"
									:disabledDate="
										(current) => {
											return current && current > dayjs().endOf(time_picker);
										}
									"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/clock.png"
											alt="#"
										/>
									</template>
								</a-range-picker>
							</a-form-item>
							<a-form-item>
								<a-checkbox
									v-model:checked="powerContrast"
									@change="isPowerContrastChange"
									style="font-size: 16px"
								>
									对比
								</a-checkbox>
							</a-form-item>
							<a-form-item
								label="添加对比"
								name="compareStartTime"
								v-if="powerContrast"
							>
								<a-date-picker
									style="width: 200px"
									:disabled-date="
										(current) => {
											return current && current > dayjs().endOf('day');
										}
									"
									:picker="time_picker"
									:allowClear="false"
									v-model:value="form_state_echarts_bar.compareStartTime"
								>
									<template #suffixIcon>
										<img
											style="width: 18px; height: 18px"
											src="/assets/icon/picker_month.png"
											alt="#"
										/>
									</template>
								</a-date-picker>
							</a-form-item>

							<a-form-item>
								<a-button
									type="primary"
									@click="getEchartsBar"
								>
									查询
								</a-button>
							</a-form-item>
							<a-form-item>
								<a-button @click="resetInputEchartsBar">重置</a-button>
							</a-form-item>
						</a-form>
					</div>
					<div class="params_list_right">
						<a-button
							type="primary"
							@click="skipUnnormal"
							style="margin-right: 16px; display: flex"
						>
							<div class="icon_button">
								<img
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/unnormal_setting.png"
									alt=""
								/>
								<span>异常规则配置</span>
							</div>
						</a-button>
						<a-button
							:loading="export_loading_2"
							@click="exportFn_2"
							:disabled="!hasData1"
							@mouseenter="hover2 = true"
							@mouseleave="hover2 = false"
							style="display: flex"
						>
							<div class="icon_button">
								<img
								v-if="hover2 && hasData1"
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_hover.png"
									alt=""
								/>
								<img
								v-else
									style="display: inline-block; height: 18px; width: 18px"
									src="/assets/icon/export_icon_dark.png"
									alt=""
								/>
								<span>导出</span>
							</div>
						</a-button>
					</div>
				</div>
				<div class="map_line_contain">
					<!-- <span class="unit_mess">kWh</span> -->
					<div
						v-if="hasData1"
						class="map_line"
						id="echarts_bar_id"
					></div>
					<a-empty
						v-if="!hasData1"
						style="margin: 0 auto; position: relative; top: 30%"
					/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, reactive, onMounted, markRaw, watch } from 'vue';
	import * as echarts from 'echarts';
	import {
		handleTime,
		timeRange,
		handleTimeParams,
		handleLineMapOption,
		handleLineMapSelection,
		handleBarMapSelection,
		handleBarMapOption,
		handleXAxisTime,
		init_x_y,
		handleMax
	} from '/assets/js/commonTool.js';
	// import zhCN from 'ant-design-vue/es/locale/zh_CN'
	import {
		getStandardValueByEleratePrice,
		fhStatMinuteActivePower,
		fhStatPositiveAndReverseActivePower,
		ynExportMinuteActivePower,
		ynExportPositiveActivePower
	} from './api.js';
	import dayjs from 'dayjs';
	import { message } from 'ant-design-vue';
	import { eventBus } from '/assets/js/eventBus'; // 导入事件总线实例
	// ***********************************************************************************************************
	//                                                   全局参数配置                                            //
	// ***********************************************************************************************************

	const project_id = ref(null);
	const skipUnnormal = () => {
		eventBus.emit('menu-change', [1, 2, '/pages/operationMonitoring/#/ruleSetting']); // 触发事件
		// window.location.href = '/pages/operationMonitoring/#/ruleSetting?first_tab_index=1&second_tab_index=2'
	};
	// ***********************************************************************************************************
	//                                                   全局参数配置                                            //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                              用能功率                                            //
	// ***********************************************************************************************************

	const hasData = ref(true);
	// 时间参数
	const form_state_echarts_line = reactive({
		time: [dayjs().startOf('day'), dayjs()],
		compareStartTime: null
	});

	// 重置查询条件
	const resetEchartsLineInput = () => {
		Object.assign(form_state_echarts_line, {
			time: [dayjs().startOf('day'), dayjs()],
			compareStartTime: null
		});
		contrast.value = false;
		getEchartsLine();
	};

	const contrast = ref(false);
	const powerContrast = ref(false);
	const isContrastChange = () => {
		if (contrast.value) {
			form_state_echarts_line.compareStartTime = dayjs().subtract(1, 'day');
		} else {
			form_state_echarts_line.compareStartTime = null;
		}
	};

	// 时间插件
	const time_status_echarts_line = ref(false);

	// 查询
	const xaxis_data_echarts_line = ref([]);
	const yaxis_data_echarts_line = ref([]);
	const xaxis_data_echarts_line_contrast = ref([]);
	const yaxis_data_echarts_line_contrast = ref([]);
	const unit_echarts_line = ref('kW');
	const my_chart_line = ref();
	const capacity_echarts_line = ref(null);
	// 根据条件查询需量和储能功率
	const getEchartsLine = () => {
		let time_judge = timeRange(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'date'
		);

		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}

		let time_result = handleTimeParams(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'day'
		);
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;

		let params = {
			dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: project_id.value,
			startTime
		};
		form_state_echarts_line.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_line.compareStartTime)
					.startOf('day')
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;

		Promise.all([
			fhStatMinuteActivePower(params) // 功率曲线
		])
			.then(([data1]) => {
				if (data1) {
					handleDataEchartsLine(data1.statisticList);
				} else {
					hasData.value = false;
				}
			})
			.catch((error) => {
				console.log(error);
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	const handleDataEchartsLine = (data) => {
		if (data[0].elementList.length > 0) {
			let result = init_x_y(data[0].elementList);
			xaxis_data_echarts_line.value = result.x_axis;
			yaxis_data_echarts_line.value = result.y_axis;
		}
		if (data[0].compareElementList.length > 0) {
			let result_contrast = init_x_y(data[0].compareElementList);
			xaxis_data_echarts_line_contrast.value = result_contrast.x_axis;
			yaxis_data_echarts_line_contrast.value = result_contrast.y_axis;
		}

		drawMapEchartsLine();
	};

	const drawMapEchartsLine = () => {
		if (my_chart_line.value) {
			my_chart_line.value.dispose();
		}
		let option_result = {};
		if (contrast.value) {
			option_result = handleLineMapSelection(
				xaxis_data_echarts_line.value,
				yaxis_data_echarts_line.value,
				xaxis_data_echarts_line_contrast.value,
				yaxis_data_echarts_line_contrast.value,
				unit_echarts_line.value
			);
		} else {
			option_result = handleLineMapSelection(
				xaxis_data_echarts_line.value,
				yaxis_data_echarts_line.value,
				'',
				[],
				unit_echarts_line.value
			);
		}

		// let max = handleMax(option_result.yAxis[0].max);
		// option_result.yAxis[0].max = max;
		delete option_result.yAxis[0].max;

		if (document.getElementById('echarts_line_id')) {
			my_chart_line.value = markRaw(echarts.init(document.getElementById('echarts_line_id')));
			my_chart_line.value.setOption(option_result);
		}
	};

	const hover1 = ref(false);
	const hover2 = ref(false);

	// ***********************************************************************************************************
	//                                              用能功率                                            //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                              用能功率导出                                                //
	// ***********************************************************************************************************
	const export_loading = ref(false);
	const exportFn = () => {
		let time_judge = timeRange(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'date'
		);
		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}
		let time_result = handleTimeParams(
			form_state_echarts_line.time[0],
			form_state_echarts_line.time[1],
			'day'
		);
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;
		let params = {
			dateParticle: 1, //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: Number(project_id.value),
			startTime
		};
		form_state_echarts_line.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_line.compareStartTime)
					.startOf('day')
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;
		export_loading.value = true;
		ynExportMinuteActivePower(params)
			.then((data) => {
				let templateName =
					dayjs(startTime).format('YYYYMMDD') +
					'-' +
					dayjs(endTime).format('YYYYMMDD') +
					'用能功率曲线报表';
				if (!data) {
					return;
				}
				const url = window.URL.createObjectURL(new Blob([data], { type: 'application/zip' }));
				let link = document.getElementById('download');
				link.href = url;
				link.setAttribute('download', `${templateName}.xlsx`);
				link.click();
				export_loading.value = false;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
				export_loading.value = false;
			});
	};
	// ***********************************************************************************************************
	//                                              用能功率导出                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                              用能电量                                               //
	// ***********************************************************************************************************
	const hasData1 = ref(true);
	// 时间参数
	const time_picker = ref('date');
	const form_state_echarts_bar = reactive({
		time: [dayjs().startOf('day'), dayjs()],
		compareStartTime: null,
		time_type: '1'
	});
	watch(
		() => form_state_echarts_bar.time_type,
		(newVal) => {
			if (newVal == 1) {
				time_picker.value = 'date';
			} else if (newVal == 2) {
				time_picker.value = 'month';
			} else if (newVal == 3) {
				time_picker.value = 'year';
			}
			// form_state_echarts_bar.compareStartTime = dayjs().subtract(1, time_picker.value);
			isPowerContrastChange();
		},
		{ deep: true }
	);
	const isPowerContrastChange = () => {
		if (powerContrast.value) {
			let type = 'day';
			if (time_picker.value != 'date') {
				type = time_picker.value;
			}
			form_state_echarts_bar.compareStartTime = dayjs().subtract(1, type);
		} else {
			form_state_echarts_bar.compareStartTime = null;
		}
	};
	// 重置查询条件
	const resetInputEchartsBar = () => {
		powerContrast.value = false;
		Object.assign(form_state_echarts_bar, {
			time: [dayjs().startOf('day'), dayjs()],
			compareStartTime: null,
			time_type: '1'
		});
		time_picker.value = 'date';
		getEchartsBar();
	};

	// 查询光伏发电量
	const getEchartsBar = () => {
		// 根据时间粒度进行判断
		let time_judge = timeRange(
			form_state_echarts_bar.time[0],
			form_state_echarts_bar.time[1],
			time_picker.value
		);
		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}
		let time_type_current = time_picker.value;
		let time_result = handleTimeParams(
			form_state_echarts_bar.time[0],
			form_state_echarts_bar.time[1],
			time_type_current
		);
		let startTime = time_result.startTime;
		let endTime = time_result.endTime;

		let params = {
			dateParticle: Number(form_state_echarts_bar.time_type), //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: project_id.value,
			startTime
		};

		form_state_echarts_bar.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_bar.compareStartTime)
					.startOf(time_type_current)
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;

		fhStatPositiveAndReverseActivePower(params)
			.then((data) => {
				console.log(data);
				if (data) {
					handleDataEchartsBar(data.statisticList);
				} else {
					hasData1.value = false;
				}
			})
			.catch((error) => {
				console.log(error);
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	const handleDataEchartsBar = (data) => {
		console.log(data);
		let x_axis = [];
		let x_axis_fan = [];
		let y_axis = [];
		let y_axis_constract = [];
		for (let i = 0; i < data.length; i++) {
			// 正向有功和反向有功的处理
			let item_i = data[i];
			let name_i = data[i].paramName;

			for (let j = 0; j < item_i.elementList.length; j++) {
				// 处理元素列表的数据
				let name_j = item_i.elementList[j].elementCodeDesc;
				if (j == 0 && item_i.elementList[j].detailList.length > 0) {
					// 仅执行一次就行
					x_axis = handleXAxisTime(item_i.elementList[j].detailList, time_picker.value);
					if (powerContrast.value) {
						x_axis_fan = handleXAxisTime(
							item_i.compareElementList[j].detailList,
							time_picker.value
						);
					}
				}
				let value_list = [];
				let value_list_fan = [];
				for (let k = 0; k < item_i.elementList[j].detailList.length; k++) {
					// 具体的时间值
					value_list.push(item_i.elementList[j].detailList[k].value);
					if (powerContrast.value) {
						value_list_fan.push(item_i.compareElementList[j].detailList[k].value);
					}
					// value_list.push(Math.ceil(200 * Math.random()))
				}
				y_axis.push({ name: name_i + '-' + name_j, value_list: value_list });
				y_axis_constract.push({ name: name_i + '-' + name_j + '对比', value_list: value_list_fan });
			}
		}
		yaxis_data_echarts_bar.value = y_axis;
		yaxis_data_echarts_bar_constract.value = y_axis_constract;
		xaxis_data_echarts_bar.value = x_axis;
		xaxis_data_echarts_bar_constract.value = x_axis_fan;
		drawMapEchartsBar();
	};

	const yaxis_data_echarts_bar = ref([]);
	const yaxis_data_echarts_bar_constract = ref([]);
	const unit_echarts_bar = ref('kWh');
	const xaxis_data_echarts_bar = ref([]);
	const xaxis_data_echarts_bar_constract = ref([]);
	const my_chart_bar = ref();

	let result_option = {};
	const drawMapEchartsBar = () => {
		if (powerContrast.value) {
			result_option = handleBarMapSelection(
				xaxis_data_echarts_bar.value,
				xaxis_data_echarts_bar_constract.value,
				yaxis_data_echarts_bar.value,
				yaxis_data_echarts_bar_constract.value,
				unit_echarts_bar.value
			);
		} else {
			result_option = handleBarMapSelection(
				xaxis_data_echarts_bar.value,
				'',
				yaxis_data_echarts_bar.value,
				[],
				unit_echarts_bar.value
			);
		}
		if (my_chart_bar.value) {
			my_chart_bar.value.dispose();
		}
		if (document.getElementById('echarts_bar_id')) {
			my_chart_bar.value = markRaw(echarts.init(document.getElementById('echarts_bar_id')));
			my_chart_bar.value.setOption(result_option);
		}
	};

	// ***********************************************************************************************************
	//                                              用能电量                                               //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                             用能电量导出                                                //
	// ***********************************************************************************************************
	const export_loading_2 = ref(false);
	const exportFn_2 = () => {
		let time_judge = timeRange(
			form_state_echarts_bar.time[0],
			form_state_echarts_bar.time[1],
			time_picker.value
		);
		if (!time_judge.status) {
			message.error(time_judge.message);
			return;
		}

		let time_type_current = time_picker.value;

		let time_result = handleTimeParams(
			form_state_echarts_bar.time[0],
			form_state_echarts_bar.time[1],
			time_type_current
		);

		let startTime = time_result.startTime;
		let endTime = time_result.endTime;
		let params = {
			dateParticle: Number(form_state_echarts_bar.time_type), //时间粒度此处默认为 日  日期粒度(1 日 2 月 3 年)
			endTime,
			projectId: Number(project_id.value),
			startTime
		};
		form_state_echarts_bar.compareStartTime
			? (params.compareStartTime = dayjs(form_state_echarts_bar.compareStartTime)
					.startOf(time_type_current)
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;
		export_loading_2.value = true;
		ynExportPositiveActivePower(params)
			.then((data) => {
				let timeFormat = 'YYYYMMDD';
				if (time_picker.value == 'month') {
					timeFormat = 'YYYYMM';
				} else if (time_picker.value == 'year') {
					timeFormat = 'YYYY';
				}
				let templateName =
					dayjs(startTime).format(timeFormat) +
					'-' +
					dayjs(endTime).format(timeFormat) +
					'用能电量报表';
				if (!data) {
					return;
				}
				const url = window.URL.createObjectURL(new Blob([data], { type: 'application/zip' }));
				let link = document.getElementById('download');
				link.href = url;
				link.setAttribute('download', `${templateName}.xlsx`);
				link.click();
				export_loading_2.value = false;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
				export_loading_2.value = false;
			});
	};
	// ***********************************************************************************************************
	//                                              导出                                                //
	// ***********************************************************************************************************

	// 全页面查询
	const searchAll = () => {
		// 查询用能功率曲线
		getEchartsLine();
		// 查询用能电量
		getEchartsBar();
	};

	onMounted(() => {
		// 项目id
		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			project_id.value = project_mess.project_id;
		} else {
			message.error('未选择项目，请重新选择！');
		}
		// 初始化调用
		searchAll();
	});
</script>
<style lang="less" scoped>
	.main_contain_scroll {
		background-color: transparent !important;
		padding: 0 !important;
	}

	:deep(.ant-picker-dropdown){
		left: 0px !important;
		top: 38px !important;
	}

	.map_item {
		height: 598px;
		padding: 32px;
		background-color: #ffffff;
		margin-bottom: 32px;
		box-sizing: border-box;
		.map_title {
			height: 18px;
			line-height: 18px;
			font-family: SourceHanSansCN-Regular;
			font-weight: bold;
			font-size: 18px;
			color: #092649;
		}
		.params_list {
			height: 36px;
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			margin-top: 32px;
			margin-bottom: 20px;
		}
		&:last-child {
			margin-bottom: 0px;
		}
	}

	.map_line_contain {
		height: 426px;
		width: 100%;
		position: relative;

		.unit_mess {
			color: #949aa6;
			font-size: 14px;
			font-family: 'shsr';
			position: absolute;
			top: 5px;
		}
		.map_line {
			height: 100%;
			width: 100%;
		}

		.no_data {
			position: absolute;
			top: -5%;
			left: 0;
			right: 0;
			bottom: 0;
			padding-top: 0px;
		}
	}
	.params_list_right {
		display: flex;
	}
</style>
