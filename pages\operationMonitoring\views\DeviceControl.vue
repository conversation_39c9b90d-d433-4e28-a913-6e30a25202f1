<template>
  <div class="page_all equipment_list">
    <div class="main_contain">
      <div class="main_contain_scroll">
        <!-- 菜单title -->
        <div class="page_header_title">设备清单</div>

        <!-- 搜索条件 -->
        <div class="search_input_reset">
           <a-input
            autocomplete="off"
            style="width: 370px"
            v-model:value="search_value_input"
            placeholder="请输入设备名称或平台设备编号"
          />
          <a-button @click="search" style="margin-left: 16px" type="primary">查询</a-button>
          <a-button @click="resetInput" style="margin-left: 16px">重置</a-button>
        </div>

        <!-- 设备分类数量 -->
        <div class="device_num">
          <div class="device_num_item">
            <div class="num_title">设备数量</div>
            <div class="num_mess">
              <span>{{ device_num_all_type.device_num }}</span>
              <span>个</span>
            </div>
            <div class="device_icon"></div>
          </div>
          <div class="device_num_item">
            <div class="num_title">在线</div>
            <div class="num_mess">
              <span>{{ device_num_all_type.device_online }}</span>
              <span>个</span>
            </div>
            <div class="device_icon"></div>
          </div>
          <div class="device_num_item">
            <div class="num_title">离线</div>
            <div class="num_mess">
              <span>{{ device_num_all_type.device_outline }}</span>
              <span>个</span>
            </div>
            <div class="device_icon"></div>
          </div>
        </div>

        <!-- 搜索结果列表 -->
        <a-table :pagination="false" :columns="columns" :data-source="dataSource" :bordered="false">
          <template #headerCell="{ column }">
            <template v-if="column.key === 'id'">
              <a-tooltip placement="right">
                <template #title>
                  <span>平台设备编号</span>
                </template>
                <div
                  style="width: max-content; cursor: pointer; display: flex; align-items: center"
                >
                  {{ column.title }}
                  <img style="margin-left: 8px" src="/assets/icon/tips.png" alt="#" />
                </div>
              </a-tooltip>
            </template>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <div v-if="record.status == 1" class="device_status success_tag">在线</div>
              <div v-if="record.status == 2" class="device_status error_tag">离线</div>
            </template>
          </template>
        </a-table>
        <Pagination
          ref="pagination"
          @changePageSize="changePageSize"
          :totalNum="totalNum"
        ></Pagination>
      </div>
    </div>
    <!-- <Loading ref="loading"></Loading> -->
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, watch } from 'vue'
// 分页的结构
import Pagination from '/components/Pagination.vue'
// loading 的组件
// import Loading from '/components/Loading.vue'
// 请求方法
import { getDeviceListHttp, getDeviceNumberHttp } from './deviceControl.js'
import { message } from 'ant-design-vue'
const project_id = ref()


const loading = ref()

const pagination = ref()
const search_params = reactive({
  pageSize: 10,
  pageIndex: 1,
  conditions: null,
  projectId: project_id.value
})

const search_value_input = ref(null)

watch(search_value_input, (newVal) => {
  search_params.conditions = newVal
})
const totalNum = ref(0) // 表格数据总数
const changePageSize = (currentPageMess) => {
  console.log(currentPageMess)

  search_params.pageSize = currentPageMess.currentPageSize
  search_params.pageIndex = currentPageMess.currentPage
  // 修改pagesize后调用接口
  let { pageSize, pageIndex, conditions, projectId } = search_params
  let params = { pageSize, pageIndex, conditions, projectId }
  getDeviceList(params)
}
// 搜索信息输入的部分 点击查询
const search = () => {
  console.log(search_params);
  let { pageSize, pageIndex, conditions, projectId } = search_params
  // 设备列表
  getDeviceList({ pageSize, pageIndex, conditions, projectId })
  // 设备数量
  getDeviceNumber({ conditions, projectId })
}

const resetInput = () => {
  search_value_input.value = null
  Object.assign(search_params, {
    pageSize: 10,
    pageIndex: 1,
    conditions: null,
    projectId: project_id.value
  })
  pagination.value.reset()
  search()
}

const columns = [
  {
    title: '序号',
    key: 'no',
    dataIndex: 'no',
    align: 'center'
  },
  {
    title: '设备名称',
    key: 'name',
    dataIndex: 'name'
  },
  {
    title: '平台设备编号',
    key: 'id',
    dataIndex: 'id',
    width: 300,
  },
  {
    title: '状态',
    key: 'status',
    dataIndex: 'status',
    align: 'center'
  },
  {
    title: '最新上报时间',
    key: 'update_time',
    dataIndex: 'update_time'
  },
  {
    title: '创建时间',
    key: 'create_time',
    dataIndex: 'create_time'
  }
]
const dataSource = ref([])

const getDeviceList = async (params) => {
  await getDeviceListHttp(params).then((data) => {

    totalNum.value = data.total
    let temp_data = []
    for (let i = 0; i < data.list.length; i++) {
      let {
        deviceCode: id,
        deviceName: name,
        deviceStatus: status,
        lastInputTime: update_time,
        createTime: create_time
      } = data.list[i]
      temp_data.push({
        no: i + 1,
        name,
        id,
        status,
        update_time,
        create_time
      })
    }
    dataSource.value = temp_data
    // return temp_data
  })
}
const device_num_all_type = reactive({
  device_num: 0,
  device_online: 0,
  device_outline: 0
})
// 获取设备不同类型数量情况
const getDeviceNumber = async (params) => {
  await getDeviceNumberHttp(params).then((data) => {
    let { devNums: device_num, offlineNums: device_outline, onlineNums: device_online } = data
    Object.assign(device_num_all_type, {
      device_num,
      device_online,
      device_outline
    })
    // return data
  })
}


onMounted(() => {
  let project_mess = localStorage.getItem('project_mess')
  if (project_mess) {
    project_mess = JSON.parse(project_mess)
    project_id.value = project_mess.project_id
  } else {
    message.error('未选择项目，请重新选择！')
  }
  search_params.projectId = project_id.value
  search()
 
})
</script>
<style lang="less" scoped>
.search_input_reset {
  margin-top: 32px;
}

.device_num {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin: 32px 0 25px 0;
  .device_num_item {
    margin-right: 32px;
    width: 334px;
    width: 17.4vw;
    height: 124px;
    border-radius: 8px;
    padding: 32px 1.7vw;
    box-sizing: border-box;
    position: relative;
    .device_icon {
      width: 101px;
      height: 101px;
      position: absolute;
      right: 4.5%;
      top: 16%;
    }
    &:nth-child(1) {
      background: linear-gradient(90deg, rgba(17, 129, 253, 0.12), rgba(17, 129, 253, 0.02));
      .device_icon {
        background-image: url('/assets/icon/device_num.png');
        background-repeat: no-repeat;
        background-size: 100%;
      }
    }
    &:nth-child(2) {
      background: linear-gradient(90deg, rgba(3, 227, 161, 0.12), rgba(3, 227, 161, 0.02));
      .device_icon {
        background-image: url('/assets/icon/device_online.png');
        background-repeat: no-repeat;
        background-size: 100%;
      }
    }
    &:nth-child(3) {
      background: linear-gradient(90deg, rgba(121, 133, 252, 0.12), rgba(121, 133, 252, 0.02));
      .device_icon {
        background-image: url('/assets/icon/device_outline.png');
        background-repeat: no-repeat;
        background-size: 100%;
      }
      .num_mess {
        span {
          &:first-child {
            color: #ff6161;
          }
        }
      }
    }

    .num_title {
      font-family: SourceHanSansCN-Regular;
      font-weight: 400;
      font-size: 16px;
      color: #9ca2af;
      height: 16px;
      margin-bottom: 22px;
    }
    .num_mess {
      height: 22px;
      span {
        display: inline-block;
        &:nth-child(1) {
          font-family: D-DIN-PRO;
          font-weight: bold;
          font-size: 32px;
          color: #092649;
        }
        &:nth-child(2) {
          font-family: SourceHanSansCN-Regular;
          font-weight: 400;
          font-size: 14px;
          color: #9ca2af;
        }
      }
    }
  }
}

.device_status {
  width: 46px;
  height: 26px;
  border-radius: 13px;
  line-height: 26px;
  text-align: center;
  margin: 0 auto;
}

</style>
