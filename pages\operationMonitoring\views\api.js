import http from '/tool/http.js';

// 查询异常列表  分页查询
export const getUnnormalListHttp = (params) => {
	let url = '/api/warn/findPage';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200 && data.data.records.length > 0) {
					resolve({ list: data.data.records, total: data.data.total });
				} else {
					// 空数据
					resolve([]);
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 统计异常触发总数
export const getUnnormalCountHttp = (params) => {
	// console.log(params)
	let url = '/api/warn/countAll';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					// 空数据
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 根据预警级别统计异常数
export const getWarningLevelHttp = (params) => {
	let url = '/api/warn/countWarnNumByWarnLevel';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					// 空数据
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 根据处理状态统计异常数
export const getWarningStatusHttp = (params) => {
	let url = '/api/warn/countWarnNumByWarnStatus';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					// 空数据
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 根据触发次数统计告警规则条数
export const getWarningCountHttp = (params) => {
	let url = '/api/warn/countRuleNumByTriggerCount';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					// 空数据
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 确认异常
export const confirmUnnormalHttp = (params) => {
	let url = `/api/warn/confirm/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.success);
				} else {
					// 空数据
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 处理异常
export const handleUnnormalHttp = (params) => {
	let url = `/api/warn/handling`;
	console.log(params);
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.msg);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询异常信息详情  处理结果详情
export const getUnnormalDetailHttp = (params) => {
	let url = `/api/warn/getById`;
	console.log(params);
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					// 空数据
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 分页查询规则配置
export const getRuleSettingHttp = (params) => {
	let url = `/api/rule/findPage`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				// console.log(data)
				if (data.code == 200) {
					resolve({ total: data.data.total, list: data.data.records });
				} else {
					// 空数据
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询边端控制器下面元素列表
export const getEdgeElementSimpleListHttp = (params) => {
	let url = `/api/select/getEdgeElementSimpleList/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					// 空数据
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询边端控制器下面相关元素的设备列表
export const getDeviceSimpleListHttp = (params) => {
	let url = `/api/select/getDeviceSimpleList`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					// 空数据
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 根据设备类型查询量测列表
export const getByDeviceTypeHttp = (params) => {
	let url = `/api/select/getByDeviceType/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					// 空数据
					reject({ message: '数据为空' });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 添加规则配置
export const addRuleHttp = (params) => {
	let url = `/api/rule/addRule`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.msg);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 修改规则配置
export const editRuleHttp = (params) => {
	let url = `/api/rule/updateRule`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.msg);
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询规则配置详情
export const getRuleDetailByIdHttp = (params) => {
	let url = `/api/rule/getById/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 删除规则配置
export const deleteRuleHttp = (params) => {
	let url = `/api/rule/deleteRule/${params}`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.msg);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询微网功率
export const statMinuteActivePowerHttp = (params) => {
	let url = `/api/overview/statMinuteActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				// console.log(data);
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

//查询指定元素类型标准值
export const getStandardValueByElement = (params) => {
	let url = `/api/overview/getStandardValueByElement`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询需量限制
export const getStandardValueByEleratePrice = (params) => {
	let url = `/api/overview/getStandardValueByEleratePrice`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询需量和储能功率
export const statMinuteRealDemandAndActivePowerHttp = (params) => {
	let url = `/api/overview/statMinuteRealDemandAndActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询微网电量
export const statPositiveAndReverseActivePowerHttp = (params) => {
	let url = `/api/overview/statPositiveAndReverseActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询光伏发电功率
export const pvStatMinuteActivePowerHttp = (params) => {
	let url = `/api/pv/statMinuteActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询光伏发电量
export const pvStatPositiveAndReverseActivePower = (params) => {
	let url = `/api/pv/statPositiveAndReverseActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询充电桩功率
export const cdzStatMinuteActivePower = (params) => {
	let url = `/api/cdz/statMinuteActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询充电桩电量
export const cdzStatPositiveAndReverseActiveTime = (params) => {
	let url = `/api/cdz/statPositiveAndReverseActiveTime`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询用能功率
export const fhStatMinuteActivePower = (params) => {
	let url = `/api/fh/statMinuteActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询用能电量
export const fhStatPositiveAndReverseActivePower = (params) => {
	let url = `/api/fh/statPositiveAndReverseActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询储能功率
export const cnStatMinuteActivePower = (params) => {
	let url = `/api/cn/statMinuteActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询SoC
export const cnStatMinuteSoC = (params) => {
	let url = `/api/cn/statMinuteSoC`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询储能功率和SOC
export const cnStatMinuteActivePowerAndSOC = (params) => {
	let url = `/api/cn/statMinuteActivePowerAndSOC`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 查询储能充放电量
export const cnStatPositiveAndReverseActivePower = (params) => {
	let url = `/api/cn/statChargeAndDischarge`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 导出微网功率
export const exportMinuteActivePowerHttp = (params) => {
	// console.log(params);
	let url = `/api/overview/exportMinuteActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params, {
				headers: { 'content-type': 'application/json; charset=utf-8' },
				responseType: 'blob'
			})
			.then((data) => {
				resolve(data);
				// if (data.code == 200) {
				//     resolve(data.data)
				// }else{
				//     reject({
				// message: data.msg
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 导出需量和储能功率
export const exportMiuteRealDemandAndActivePower = (params) => {
	let url = `/api/overview/exportMiuteRealDemandAndActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params, {
				headers: { 'content-type': 'application/json; charset=utf-8' },
				responseType: 'blob'
			})
			.then((data) => {
				resolve(data);
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 导出微网电量
export const exportPositiveAndReverseActivePower = (params) => {
	let url = `/api/overview/exportPositiveAndReverseActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params, {
				headers: { 'content-type': 'application/json; charset=utf-8' },
				responseType: 'blob'
			})
			.then((data) => {
				resolve(data);
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 导出光伏发电功率
export const pvExportMinuteActivePower = (params) => {
	let url = `/api/pv/exportMinuteActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params, {
				headers: { 'content-type': 'application/json; charset=utf-8' },
				responseType: 'blob'
			})
			.then((data) => {
				resolve(data);
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 导出光伏发电量
export const pvExportPositiveActivePower = (params) => {
	let url = `/api/pv/exportPositiveActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params, {
				headers: { 'content-type': 'application/json; charset=utf-8' },
				responseType: 'blob'
			})
			.then((data) => {
				resolve(data);
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 导出充电桩功率
export const cdzExportMinuteActivePower = (params) => {
	let url = `/api/cdz/exportMinuteActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params, {
				headers: { 'content-type': 'application/json; charset=utf-8' },
				responseType: 'blob'
			})
			.then((data) => {
				resolve(data);
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 导出充电桩功率
export const cdzExportPositiveActivePower = (params) => {
	let url = `/api/cdz/exportPositiveActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params, {
				headers: { 'content-type': 'application/json; charset=utf-8' },
				responseType: 'blob'
			})
			.then((data) => {
				resolve(data);
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 导出用能功率
export const ynExportMinuteActivePower = (params) => {
	let url = `/api/fh/exportMinuteActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params, {
				headers: { 'content-type': 'application/json; charset=utf-8' },
				responseType: 'blob'
			})
			.then((data) => {
				resolve(data);
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};
// 导出用能电量
export const ynExportPositiveActivePower = (params) => {
	let url = `/api/fh/exportPositiveActivePower`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params, {
				headers: { 'content-type': 'application/json; charset=utf-8' },
				responseType: 'blob'
			})
			.then((data) => {
				resolve(data);
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 导出储能充放电
export const cnExportPositiveAndReverseActivePower = (params) => {
	let url = `/api/cn/exportChargeAndDischarge`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params, {
				headers: { 'content-type': 'application/json; charset=utf-8' },
				responseType: 'blob'
			})
			.then((data) => {
				resolve(data);
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 导出储能功率和SoC
export const cnExportActivePowerAndSoC = (params) => {
	let url = `/api/cn/exportActivePowerAndSoC`;
	return new Promise((resolve, reject) => {
		http
			.post(url, params, {
				headers: { 'content-type': 'application/json; charset=utf-8' },
				responseType: 'blob'
			})
			.then((data) => {
				resolve(data);
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};

// 预测分析图表接口
export const statPrediction = (params) => {
	let url = '/api/pred/statPrediction';
	return new Promise((resolve, reject) => {
		http
			.post(url, params)
			.then((data) => {
				if (data.code == 200) {
					resolve(data.data);
				} else {
					reject({ message: data.msg });
				}
			})
			.catch((errorInfo) => {
				reject({ message: errorInfo });
			});
	});
};
