import http from '/tool/http.js';

// 根据输入信息查询设备列表
export const getDeviceListHttp = (params) => {
    let url = "/api/dev/queryDevList";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200) {
                resolve({
                    list: data.data.records,
                    total: data.data.total
                })
            } 
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}


//  查询不同在线设备数量
export const getDeviceNumberHttp = (params) => {
    let url = "/api/dev/queryDevsInfo";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
            if (data.code == 200) {
                resolve(data.data)
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}