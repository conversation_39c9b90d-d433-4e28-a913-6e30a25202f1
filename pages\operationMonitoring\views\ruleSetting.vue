<template>
	<div class="page_all rule_setting">
		<div class="third_menu_contain">
			<div
				:class="['third_menu_item', currentThirdMenu == 0 ? 'active' : '']"
				@click="menuChange(0)"
			>
				<span class="third_menu_name">异常查询</span>
				<span class="third_menu_line"></span>
			</div>
			<div
				:class="['third_menu_item', currentThirdMenu == 1 ? 'active' : '']"
				@click="menuChange(1)"
			>
				<span class="third_menu_name">规则配置</span>
				<span class="third_menu_line"></span>
			</div>
		</div>
		<div class="main_contain main_contain_thirdMenu">
			<div class="main_contain_scroll">
				<!-- 搜索条件 -->
				<div class="search_input_reset">
					<a-form
						ref="form_setting"
						layout="inline"
						:model="rule_form_state"
					>
						<a-form-item
							name="ruleName"
							:rules="[
								{
									required: false,
									validator: (rule, value) => {
										return chEnNum(value);
									},
									message: '规则名称仅支持中英文数字'
								}
							]"
						>
							<a-input
								autocomplete="off"
								v-model:value="rule_form_state.ruleName"
								placeholder="请输入规则名称"
							></a-input>
						</a-form-item>
						<a-form-item
							label="创建时间"
							name="time"
							:rules="[{ required: false, message: '时间不能为空' }]"
						>
							<a-range-picker
								v-model:value="rule_form_state.time"
								style="width: 300px"
								:allowClear="false"
							>
								<template #suffixIcon>
									<img
										style="width: 18px; height: 18px"
										src="/assets/icon/clock.png"
										alt="#"
									/>
								</template>
							</a-range-picker>
						</a-form-item>
						<a-form-item
							label="告警级别"
							name="warn_level"
						>
							<a-select
								v-model:value="warnLevel_table"
								:options="warning_level_list_all"
								:getPopupContainer="(e) => e.parentNode"
								ref="select"
								placeholder="告警级别"
								style="width: 126px"
							></a-select>
						</a-form-item>
						<a-row>
							<a-col>
								<a-form-item
									class="triggerCount"
									label="触发频次"
									name="triggerCountLower"
									:rules="[
										{
											required: false,
											validator: (rule, value) => {
												return positiveInteger(value);
											},
											message: '仅支持正整数'
										}
									]"
								>
									<a-input
										autocomplete="off"
										v-model:value="rule_form_state.triggerCountLower"
										placeholder="请输入下限"
										style="width: 100px"
									></a-input>
								</a-form-item>
							</a-col>
							<a-col style="height: 36px; line-height: 36px; text-align: center; margin: 0 8px">
								-
							</a-col>
							<a-col>
								<a-form-item
									name="triggerCountUpper"
									:rules="[
										{
											required: true,
											validator: (rule, value) => {
												return positiveInteger(value);
											},
											message: '仅支持正整数'
										}
									]"
								>
									<a-input
										autocomplete="off"
										v-model:value="rule_form_state.triggerCountUpper"
										placeholder="请输入上限"
										style="width: 100px"
									></a-input>
								</a-form-item>
							</a-col>
						</a-row>

						<a-form-item>
							<a-button
								type="primary"
								@click="search"
								style="margin-right: 15px"
							>
								查询
							</a-button>
							<a-button @click="resetInput">重置</a-button>
						</a-form-item>
					</a-form>
					<a-button
						style="float: right"
						type="primary"
						@click="openAddSettingDrawer('add')"
					>
						<template #icon>
							<img
								class="button_icon_add"
								src="/assets/icon/add.png"
								alt=""
							/>
						</template>
						新增
					</a-button>
				</div>

				<a-table
					:pagination="false"
					:columns="columns"
					:data-source="dataSource"
					:bordered="false"
				>
					<template #headerCell="{ title, column }">
						<template v-if="column.key === 'triggerCount'">
							<div
								class="table_header_sort"
								@click="tableSort"
							>
								{{ title }}
								<div class="table_header_sort_icon">
									<CaretUpOutlined></CaretUpOutlined>
									<CaretDownOutlined></CaretDownOutlined>
								</div>
							</div>
						</template>
					</template>

					<template #bodyCell="{ column, record }">
						<template v-if="column.key === 'judge_rule'">
							<a-tooltip placement="topLeft">
								<template #title>
									<span>{{ record[column.key] }}</span>
								</template>
								<div
									class="ellipsis_table"
									style="
										display: -webkit-box;
										max-width: 360px;
										max-height: 52px;
										border-radius: 18px !important;
										line-height: 26px !important;
										padding: 0 2px;
									"
								>
									<span>{{ record[column.key] }}</span>
								</div>
							</a-tooltip>
						</template>

						<template v-if="column.key === 'warnLevel_table'">
							<div
								style="margin: 0 auto"
								:class="[
									record['warnLevel'] == 1 ? 'error_tag' : '',
									record['warnLevel'] == 2 ? 'warning_tag' : '',
									record['warnLevel'] == 3 ? 'normal_tag' : '',
									record['warnLevel'] == 4 ? 'prompt_tag' : ''
								]"
							>
								{{ record['warnLevelDesc'] }}
							</div>
						</template>
						<template v-if="column.key === 'deviceName'">
							<div class="device_tips_all">
								{{ record[column.key] }}
								<!-- <div class="error_tag device_tips">设备已删除</div> -->
								<div
									v-if="record['deviceList'][0].deleteFlag == 1 ? true : false"
									class="error_tag device_tips"
								>
									<!-- <img
										style="height: 16px; width: 16px"
										src="/assets/icon/price_tips_error.png"
										alt=""
									/> -->
									设备已删除
								</div>
							</div>
						</template>
						<template v-if="column.key === 'status'">
							<a-switch
								@change="changeSwitch(record, record[column.key])"
								v-model:checked="record[column.key]"
							/>
						</template>
						<!-- <template v-if="column.key === 'triggerCount'"
              ><a @click="showCountTable(record)" href="javascript:void(0)">{{
                record[column.key]
              }}</a></template
            > -->
						<template v-if="column.key === 'control'">
							<a
								href="javascript:void(0)"
								style="margin-right: 20px"
								@click="openAddSettingDrawer(record)"
							>
								编辑
							</a>

							<a
								href="javascript:void(0)"
								@click="showDeleteEdge(record)"
								style="margin-right: 20px"
							>
								删除
							</a>
							<DeleteModal
								ref="deleteModal_setting"
								@deleteConfirm="deleteRule(record)"
							></DeleteModal>
						</template>
					</template>
				</a-table>

				<Pagination
					ref="pagination"
					@changePageSize="changePageSize"
					:totalNum="totalNum"
				></Pagination>

				<!-- 触发详情的页面 -->
				<div :class="count_num_modal ? 'count_num_modal' : 'count_num_modal_hide'">
					<div class="contain_count">
						<div class="title_count">触发明细</div>
						<div>
							<a-table
								:pagination="false"
								:columns="columns_count_num"
								:data-source="data_count"
							></a-table>
							<Pagination
								ref="pagination_count"
								@changePageSize="changePageSizeCount"
								:totalNum="totalNumCount"
							></Pagination>
							<a-button
								@click="cancelCount"
								style="margin: 50px auto; display: block"
							>
								取消
							</a-button>
						</div>
					</div>
				</div>

				<!-- 新增和编辑的抽屉展示 -->
				<a-drawer
					v-model:open="addDrawerStatus"
					class="custom-class"
					root-class-name="root-class-name"
					:closable="true"
					:getContainer="false"
					:title="drawerTitle"
					placement="right"
					width="45%"
					@close="closeDrawer"
				>
					<a-form
						id="rule_setting_form"
						ref="rule_setting_form"
						:model="ruleSettingFormState"
						name="rule_setting_form"
						:label-col="{ span: 24 }"
						:wrapper-col="{ span: 24 }"
						autocomplete="off"
						labelAlign="left"
					>
						<a-form-item
							label="规则名称"
							name="ruleName"
							:rules="[
								{ required: true, message: '规则名称不能为空' },
								{
									validator: (rule, value) => {
										return ruleName(value);
									},
									message: '支持中英文数字，最多64个字'
								}
							]"
						>
							<a-input
								autocomplete="off"
								placeholder="请输入规则名称"
								v-model:value="ruleSettingFormState.ruleName"
							/>
						</a-form-item>

						<a-form-item
							label="边端控制器"
							name="controId"
							:rules="[{ required: true, message: '边端控制器不能为空' }]"
						>
							<a-select
								ref="select"
								v-model:value="ruleSettingFormState.controId"
								:getPopupContainer="(e) => e.parentNode"
								:options="edge_control_list"
								@select="selectEdgeControl"
								placeholder="请选择边端控制器"
							></a-select>
						</a-form-item>

						<a-form-item
							label="元素类型"
							name="elementType"
							:rules="[{ required: true, message: '元素类型不能为空' }]"
						>
							<a-select
								ref="select"
								v-model:value="ruleSettingFormState.elementType"
								:options="elementTypeList"
								:disabled="elementTypeList ? false : true"
								@select="selectElement"
								:getPopupContainer="(e) => e.parentNode"
								placeholder="请选择元素类型"
							></a-select>
						</a-form-item>
						<a-form-item
							label="设备类型"
							name="deviceType"
							:rules="[{ required: true, message: '设备类型不能为空' }]"
						>
							<a-select
								ref="select"
								:disabled="deviceTypeList ? false : true"
								v-model:value="ruleSettingFormState.deviceType"
								:options="deviceTypeList"
								@select="selectDeviceType"
								:getPopupContainer="(e) => e.parentNode"
								placeholder="请选择设备类型"
							></a-select>
						</a-form-item>
						<a-form-item
							label="适用设备"
							name="deviceIds"
							:rules="[{ required: true, message: '适用设备不能为空' }]"
						>
							<a-select
								ref="select"
								:disabled="deviceList ? false : true"
								v-model:value="ruleSettingFormState.deviceIds"
								:options="deviceList"
								:getPopupContainer="(e) => e.parentNode"
								placeholder="请选择适用设备"
							></a-select>
						</a-form-item>
						<a-form-item
							label="告警级别"
							name="warnLevel"
							:rules="[{ required: true, message: '告警级别不能为空' }]"
						>
							<a-select
								ref="select"
								:disabled="warning_level_list ? false : true"
								v-model:value="ruleSettingFormState.warnLevel"
								:options="warning_level_list"
								:getPopupContainer="(e) => e.parentNode"
								placeholder="请选择告警级别"
							></a-select>
						</a-form-item>

						<!-- 判断规则 -->
						<div class="form_self_normal">
							<div class="form_item">
								<div class="form_label">
									<i class="require_true">*</i>
									判断规则
								</div>
								<div class="form_value form_table_edit rule_setting_drawer">
									<div
										class="form_table_edit_item"
										v-for="(item, index) in columns_rule"
										:key="index"
									>
										<a-form
											:id="'form' + index"
											layout="inline"
											:model="item"
											style="width: 100%"
										>
											<a-form-item
												name="deviceType"
												style="width: 100%; min-width: 90px"
											>
												<a-select
													ref="select"
													v-model:value="item.paramId"
													:options="paramId_list"
													:getPopupContainer="(e) => e.parentNode"
													placeholder="请输入数据项"
												></a-select>
											</a-form-item>
											<a-form-item
												name="deviceType"
												style="width: 100%; min-width: 90px"
											>
												<a-select
													ref="select"
													placeholder="请输入规则类型"
													v-model:value="item.determine"
													:getPopupContainer="(e) => e.parentNode"
													:options="num_rule_list"
												></a-select>
											</a-form-item>
											<a-form-item
												name="threshold"
												style="width: 100%; min-width: 90px"
											>
												<a-input
													autocomplete="off"
													placeholder="请输入判断阈值"
													v-model:value="item.threshold"
												/>
											</a-form-item>
											<a-form-item name="control">
												<div class="add_delete_table">
													<div class="add_delete_table_item">
														<div
															v-if="columns_rule.length == index + 1"
															@click="add_line"
															class="add_delete_button add_table"
														></div>
														<div
															@click="delete_line(index)"
															class="add_delete_button delete_table"
														></div>
													</div>
												</div>
											</a-form-item>
										</a-form>
									</div>
								</div>
							</div>
						</div>
					</a-form>
					<template #footer>
						<a-space
							class="drawer_footer"
							align="center"
						>
							<a-button @click="cancelDrawer">取消</a-button>
							<a-button
								:loading="loading_status"
								@click="uploadDrawer"
								type="primary"
							>
								确定
							</a-button>
						</a-space>
					</template>
				</a-drawer>
			</div>
		</div>
	</div>
</template>
<script setup>
	import { ref, onMounted, reactive, watch } from 'vue';
	import { message } from 'ant-design-vue';
	// import zhCN from 'ant-design-vue/es/locale/zh_CN'
	import { useRoute, useRouter } from 'vue-router';
	import { chEnNum, positiveInteger, ruleName, isNumber, NumberFloat2 } from '/assets/js/reg.js';
	import {
		getDictHttp,
		getDeviceListHttp,
		getEageDeviceListHttp,
		getEdgeSimpleListHttp,
		getDeviceTypeRelAssetSimpleList
	} from '/assets/js/commonApi.js';
	import {
		getRuleSettingHttp,
		getEdgeElementSimpleListHttp,
		getDeviceSimpleListHttp,
		addRuleHttp,
		editRuleHttp,
		getByDeviceTypeHttp,
		getRuleDetailByIdHttp,
		deleteRuleHttp
	} from './api.js';
	import DeleteModal from '/components/ModalDelete.vue';
	import dayjs from 'dayjs';
	// 分页的结构
	import Pagination from '/components/Pagination.vue';
	import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons-vue';

	// ***********************************************************************************************************
	//                                                   全局配置                                                //
	// ***********************************************************************************************************

	const router = useRouter();
	const route = useRoute();
	// 菜单切换
	const currentThirdMenu = ref(1);
	const menuChange = (num) => {
		currentThirdMenu.value = num;
		if (num) {
			// 规则配置
			router.push('/ruleSetting');
		} else {
			// 异常查询
			router.push('/unNormalControl');
		}
	};
	const project_id = ref(null);
	// ***********************************************************************************************************
	//                                                   全局配置                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                   table告警级别                                            //
	// ***********************************************************************************************************

	const warnLevel_table = ref(null);
	// const handleChangeWarning = (value) => {
	//   warnLevel_table.value = value
	// }

	// ***********************************************************************************************************
	//                                                   table告警级别                                            //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                   搜索查询                                                //
	// ***********************************************************************************************************
	const pagination = ref();
	const totalNum = ref(0); // 表格数据总数
	const pageIndex = ref(1);
	const pageSize = ref(10);
	const changePageSize = (currentPageMess) => {
		pageSize.value = currentPageMess.currentPageSize;
		pageIndex.value = currentPageMess.currentPage;
		search();
	};

	const rule_form_state = reactive({
		ruleName: null,
		time: [null, null],
		triggerCountLower: null,
		triggerCountUpper: null
	});

	const form_setting = ref();

	const search = () => {
		let createTimeEnd = null;
		let createTimeStart = null;
		rule_form_state.time[0]
			? (createTimeStart = dayjs(rule_form_state.time[0])
					.startOf('day')
					.format('YYYY-MM-DD HH:mm:ss'))
			: null;
		rule_form_state.time[1]
			? (createTimeEnd = dayjs(rule_form_state.time[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss'))
			: null;

		let params = {
			pageIndex: pageIndex.value,
			pageSize: pageSize.value,
			projectId: project_id.value
		};
		// 参数处理如果没有写就不传参
		createTimeEnd ? (params.createTimeEnd = createTimeEnd) : null;
		createTimeStart ? (params.createTimeStart = createTimeStart) : null;
		rule_form_state.ruleName ? (params.ruleName = rule_form_state.ruleName) : null;
		rule_form_state.triggerCountLower
			? (params.triggerCountLower = rule_form_state.triggerCountLower)
			: null;
		rule_form_state.triggerCountUpper
			? (params.triggerCountUpper = rule_form_state.triggerCountUpper)
			: null;
		warnLevel_table.value ? (params.warnLevel = warnLevel_table.value) : null;
		// 分页查询规则配置
		getRuleSettingHttp(params)
			.then((data) => {
				// console.log('查询出的数据')
				// console.log(data)
				totalNum.value = data.total;
				data.list.forEach((item, index) => {
					let judge_rule = '';
					item.status == 1 ? (item.status = true) : (item.status = false);
					item.detailList.forEach((item1) => {
						judge_rule = `${judge_rule}${item1.paramName}${item1.determineDesc}${item1.threshold},`;
					});
					// console.log(judge_rule);
					judge_rule = judge_rule.substring(0, judge_rule.length - 1);
					item.judge_rule = judge_rule;
					item.no = index + 1;
					let deviceName = '';
					item.deviceList.forEach((item_device) => {
						deviceName = deviceName + item_device.deviceName;
					});
					item.deviceName = deviceName;
				});
				dataSource.value = data.list;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	const resetInput = () => {
		form_setting.value.clearValidate();
		form_setting.value.resetFields();
		pageSize.value = 10;
		pageIndex.value = 1;
		// 重置分页的数据
		pagination.value.reset();
		//  告警级别的处理
		// let temp_level = JSON.parse(JSON.stringify(columns.value))
		// columns.value = []
		// columns.value = temp_level
		// // let temp_warn_level = warning_level_list_all.value
		// // warning_level_list_all.value = []
		// // warning_level_list_all.value = temp_warn_level
		warnLevel_table.value = null;
		search();
	};

	// ***********************************************************************************************************
	//                                                   搜索查询                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                   是否启用                                                //
	// ***********************************************************************************************************

	const changeSwitch = (item, result) => {
		// 状态 0.禁用 1.启用
		let params = { id: item.id, status: result ? 1 : 0 };
		editRuleHttp(params)
			.then((data) => {
				if (data) {
					message.success(data);
				}
				search();
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
		//
	};

	// ***********************************************************************************************************
	//                                                   是否启用                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                   出发频次                                                //
	// ***********************************************************************************************************
	const count_num_modal = ref(false);
	const columns_count_num = ref([
		{ name: 'Name', dataIndex: 'name', key: 'name' },
		{ title: 'Age', dataIndex: 'age', key: 'age' },
		{ title: 'Address', dataIndex: 'address', key: 'address' },
		{ title: 'Tags', key: 'tags', dataIndex: 'tags' },
		{ title: 'Action', key: 'action' }
	]);
	const data_count = ref([
		{
			key: '1',
			name: 'John Brown',
			age: 32,
			address: 'New York No. 1 Lake Park',
			tags: ['nice', 'developer']
		},
		{ key: '2', name: 'Jim Green', age: 42, address: 'London No. 1 Lake Park', tags: ['loser'] },
		{
			key: '3',
			name: 'Joe Black',
			age: 32,
			address: 'Sidney No. 1 Lake Park',
			tags: ['cool', 'teacher']
		}
	]);
	const pagination_count = ref();
	const totalNumCount = ref(0); // 表格数据总数
	const pageIndexCount = ref(1);
	const pageSizeCount = ref(10);
	const changePageSizeCount = (currentPageMess) => {
		pageSizeCount.value = currentPageMess.currentPageSize;
		pageIndexCount.value = currentPageMess.currentPage;
		getNunCountData();
	};
	const showCountTable = (record) => {
		count_num_modal.value = true;
	};
	const cancelCount = () => {
		count_num_modal.value = false;
		getNunCountData();
	};

	const getNunCountData = () => {};

	// ***********************************************************************************************************
	//                                                   出发频次                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                   新增编辑删除规则                                                //
	// ***********************************************************************************************************

	// 新增抽屉
	const addDrawerStatus = ref(false);
	const drawerTitle = ref('新增');

	// 规则的信息表单

	// ------------------------------------form表单
	const ruleSettingFormState = reactive({
		ruleName: null,
		elementType: null,
		deviceType: null,
		deviceIds: null,
		warnLevel: null,
		controId: null
	});

	// 表单中的是可编辑设备列表
	const columns_rule = ref([{ paramId: null, determine: null, threshold: null }]);

	// ------------------------规则新增

	const add_line = () => {
		columns_rule.value.push({ paramId: null, determine: null, threshold: null });
	};

	const delete_line = (index) => {
		columns_rule.value.splice(index, 1);
		if (columns_rule.value.length == 0) {
			add_line();
		}
	};
	// -------------------------规则新增

	// ------------------------------------form表单

	// ------------------------------------------项目下边缘控制器列表
	const edge_control_list = ref(null);
	const getEdgeControlList = () => {
		let params = project_id.value;
		getEdgeSimpleListHttp(params)
			.then((data) => {
				let result = [];
				for (let index = 0; index < data.length; index++) {
					result.push({ label: data[index].controName, value: data[index].controId });
				}
				edge_control_list.value = result;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	// --------------------------------------元素类型列表
	const elementTypeList = ref([]);
	const selectEdgeControl = async () => {
		// 置空 元素 设备类型  设备
		ruleSettingFormState.elementType = null;
		ruleSettingFormState.deviceType = null;
		ruleSettingFormState.deviceIds = null;

		let params = ruleSettingFormState.controId;

		await getEdgeElementSimpleListHttp(params)
			.then((data) => {
				let result = [];
				for (let index = 0; index < data.length; index++) {
					result.push({ label: data[index].elementName, value: data[index].elementCode });
				}
				elementTypeList.value = result;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	// --------------------------------------元素类型列表

	// ------------------------------------设备类型

	const deviceTypeList = ref(null);
	const selectElement = async () => {
		ruleSettingFormState.deviceType = null;
		ruleSettingFormState.deviceIds = null;
		// console.log(value)
		// let params = ruleSettingFormState.elementType
		let params = {
			controId: ruleSettingFormState.controId,
			elementCode: ruleSettingFormState.elementType
		};
		// await getDictHttp(params)
		await getDeviceTypeRelAssetSimpleList(params)
			.then((data) => {
				let result = [];
				// console.log(data)
				for (let index = 0; index < data.length; index++) {
					result.push({ label: data[index].dictValue, value: data[index].dictKey });
				}
				deviceTypeList.value = result;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	// -------------------------------------设备类型

	// ---------------------------------根据边缘控制器元素和设备类型获取设备列表

	const deviceList = ref(null);
	const paramId_list = ref(null); //判断规则类型列表
	const selectDeviceType = async () => {
		// 获取设备列表
		ruleSettingFormState.deviceIds = null;
		let params = {
			controId: ruleSettingFormState.controId,
			deviceType: ruleSettingFormState.deviceType,
			elementCode: ruleSettingFormState.elementType
		};
		await getDeviceSimpleListHttp(params)
			.then((data) => {
				let result = [];

				for (let index = 0; index < data.length; index++) {
					result.push({ label: data[index].deviceName, value: data[index].deviceId });
				}
				deviceList.value = result;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});

		// 获取量测类型列表
		let params1 = ruleSettingFormState.deviceType;
		await getByDeviceTypeHttp(params1)
			.then((data) => {
				let result = [];
				for (let index = 0; index < data.length; index++) {
					result.push({ label: data[index].paramName, value: data[index].paramId });
				}
				paramId_list.value = result;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	// ---------------------------------根据边缘控制器元素和设备类型获取设备列表

	// --------------------------------------判断规则列表

	const num_rule_list = ref(null);
	// 获取判断规则的下拉列表
	const getJudgeMentList = () => {
		// RULE_JUDGMENT  传参  判断规则
		let params = 'RULE_JUDGMENT';
		getDictHttp(params).then((data) => {
			let result = [];
			for (let index = 0; index < data.length; index++) {
				result.push({ label: data[index].dictValue, value: data[index].dictKey });
			}
			num_rule_list.value = result;
		});
	};

	// --------------------------------------判断规则列表

	// --------------------------------------报警级别

	const warning_level_list = ref(null);
	const warning_level_list_all = ref([]);
	const getWarningLevel = async () => {
		let params = 'WARN_LEVEL';
		await getDictHttp(params).then((data) => {
			let result = [];
			for (let index = 0; index < data.length; index++) {
				result.push({ label: data[index].dictValue, value: data[index].dictKey });
			}
			warning_level_list.value = result;

			warning_level_list_all.value = result;
			// warning_level_list_all.value.unshift({
			//   label: '全部',
			//   value: '0'
			// })
		});
	};

	// --------------------------------------报警级别

	// --------------------------------------新增和编辑规则的抽屉
	const ruleId = ref(null); // 所编辑规则的id
	const openAddSettingDrawer = (mess) => {
		console.log(mess);
		if (mess === 'add') {
			// 设置编辑操作的规则ID为空
			ruleId.value = null;
			elementTypeList.value = null;
			deviceTypeList.value = null;
			deviceList.value = null;

			drawerTitle.value = '新增';
		} else {
			ruleId.value = mess.id;
			// 表示是编辑
			drawerTitle.value = '编辑';
			// 初始赋值

			// ruleName
			// warnLevel
			// 根据设备类型获取 规则
			let params = ruleId.value;
			getRuleDetailByIdHttp(params)
				.then((data) => {
					// form的赋值
					ruleSettingFormState.ruleName = data.ruleName;
					ruleSettingFormState.controId = data.controId;
					// 根据边缘控制器id查询元素列表
					selectEdgeControl();
					ruleSettingFormState.elementType = data.elementCode;
					// 根据元素类型查询设备类型
					selectElement();
					ruleSettingFormState.deviceType = data.deviceType;
					// 根据设备类型查询设备列表
					selectDeviceType();

					if (data.deviceList[0].deleteFlag == 1) {
						ruleSettingFormState.deviceIds = null;
					} else {
						ruleSettingFormState.deviceIds = data.deviceList[0].deviceId;
					}
					// 预警级别表已经初始化过
					ruleSettingFormState.warnLevel = String(data.warnLevel);

					// 规则的初始赋值
					let temp_columns_rule = [];
					data.detailList.forEach((item) => {
						let { paramId, determine, threshold } = item;
						temp_columns_rule.push({
							paramId: String(paramId),
							determine: String(determine),
							threshold: String(threshold)
						});
					});
					columns_rule.value = temp_columns_rule;
				})
				.catch((error) => {
					message.error(error.message);
					setTimeout(() => {
						message.destroy();
					}, 1500);
				});
		}

		addDrawerStatus.value = true;
	};
	// ---------------------------------------删除规则
	const deleteModal_setting = ref();
	const deleteId = ref(null);
	const showDeleteEdge = (mess) => {
		deleteId.value = mess.id;
		deleteModal_setting.value.showHideDeleteModal();
	};

	const deleteRule = (mess) => {
		let params = deleteId.value;
		console.log(mess);
		console.log(params);
		deleteModal_setting.value.changeLoading();
		deleteRuleHttp(params)
			.then((data) => {
				message.success(data);
				deleteModal_setting.value.showHideDeleteModal();
				// 重新请求
				deleteModal_setting.value.changeLoading();
				deleteModal_setting.value.showHideDeleteModal();
				search();
			})
			.catch((error) => {
				message.error(error.message);
				deleteModal_setting.value.changeLoading();
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};
	// ---------------------------------------删除规则

	// 关闭抽屉的回调
	const rule_setting_form = ref();
	const closeDrawer = () => {
		// 重置参数

		rule_setting_form.value.resetFields();
		rule_setting_form.value.clearValidate();
		columns_rule.value = [{ paramId: null, determine: null, threshold: null }];
		ruleId.value = null;
	};

	// 取消抽屉
	const cancelDrawer = () => {
		addDrawerStatus.value = false;
	};

	const validateRuleList = () => {
		let flag = true;
		columns_rule.value.forEach((item) => {
			if (!item.paramId || !item.determine || !item.threshold) {
				message.error('规则列表不能为空');
				flag = false;
				return;
			} else {
				if (!NumberFloat2(item.threshold)) {
					message.error('只支持数字，最多保留两位小数');
					flag = false;
					return;
				}
			}
		});
		return flag;
	};

	// 提交 新增规则
	const loading_status = ref(false);
	const uploadDrawer = () => {
		loading_status.value = true;
		rule_setting_form.value
			.validateFields()
			.then(() => {
				// 校验规则列表
				if (validateRuleList()) {
					let params = handleParams();
					// 编辑的数据
					if (ruleId.value) {
						editRuleHttp(params)
							.then((data) => {
								message.success(data);
								loading_status.value = false;
								closeDrawer();

								ruleId.value = null;
								addDrawerStatus.value = false;
								search();
							})
							.catch((error) => {
								loading_status.value = false;
								message.error(error.message);
								setTimeout(() => {
									message.destroy();
								}, 1500);
							});
					} else {
						// 新增的数据
						addRuleHttp(params)
							.then((data) => {
								message.success(data);
								loading_status.value = false;
								closeDrawer();

								// 清空表格
								addDrawerStatus.value = false;
								search();
							})
							.catch((error) => {
								loading_status.value = false;
								message.error(error.message);
								setTimeout(() => {
									message.destroy();
								}, 1500);
							});
					}
				} else {
					loading_status.value = false;
				}
			})
			.catch((error) => {
				loading_status.value = false;
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	const handleParams = () => {
		let temp_data = [];
		columns_rule.value.forEach((item) => {
			temp_data.push({
				determine: Number(item.determine),
				paramId: Number(item.paramId),
				threshold: Number(item.threshold)
			});
		});

		let params = {
			detailList: temp_data,
			deviceIds: [ruleSettingFormState.deviceIds],
			warnLevel: Number(ruleSettingFormState.warnLevel),
			ruleName: ruleSettingFormState.ruleName
		};
		if (ruleId.value) {
			params.id = ruleId.value;
		} else {
			params.projectId = Number(project_id.value);
			// params.ruleName = ruleSettingFormState.ruleName
		}
		return params;
	};

	// ***********************************************************************************************************
	//                                                   新增编辑删除规则                                               //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                   table结果展示                                           //
	// ***********************************************************************************************************
	// 表格的数据
	const columns = ref([
		{ title: '序号', key: 'no', dataIndex: 'no', align: 'center', width: 60 },
		{ title: '规则名称', key: 'ruleName', dataIndex: 'ruleName', ellipsis: true, width: 300  },
		{ title: '适用设备', key: 'deviceName', dataIndex: 'deviceName', width: 300 },
		{ title: '判断规则', key: 'judge_rule', dataIndex: 'judge_rule', width: 180 },
		{ title: '告警级别', key: 'warnLevel_table', dataIndex: 'warnLevel_table', align: 'center', width: 100 },
		{ title: '是否启用', key: 'status', dataIndex: 'status', align: 'center', width: 100 },
		{ title: '创建人', key: 'createUser', dataIndex: 'createUser', width: 100 },
		{ title: '创建时间', key: 'createTime', dataIndex: 'createTime', width: 150 },
		{
			title: '触发频次',
			width: 120,
			key: 'triggerCount',
			dataIndex: 'triggerCount',
			align: 'center'
			// sorter: (a, b) => {}
		},
		{ title: '操作', key: 'control', dataIndex: 'control', width: 200 }
	]);
	const dataSource = ref([]);

	const sort_table = ref(0);
	const tableSort = () => {
		// 排序
		let result = dataSource.value;
		if (sort_table.value == 0) {
			sort_table.value = 1;
			result.sort((a, b) => {
				return a.triggerCount - b.triggerCount;
			});
		} else {
			sort_table.value = 0;
			result.sort((a, b) => {
				return b.triggerCount - a.triggerCount;
			});
		}
		for (let i = 0; i < result.length; i++) {
			result[i].no = i + 1;
		}
		dataSource.value = result;
	};

	// ***********************************************************************************************************
	//                                                    table结果展示                                               //
	// ***********************************************************************************************************

	// 确认提交
	const confirmUpload = () => {};

	onMounted(() => {
		// 获取地址参数
		// console.log(route.query)
		// if(route.query && route.query.skip == 1){
		//   // 其他页面直接跳转过来的，需要配合进行菜单的选中状态修改
		//   console.log(1111111111);

		// }

		// 项目id
		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			project_id.value = project_mess.project_id;
		} else {
			message.error('未选择项目，请重新选择！');
		}

		// 根据项目id查询边缘控制器列表
		getEdgeControlList();
		// 获取判断规则的下拉列表
		getJudgeMentList();
		// 获取预警列表
		getWarningLevel();
		// 初始化数据
		search();
	});
</script>
<style lang="less" scoped>
	.rule_setting {
		padding-top: 32px;
		box-sizing: border-box;
		box-sizing: border-box;
	}
	.search_input_reset {
		width: 100%;
		height: 64px;
		box-sizing: border-box;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		.time_label {
			font-family: SourceHanSansCN-Regular;
			font-weight: 400;
			font-size: 14px;
			color: #092649;
		}
	}

	.ant-form-item.triggerCount {
		margin-inline-end: 0px !important;
	}

	.count_num_modal {
		display: block;
		height: 100%;
		width: 100%;
		position: absolute;
		top: 0;
		left: 0;
		z-index: 99;
		background-color: rgba(0, 0, 0, 0.6);
		.contain_count {
			padding: 36px;
			background: rgba(255, 255, 255, 1);
			box-shadow: 0px 1px 0px 0px #e9edf5;
			border-radius: 8px;
			width: 800px;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}
		.title_count {
			height: 48px;
			line-height: 48px;
			text-align: center;
			font-family: SourceHanSansCN-Bold;
			font-weight: bold;
			font-size: 22px;
			color: #092649;
			margin-bottom: 36px;
		}
	}
	.count_num_modal_hide {
		display: none;
	}
	.device_tips_all {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		.device_tips {
			margin-left: 5px;
			display: flex;
			flex-direction: row;
			justify-content: space-evenly;
			align-items: center;
			width: 92px;
			box-sizing: border-box;
		}
	}
	.ant-table-wrapper {
		flex: 1 !important;
	}
	.error_tag.device_tips {
		min-width: 92px;
	}
</style>
<style>
	.form_table_edit.rule_setting_drawer {
		position: relative;
		.add_delete_table {
			/* padding-top: 58.14px; */
			height: 100%;
			width: 100%;
			/* position: absolute;
    right: 0;
    top: 0; */
			width: 70px;
			.add_delete_table_item {
				/* padding: 0 0 0 16px; */
				height: 36px;
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
			}
		}
		.form_table_edit_item {
			margin-bottom: 10px;
			.ant-form {
				display: flex;
				flex-direction: row;
				justify-content: flex-start;
				.ant-form-item {
					flex: 1;
					&:last-child {
						flex: none;
						width: 70px !important;
						margin-right: 0 !important;
					}
				}
			}
		}
	}
	.add_delete_button {
		width: 26px;
		height: 26px;
		border-radius: 50%;
		cursor: pointer;
	}
	.add_table {
		background-image: url('/assets/icon/rule_add.png');
		background-repeat: no-repeat;
		background-size: 100%;
		&:hover {
			background-image: url('/assets/icon/rule_add_hover.png');
		}
	}
	.delete_table {
		background-image: url('/assets/icon/rule_delete.png');
		background-repeat: no-repeat;
		background-size: 100%;
		&:hover {
			background-image: url('/assets/icon/rule_delete_hover.png');
		}
	}

	.ant-form-item .ant-form-item-label > label {
		min-width: unset;
	}
</style>
<style>
	.ant-tooltip {
		width: unset;
	}
	.ant-tooltip-content {
		width: unset;
	}
</style>
