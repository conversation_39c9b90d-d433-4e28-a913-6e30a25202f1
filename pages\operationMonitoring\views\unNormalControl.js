import http from '/tool/http.js';

// 查询异常列表
export const getUnnormalListHttp = (params) => {

    let url = "/api/dev/queryDevList";
    return new Promise((resolve, reject) => {
        http.post(url, params).then((data) => {
          
            if (data.code == 200 && data.data.records.length > 0) {
                resolve(data.data)
            } else {
                // 空数据
            }
        }).catch((errorInfo) => {
            reject({
                    message:errorInfo
                })
        });
    })
}