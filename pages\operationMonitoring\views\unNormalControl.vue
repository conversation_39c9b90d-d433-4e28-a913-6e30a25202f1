<template>
	<div class="page_all current_page">
		<div class="third_menu_contain">
			<div
				:class="['third_menu_item', currentThirdMenu == 0 ? 'active' : '']"
				@click="menuChange(0)"
			>
				<span class="third_menu_name">异常查询</span>
				<span class="third_menu_line"></span>
			</div>
			<div
				:class="['third_menu_item', currentThirdMenu == 1 ? 'active' : '']"
				@click="menuChange(1)"
			>
				<span class="third_menu_name">规则配置</span>
				<span class="third_menu_line"></span>
			</div>
		</div>

		<div class="main_contain main_contain_thirdMenu">
			<div class="main_contain_scroll">
				<!-- 菜单title -->
				<!-- <div class="page_header_title">基础配置</div> -->

				<!-- 搜索条件 -->
				<div class="search_input_reset">
					<span class="time_label">时间选择：</span>
					<a-range-picker
						:status="time_status ? 'error' : ''"
						:allowClear="false"
						v-model:value="time_value"
						style="width: 300px"
					>
						<template #suffixIcon>
							<img
								style="width: 18px; height: 18px"
								src="/assets/icon/clock.png"
								alt="#"
							/>
						</template>
					</a-range-picker>
					<!--  <a-input
            autocomplete="off"
            style="width: 370px"
            v-model:value="search_value_input"
            placeholder="请输入设备名称或id"
          /> -->
					<span
						class="time_label"
						style="margin-left: 15px"
					>
						告警级别：
					</span>
					<a-select
						:options="warning_level_list_all"
						v-model:value="warnLevel_table"
						name="warn_select"
						ref="select"
						placeholder="告警级别"
						:getPopupContainer="(e) => e.parentNode"
						style="width: 126px"
					></a-select>
					<span
						class="time_label"
						style="margin-left: 15px"
					>
						处理状态：
					</span>
					<a-select
						:options="warning_status_list_all"
						v-model:value="warn_status_table"
						ref="warnStatus"
						:getPopupContainer="(e) => e.parentNode"
						placeholder="处理状态"
						name="warnStatus"
						style="width: 126px"
					></a-select>
					<a-button
						@click="searchAll"
						:loading="search_loading"
						:disabled="time_status"
						style="margin-left: 15px"
						type="primary"
					>
						查询
					</a-button>
					<a-button
						@click="resetInput"
						style="margin-left: 15px"
					>
						重置
					</a-button>
				</div>

				<div class="total_count">
					<div class="card_item">
						<div class="card_item_contain">
							<div class="card_title">异常触发累计</div>
							<div class="card_num">
								<span>{{ unnormal_sum }}</span>
								<span>条</span>
							</div>
						</div>

						<div class="device_icon"></div>
					</div>
					<div class="card_item">
						<div class="card_item_contain">
							<div class="card_title">告警级别({{ warning_level_1[0].warnLevelDesc }})</div>
							<div class="card_num">
								<span>{{ warning_level_1[0].totalNum }}</span>
								<span>条</span>
							</div>
							<div class="card_detail">
								<span
									v-for="(item, index) in warning_level_2"
									:key="index"
								>
									{{ item.warnLevelDesc }}
									<i>{{ item.totalNum }}</i>
									条
								</span>
							</div>
						</div>
						<div class="device_icon"></div>
					</div>
					<div class="card_item">
						<div class="card_item_contain">
							<div class="card_title">处理状态({{ handle_status_mess_1[0].warnStatusDesc }})</div>
							<div class="card_num">
								<span>{{ handle_status_mess_1[0].totalNum }}</span>
								<span>条</span>
							</div>
							<div class="card_detail">
								<span
									v-for="(item, index) in handle_status_mess_2"
									:key="index"
								>
									{{ item.warnStatusDesc }}
									<i>{{ item.totalNum }}</i>
									条
								</span>
							</div>
						</div>
						<div class="device_icon"></div>
					</div>
					<div class="card_item">
						<div class="card_item_contain">
							<div class="card_title">触发频次(大于500次)</div>
							<div class="card_num">
								<span>
									<i v-if="frequency_mess.length">{{ frequency_mess[2].totalNum }}</i>
								</span>
								<span>条</span>
							</div>
							<div class="card_detail">
								<span>
									100-500次
									<i v-if="frequency_mess.length">{{ frequency_mess[1].totalNum }}</i>
									条
								</span>
								<span>
									小于100次
									<i v-if="frequency_mess.length">{{ frequency_mess[0].totalNum }}</i>
									条
								</span>
							</div>
						</div>
						<div class="device_icon"></div>
					</div>
				</div>

				<!-- 搜索结果列表 -->
				<a-table
					:pagination="false"
					:columns="columns"
					:data-source="dataSource"
					:bordered="false"
				>
					<template #bodyCell="{ column, record }">
						<template v-if="column.key === 'warnContent'">
							<a-tooltip
								placement="topLeft"
								:title="record.warnContent"
							>
								<span
									class="ellipsis_table"
									style="
										display: -webkit-box;
										max-width: 360px;
										max-height: 52px;
										border-radius: 18px !important;
										line-height: 26px !important;
										padding: 0 2px;
									"
								>
									{{ record.warnContent }}
								</span>
							</a-tooltip>
						</template>
						<template v-if="column.key === 'warnLevelDesc'">
							<span
								style="
									display: block;
									width: 46px;
									height: 26px;
									border-radius: 18px !important ;
									margin: 0 auto;
									line-height: 26px !important;
								"
								:class="[
									record.warnLevel == 1 ? 'error_tag' : '',
									record.warnLevel == 2 ? 'warning_tag' : '',
									record.warnLevel == 3 ? 'normal_tag' : '',
									record.warnLevel == 4 ? 'success_tag' : ''
								]"
							>
								{{ record.warnLevelDesc }}
							</span>
						</template>
						<template v-if="column.key === 'warnStatusDesc'">
							<span
								style="
									display: block;
									width: 66px;
									height: 26px;
									border-radius: 18px !important;
									margin: 0 auto;
									line-height: 26px !important;
								"
								:class="[
									record.warnStatus == 3 ? 'success_tag' : '',
									record.warnStatus == 1 ? 'error_tag' : ''
								]"
							>
								{{ record.warnStatusDesc }}
							</span>
						</template>
						<template v-if="column.key === 'control'">
							<!-- 处理状态 为 1  未处理  展示处理 -->
							<a
								href="javascript:void(0)"
								style="margin-right: 20px"
								:class="[record.warnStatus != 1 ? 'disabled' : '']"
								@click="handleDrawerOpen(record)"
							>
								处理
							</a>
							<!-- 处理状态 为 3  已处理  展示处理结果 -->
							<a
								href="javascript:void(0)"
								style="margin-right: 20px"
								:class="[record.warnStatus != 3 ? 'disabled' : '']"
								@click="handleResultDrawerOpen(record)"
							>
								处理结果
							</a>
							<a
								href="javascript:void(0)"
								@click="ruleSetting(record)"
								style="margin-right: 20px"
							>
								规则配置
							</a>
						</template>
					</template>
				</a-table>
				<Pagination
					ref="pagination_ref"
					@changePageSize="changePageSize"
					:totalNum="totalNum"
				></Pagination>
			</div>
		</div>
		<!-- 处理操作抽屉 -->
		<a-drawer
			v-model:open="handle_drawer_status"
			class="custom-class"
			root-class-name="root-class-name"
			:closable="true"
			:getContainer="false"
			title="处理"
			placement="right"
			width="38.5%"
			@close="closeDrawer"
		>
			<a-form
				id="handle_form"
				ref="handle_form"
				:model="handle_formState"
				name="handle_form"
				:label-col="{ span: 24 }"
				:wrapper-col="{ span: 24 }"
				autocomplete="off"
				labelAlign="left"
				layout="horizontal"
			>
				<a-form-item
					label="规则名称"
					name="ruleName"
				>
					<a-input
						autocomplete="off"
						disabled
						v-model:value="handle_formState.ruleName"
					/>
				</a-form-item>
				<a-form-item
					label="告警内容"
					name="warnContent"
				>
					<a-input
						autocomplete="off"
						disabled
						v-model:value="handle_formState.warnContent"
					/>
				</a-form-item>
				<a-form-item
					label="告警级别"
					name="warnLevelDesc"
				>
					<a-input
						autocomplete="off"
						disabled
						v-model:value="handle_formState.warnLevelDesc"
					/>
				</a-form-item>
				<a-form-item
					label="触发时间"
					name="triggerTime"
				>
					<a-input
						autocomplete="off"
						disabled
						v-model:value="handle_formState.triggerTime"
					/>
				</a-form-item>
				<a-form-item
					label="异常类型"
					name="unusualType"
					:rules="[{ required: true }]"
				>
					<a-select
						ref="select"
						v-model:value="handle_formState.unusualType"
						:getPopupContainer="(e) => e.parentNode"
						placeholder="请选择反馈类型"
					>
						<a-select-option value="1">异常</a-select-option>
						<a-select-option value="2">误报/无需反馈</a-select-option>
					</a-select>
				</a-form-item>
				<a-form-item
					label="原因"
					name="reason"
					:rules="[{ required: true, message: '请输入原因' }]"
				>
					<!-- v-if="handle_formState.unusualType == 1 ? true : false" -->
					<a-textarea v-model:value="handle_formState.reason" />
				</a-form-item>
				<a-form-item
					v-if="handle_formState.unusualType == 1 ? true : false"
					label="图片"
					name="imageUrl"
				>
					<UploadImg
						:defaultImg="defaultImgMess"
						ref="upImg"
						:imgNumber="3"
						@upDone="upDone"
					></UploadImg>
				</a-form-item>
			</a-form>
			<template #footer>
				<a-space
					class="drawer_footer"
					align="center"
				>
					<a-button @click="handleCancle">取消</a-button>
					<a-button
						:loading="handle_loading"
						@click="handleUpload"
						type="primary"
					>
						确定
					</a-button>
				</a-space>
			</template>
		</a-drawer>

		<!-- 处理结果的展示 -->
		<a-drawer
			v-model:open="handle_drawer_result"
			class="custom-class"
			root-class-name="root-class-name"
			:closable="true"
			:getContainer="false"
			title="处理结果"
			placement="right"
			width="38.5%"
		>
			<div class="form_self">
				<div class="form_item">
					<div class="form_label">异常规则名称</div>
					<div class="form_value">{{ handle_result_data.ruleName }}</div>
				</div>
				<div class="form_item">
					<div class="form_label">告警内容</div>
					<div class="form_value">{{ handle_result_data.warnContent }}</div>
				</div>
				<div class="form_item">
					<div class="form_label">告警级别</div>
					<div class="form_value">
						<div
							:class="[
								handle_result_data.warnLevel == 1 ? 'error_tag' : '',
								handle_result_data.warnLevel == 2 ? 'warning_tag' : '',
								handle_result_data.warnLevel == 3 ? 'normal_tag' : '',
								handle_result_data.warnLevel == 4 ? 'success_tag' : ''
							]"
						>
							{{ handle_result_data.warnLevelDesc }}
						</div>
					</div>
				</div>
				<div class="form_item">
					<div class="form_label">异常类型</div>
					<div class="form_value">
						<span
							class="warning_color"
							style="font-family: SourceHanSansCN-Regular; font-weight: 400; font-size: 14px"
						>
							{{ handle_result_data.unusualTypeDesc }}
						</span>
					</div>
				</div>
				<div class="form_item">
					<div class="form_label">原因</div>
					<div class="form_value">
						{{ handle_result_data.reason }}
					</div>
				</div>
				<div class="form_item">
					<div class="form_label">处理人</div>
					<div class="form_value">{{ handle_result_data.updateBy }}</div>
				</div>
				<div class="form_item">
					<div class="form_label">处理时间</div>
					<div class="form_value">{{ handle_result_data.updateTime }}</div>
				</div>

				<div
					class="form_item"
					v-if="handle_result_data.img_list.length > 0 ? true : false"
				>
					<div class="form_label">图片</div>
					<div class="handle_result">
						<img
							v-for="(item, index) in handle_result_data.img_list"
							:src="item"
							alt="#"
							:key="index"
						/>
					</div>
				</div>
			</div>
			<!-- <template #footer>
        <a-space class="drawer_footer" align="center">
          <a-button>取消</a-button>
          <a-button type="primary">确定</a-button>
        </a-space>
      </template> -->
		</a-drawer>
	</div>
</template>

<script setup>
	// import zhCN from 'ant-design-vue/es/locale/zh_CN'
	import { ref, onMounted, reactive, watch } from 'vue';
	import { useRouter } from 'vue-router';
	import { message } from 'ant-design-vue';
	// 分页的结构
	import Pagination from '/components/Pagination.vue';
	import UploadImg from '/components/UploadImg.vue';
	// 请求方法
	import {
		getUnnormalListHttp,
		getUnnormalCountHttp,
		getWarningLevelHttp,
		getWarningStatusHttp,
		getWarningCountHttp,
		confirmUnnormalHttp,
		handleUnnormalHttp,
		getUnnormalDetailHttp
	} from './api.js';
	import { getDictHttp, getObjectUrl } from '/assets/js/commonApi.js';
	import dayjs from 'dayjs';
	import { result } from 'lodash-es';

	// ***********************************************************************************************************
	//                                                   全局配置                                                //
	// ***********************************************************************************************************

	const router = useRouter();
	const project_id = ref();
	const triggerTimeEnd = ref(dayjs().format('YYYY-MM-DD HH:mm:ss'));
	const triggerTimeStart = ref(
		dayjs().startOf('month').startOf('day').format('YYYY-MM-DD HH:mm:ss')
	);

	// 异常触发累计
	const unnormal_sum = ref(0);

	// 触发频次 总数
	const frequency_mess = ref([]);

	// 条件改变搜索所有的
	const search_loading = ref(false);

	const warning_level_1 = ref([{ totalNum: null, warnLevel: null, warnLevelDesc: null }]);
	const warning_level_2 = ref([]);

	const handle_status_mess_1 = ref([{ totalNum: null, warnStatus: null, warnLevelDesc: null }]);
	const handle_status_mess_2 = ref([]);

	const searchAll = () => {
		//////console.log('查询全部')
		// search_loading.value = true
		pageSize.value = 10;
		pageIndex.value = 1;
		// 重置分页的数据
		pagination_ref.value.reset();
		// 异常触发累计
		let params_unnormal = {
			projectId: Number(project_id.value),
			triggerTimeEnd: triggerTimeEnd.value,
			triggerTimeStart: triggerTimeStart.value
		};

		// 告警触发频次 0 -100
		let params_warning_count = {
			projectId: project_id.value,
			intervalList: [
				{ triggerCountMax: 100, triggerCountMin: 0 },
				{ triggerCountMax: 500, triggerCountMin: 100 },
				{ triggerCountMax: -1, triggerCountMin: 500 }
			],
			triggerTimeEnd: triggerTimeEnd.value,
			triggerTimeStart: triggerTimeStart.value
		};
		// let params_warning_count_100_500 = {
		//   projectId: project_id.value,
		//   intervalList: [
		//     {
		//       triggerCountMax: 500,
		//       triggerCountMin: 100
		//     }
		//   ]
		// }

		// 触发频次 0 -100
		getWarningCountHttp(params_warning_count)
			.then((data) => {
				frequency_mess.value = data;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});

		//
		getUnnormalCountHttp(params_unnormal)
			.then((data) => {
				////console.log(data)
				unnormal_sum.value = data;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
		// 告警级别

		getWarningLevelHttp(params_unnormal)
			.then((data) => {
				let temp_data_1 = [];
				let temp_data_2 = [];
				data.forEach((item) => {
					let { totalNum, warnLevel, warnLevelDesc } = item;
					if (item.warnLevel == 1) {
						temp_data_1.push({ totalNum, warnLevel, warnLevelDesc });
					} else {
						temp_data_2.push({ totalNum, warnLevel, warnLevelDesc });
					}
				});
				warning_level_1.value = temp_data_1;
				//console.log(warning_level_1.value)
				warning_level_2.value = temp_data_2;
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});

		// 处理状态

		getWarningStatusHttp(params_unnormal)
			.then((data) => {
				//console.log(data)
				handle_status_mess_1.value = [];
				handle_status_mess_2.value = [];
				data.forEach((item) => {
					let { totalNum, warnStatus, warnStatusDesc } = item;
					// total = total + Number(item.totalNum)
					if (item.warnStatus == 1) {
						//未处理
						//////console.log('暂未展示未处理')
						handle_status_mess_1.value.push({ totalNum, warnStatus, warnStatusDesc });
					} else {
						handle_status_mess_2.value.push({ totalNum, warnStatus, warnStatusDesc });
					}
				});
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});

		// 查询列表
		getList();
	};

	// 菜单切换
	const currentThirdMenu = ref(0);
	const menuChange = (num) => {
		currentThirdMenu.value = num;
		if (num) {
			// 规则配置
			router.push('/ruleSetting');
		} else {
			// 异常查询
			router.push('/unNormalControl');
		}
	};

	// --------------------------------------报警级别

	// ***********************************************************************************************************
	//                                                   全局配置                                                //
	// ***********************************************************************************************************
	const upImg = ref();
	// ***********************************************************************************************************
	//                                                   时间选择                                                //
	// ***********************************************************************************************************

	const time_status = ref(false);
	// 初始时间的设置当前月的数据
	const time_value = ref([dayjs().startOf('month'), dayjs()]);
	watch(time_value, (newVal) => {
		//////console.log(newVal)
		if (!newVal) {
			time_status.value = true;
			return;
		}
		// 起始时间增加30天
		let time_30 = dayjs(newVal[0]).add(30, 'day');
		let time_result = dayjs(newVal[1]).isBefore(time_30);
		if (time_result) {
			// 满足要求 时间范围要求在30天以内
			time_status.value = false;
		} else {
			time_status.value = true;
			message.error('选择起止时间对触发时间进行查询最长支持30天！');
			return;
		}
		triggerTimeEnd.value = dayjs(newVal[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss');
		triggerTimeStart.value = dayjs(newVal[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss');
	});
	// ***********************************************************************************************************
	//                                                   时间选择                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                   重置条件                                                //
	// ***********************************************************************************************************
	const resetInput = () => {
		// 重置时间
		time_value.value = [dayjs().startOf('month'), dayjs()];
		triggerTimeEnd.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
		triggerTimeStart.value = dayjs().startOf('month').startOf('day').format('YYYY-MM-DD HH:mm:ss');

		pageSize.value = 10;
		pageIndex.value = 1;
		// 重置分页的数据
		pagination_ref.value.reset();
		// 重新请求
		warnLevel_table.value = null;
		warn_status_table.value = null;
		// warning_level_list_all.value = []
		// warning_status_list_all.value = []
		searchAll();
	};

	// ***********************************************************************************************************
	//                                                   重置条件                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                  分页处理                                                //
	// ***********************************************************************************************************

	const pagination_ref = ref();
	const pageIndex = ref(1);
	const pageSize = ref(10);
	const totalNum = ref(0); // 表格数据总数
	const changePageSize = (currentPageMess) => {
		pageSize.value = currentPageMess.currentPageSize;
		pageIndex.value = currentPageMess.currentPage;
		getList();
	};
	// ***********************************************************************************************************
	//                                                   分页处理                                                //
	// ***********************************************************************************************************

	// ***********************************************************************************************************
	//                                                   异常列表                                                //
	// ***********************************************************************************************************

	// 告警级别的选择

	// const handleChangeWarning = (item) => {
	//   warnLevel_table.value = Number(item.value)
	//   if (item.value == 0) {
	//     warnLevel_table.value = null
	//   }
	//   // 重置分页条件
	//   pageSize.value = 10
	//   pageIndex.value = 1
	//   // 重置分页的数据
	//   pagination_ref.value.reset()
	//   // 重新请求
	//   getList()
	// }

	// 处理状态的选择
	// const handleChangeHandleStatus = (item) => {
	//   warn_status_table.value = Number(item.value)
	//   if (item.value == 0) {
	//     warn_status_table.value = null
	//   }
	//   // 重置分页条件
	//   pageSize.value = 10
	//   pageIndex.value = 1
	//   // 重置分页的数据
	//   pagination_ref.value.reset()
	//   // 重新请求
	//   getList()
	// }

	// -------------------------------------------------------异常确认提交
	const confirmUpload = (item) => {
		if (item.warnStatus != 1) {
			// 不是未处理的状态，不能使用确认
			message.warning('目前状态下，该功能暂无法使用！');
			return;
		}
		let params = item.id;
		confirmUnnormalHttp(params)
			.then((data) => {
				message.success(data);
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
			});
	};

	// -------------------------------------------------------异常确认提交
	// const handleResultSelect =

	// -------------------------------------------------处理
	// 异常处理的表单
	const handle_formState = reactive({
		id: null,
		ruleName: null,
		warnContent: null,
		warnLevel: null,
		warnLevelDesc: null,
		warnStatus: null,
		triggerTime: null,
		unusualType: null,
		reason: null,
		imageUrl: null
	});

	// 监测异常类型的改变
	watch(
		() => handle_formState.unusualType,
		(newVal) => {
			//////console.log(newVal);
			handle_formState.unusualType = newVal;
			//////console.log(handle_formState);
		},
		{ deep: true }
	);

	const handle_drawer_status = ref(false);
	// 处理抽屉的打开
	const handleDrawerOpen = (item) => {
		if (item.warnStatus != 1) {
			message.warning('目前状态下，该功能暂无法使用！');
			return;
		}
		img_up_list.value = [];

		let {
			id,
			ruleName,
			warnContent,
			warnLevel,
			warnLevelDesc,
			warnStatus,
			triggerTime,
			unusualType,
			reason,
			imageUrl
		} = item;

		Object.assign(handle_formState, {
			id,
			ruleName,
			warnContent,
			warnLevel,
			warnLevelDesc,
			warnStatus,
			triggerTime,
			unusualType,
			reason,
			imageUrl
		});

		handle_drawer_status.value = true;
	};
	// 处理抽屉的取消
	const handleCancle = () => {
		handle_drawer_status.value = false;
	};
	const closeDrawer = () => {
		// 处理抽屉的关闭

		upImg.value.reset();
	};
	// 处理状态
	const handle_loading = ref(false);
	// 处理抽屉的提交
	const handle_form = ref();
	const handleUpload = () => {
		//////console.log(handle_formState.id);
		handle_loading.value = true;
		handle_form.value
			.validateFields()
			.then((data) => {
				let params = {
					id: handle_formState.id,
					triggerTime: handle_formState.triggerTime,
					reason: handle_formState.reason,
					unusualType: Number(handle_formState.unusualType)
				};
				// if (handle_formState.unusualType == 1) {
				//   params.reason = handle_formState.reason
				// }
				if (img_up_list.value.length > 0) {
					let temp_data = JSON.parse(JSON.stringify(img_up_list.value));
					params.imageUrl = temp_data.join(',');
				}
				// console.log(params)
				// console.log(handle_formState)
				handleUnnormalHttp(params)
					.then((data) => {
						message.success(data);
						handle_loading.value = false;
						handle_drawer_status.value = false;
						getList();
					})
					.catch((error) => {
						handle_loading.value = false;
						message.error(error.message);
						setTimeout(() => {
							message.destroy();
						}, 1500);
					});
			})
			.catch(() => {
				handle_loading.value = false;
			});
	};
	// -------------------------------------------------处理

	// -------------------------------------------------处理结果
	// 处理结果的数据
	const handle_result_data = reactive({
		id: null,
		imageUrl: null,
		reason: null,
		ruleId: null,
		ruleName: null,
		triggerTime: null,
		unusualType: null,
		unusualTypeDesc: null,
		warnContent: null,
		warnLevel: null,
		warnLevelDesc: null,
		updateBy: null,
		updateTime: null,
		img_list: []
	});

	// 处理结果的抽屉
	const handle_drawer_result = ref(false);
	const handleResultDrawerOpen = (item) => {
		// 重置
		////console.log(item.warnStatus)
		if (item.warnStatus != 3 && item.warnStatus != 4) {
			// 不是已处理的状态，不能使用处理结果
			message.warning('目前状态下，该功能暂无法使用！');
			return;
		}
		// upImg.value.reset()
		handle_drawer_result.value = true;

		let params = { id: item.id, triggerTime: item.triggerTime };
		getUnnormalDetailHttp(params)
			.then((data) => {
				let {
					id,
					imageUrl,
					reason,
					ruleId,
					ruleName,
					triggerTime,
					unusualType,
					unusualTypeDesc,
					warnContent,
					warnLevel,
					warnLevelDesc,
					updateBy,
					updateTime
				} = data;

				return data;

				// 成功以后关闭抽屉
				// handle_drawer_result.value = false
			})
			.then((data) => {
				let params_image_list = [];
				if (data.imageUrl) {
					params_image_list = data.imageUrl.split(',');
				}
				let result_data = data;

				if (params_image_list.length > 0) {
					let params = { objectKeys: params_image_list };
					getObjectUrl(params).then((data) => {
						let img_list = data.data;
						result_data.img_list = img_list;
						Object.assign(handle_result_data, result_data);
					});
				} else {
					result_data.img_list = [];
					Object.assign(handle_result_data, result_data);
				}
			})
			.catch((error) => {
				message.error(error.message);
				setTimeout(() => {
					message.destroy();
				}, 1500);
				Object.assign(handle_drawer_result, {
					id: null,
					imageUrl: null,
					reason: null,
					ruleId: null,
					ruleName: null,
					triggerTime: null,
					unusualType: null,
					unusualTypeDesc: null,
					warnContent: null,
					warnLevel: null,
					warnLevelDesc: null,
					updateBy: null,
					updateTime: null
				});
			});
	};

	// -------------------------------------------------处理结果

	// --------------------------------------------------规则配置跳转
	const ruleSetting = (record) => {
		////console.log(record)
		router.push('ruleSetting');
	};

	// --------------------------------------------------规则配置跳转

	// -------------------------------------------------列表数据
	const columns = [
		{ title: '序号', key: 'no', dataIndex: 'no', align: 'center' },
		{ title: '预警类型', key: 'warningTypeDesc', dataIndex: 'warningTypeDesc', minWidth: 100 },
		{ title: '异常规则名称', key: 'ruleName', dataIndex: 'ruleName' },
		{ title: '告警内容', key: 'warnContent', dataIndex: 'warnContent', width: 280 },
		{ title: '告警级别', key: 'warnLevelDesc', dataIndex: 'warnLevelDesc', align: 'center' },
		{ title: '处理状态', key: 'warnStatusDesc', dataIndex: 'warnStatusDesc', align: 'center' },
		{ title: '触发时间', key: 'triggerTime', dataIndex: 'triggerTime' },
		{ title: '操作', key: 'control', dataIndex: 'control', width: 280 }
	];
	const dataSource = ref([]);

	// 查询列表
	const getList = () => {
		let params = {
			pageIndex: pageIndex.value,
			pageSize: pageSize.value,
			projectId: project_id.value,
			triggerTimeEnd: triggerTimeEnd.value,
			triggerTimeStart: triggerTimeStart.value
		};
		if (warnLevel_table.value) {
			params.warnLevel = Number(warnLevel_table.value);
		}
		if (warn_status_table.value) {
			params.warnStatus = Number(warn_status_table.value);
		}

		getUnnormalListHttp(params)
			.then((data) => {
				////console.log(data)
				totalNum.value = data.total;
				////console.log( totalNum.value);
				let temp_data = [];
				if (data && data.total) {
					for (let i = 0; i < data.list.length; i++) {
						let {
							id,
							ruleId,
							ruleName,
							triggerTime,
							warnContent,
							warnLevel,
							warnLevelDesc,
							warnStatus,
							warnStatusDesc,
							warnType,
							warningTypeDesc
						} = data.list[i];
						temp_data.push({
							no: i + 1,
							id,
							ruleId,
							ruleName,
							triggerTime,
							warnContent,
							warnLevel,
							warnLevelDesc,
							warnStatus,
							warnStatusDesc,
							warnType,
							warningTypeDesc
						});
					}
					dataSource.value = temp_data;
				} else {
					dataSource.value = [];
				}
			})
			.catch((error) => {
				////console.log(error);
				// message.error(error.message)
			});
	};

	// -------------------------------------------------列表数据

	// ***********************************************************************************************************
	//                                                   异常列表                                                //
	// ***********************************************************************************************************

	// 图片组件的方法
	// 默认传给uploadimg组件的初始值
	const defaultImgMess = reactive({});

	const img_up_list = ref([]);
	const upDone = (imageData) => {
		// console.log(imageData);
		let temp_img_url = [];
		imageData.forEach((item) => {
			temp_img_url.push(item.data);
		});
		img_up_list.value = temp_img_url;
	};

	// --------------------------------------报警级别
	// --------------------------------------处理状态
	const warn_status_table = ref(null);
	const warnLevel_table = ref(null);

	const warning_level_list_all = ref([]);
	const warning_status_list_all = ref([]);
	onMounted(() => {
		// 项目id
		let project_mess = localStorage.getItem('project_mess');
		if (project_mess) {
			project_mess = JSON.parse(project_mess);
			project_id.value = project_mess.project_id;
		} else {
			message.error('未选择项目，请重新选择！');
		}
		let params = 'WARN_LEVEL';
		let params1 = 'WARN_STATUS';
		Promise.all([
			getDictHttp(params), // 功率曲线
			getDictHttp(params1)
		]).then(([data1, data2]) => {
			if (data1) {
				let result = [];
				for (let index = 0; index < data1.length; index++) {
					result.push({ label: data1[index].dictValue, value: data1[index].dictKey });
				}
				warning_level_list_all.value = result;
			}
			if (data2) {
				let result1 = [];
				for (let index = 0; index < data2.length; index++) {
					result1.push({ label: data2[index].dictValue, value: data2[index].dictKey });
				}
				warning_status_list_all.value = result1;
			}
			if (data1 && data2) {
				searchAll();
			}
		});
	});
</script>
<style lang="less" scoped>
	.current_page {
		padding-top: 32px;
		box-sizing: border-box;
		box-sizing: border-box;
	}

	.search_input_reset {
		//   margin-top: 32px;
		.time_label {
			font-family: SourceHanSansCN-Regular;
			font-weight: 400;
			font-size: 14px;
			color: #092649;
		}
	}

	.total_count {
		width: 100%;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		margin: 32px 0 25px 0;
		.card_item {
			// margin-right: 32px;
			width: 424px;
			// width: 22.1vw;
			// width: 25%;
			height: 124px;
			border-radius: 8px;
			padding: 0px 1.7vw;
			box-sizing: border-box;
			position: relative;
			.card_item_contain {
				height: 100%;
				width: 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
			}
			.device_icon {
				width: 101px;
				height: 101px;
				position: absolute;
				right: 4.5%;
				top: 16%;
			}
			&:nth-child(1) {
				background: linear-gradient(90deg, rgba(17, 129, 253, 0.12), rgba(17, 129, 253, 0.02));
				.device_icon {
					background-image: url('/assets/icon/unNormal.png');
					background-repeat: no-repeat;
					background-size: 100%;
				}
			}
			&:nth-child(2) {
				background: linear-gradient(90deg, rgba(3, 227, 161, 0.12), rgba(3, 227, 161, 0.02));
				.device_icon {
					background-image: url('/assets/icon/warning.png');
					background-repeat: no-repeat;
					background-size: 100%;
				}
			}
			&:nth-child(3) {
				background: linear-gradient(90deg, rgba(121, 133, 252, 0.12), rgba(121, 133, 252, 0.02));
				.device_icon {
					background-image: url('/assets/icon/handle_status.png');
					background-repeat: no-repeat;
					background-size: 100%;
				}
			}
			&:nth-child(4) {
				background: linear-gradient(90deg, rgba(17, 129, 253, 0.12), rgba(17, 129, 253, 0.02));
				.device_icon {
					background-image: url('/assets/icon/trigger_frequency.png');
					background-repeat: no-repeat;
					background-size: 100%;
				}
			}

			.card_title {
				font-family: SourceHanSansCN-Regular;
				font-weight: 400;
				font-size: 16px;
				color: #9ca2af;
				height: 16px;
				line-height: 16px;
			}
			.card_num {
				margin-top: 16px;
				height: 22px;
				line-height: 22px;
				i {
					font-style: normal;
				}
				span {
					display: inline-block;
					&:nth-child(1) {
						font-family: D-DIN-PRO;
						font-weight: bold;
						font-size: 32px;
						color: #092649;
					}
					&:nth-child(2) {
						font-family: SourceHanSansCN-Regular;
						font-weight: 400;
						font-size: 14px;
						color: #9ca2af;
					}
				}
			}
			.card_detail {
				margin-top: 24px;
				display: flex;
				flex-direction: row;
				justify-content: flex-start;
				span {
					display: block;
					height: 15px;
					line-height: 15px;
					font-family: SourceHanSansCN-Regular;
					font-weight: 400;
					font-size: 14px;
					color: #9ca2af;
					margin-right: 16px;
					i {
						font-style: normal;
						font-family: D-DIN-PRO;
						font-weight: bold;
						font-size: 18px;
						color: #092649;
					}
				}
			}
		}
	}

	.device_status {
		width: 46px;
		height: 26px;
		border-radius: 13px;
		line-height: 26px;
		text-align: center;
	}
	// .success_tag {
	//   background-color: rgba(3, 227, 161, 0.2);
	//   border: 1px solid #03e3a1;
	//   color: #07d297;
	// }
	// .error_tag {
	//   background-color: rgba(255, 97, 97, 0.2);
	//   border: 1px solid #ff6161;
	//   color: #ff6161;
	// }

	.handle_result {
		margin-top: 20px;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		img {
			height: 80px;
			width: 80px;
			margin-right: 24px;
		}
	}
	:deep(.ant-table) {
		min-height: 400px !important;
	}
	:deep(.ant-table-empty) {
		min-height: 400px !important;
	}
</style>
