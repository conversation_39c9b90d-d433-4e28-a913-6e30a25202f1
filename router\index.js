import { createRouter, createWebHashHistory } from 'vue-router'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'login',
      component: () => import('/pages/login/LoginPage.vue')
    },
    {
      path: '/skipLoading',
      name: 'skipLoading',
      component: () => import('/pages/login/SkipLoad.vue')
    },
    {
      path: '/404',
      name: 'nopage',
      component: () => import('/pages/login/404.vue')
    },
  ]
})

export default router
