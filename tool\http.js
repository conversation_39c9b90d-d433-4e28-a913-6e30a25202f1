import axios from "axios";
import { message } from "ant-design-vue";

const VITE_STPT_SKIP_URL = import.meta.env.VITE_STPT_SKIP_URL;
const VITE_LOGIN_URL = import.meta.env.VITE_LOGIN_URL;

// //console.log(VITE_LOGIN_URL);

import { logout, logout1 } from "/pages/login/loginHttp.js";
// 创建实例前全局配置默认值
// 调试阶段获取验证码

// 创建一个实例
const http = axios.create({
  baseURL: "",
});

// 添加请求拦截器
http.interceptors.request.use(
  function (config) {
    // 登录的接口使用默认Authorization  其他的接口使用获取的token
    let token = localStorage.getItem("token");
    let Authorization = "Basic " + token;
    config.headers.Authorization = Authorization;
    return config;
  },
  function (error) {
    return Promise.reject(error);
  }
);

// 添加响应拦截器
http.interceptors.response.use(
  function (response) {
    //console.log(response);

    // 对响应数据做点什么
    if (response.status === 200) {
      let responseStats = response.data.code;
      handleHttpStatus(responseStats);
      return response.data;
    } else {
      handleHttpStatus(response.status);
    }
  },
  function (error) {
    // 响应本身的报错
    console.log(error, 'eeeeee');
    if(error.code == 'ERR_NETWORK'){
      error.message = ''
      error.name = '网络错误！'
    }
      return Promise.reject(error);
  }
);

let messageExp = null;
/**
 * 显示错误消息
 *
 * @param {string} mess - 要显示的消息内容
 */
const messgeFn = (mess) => {
  if (messageExp) {
    message.destroy();
  }
  messageExp = message.error(mess);
};

const handleHttpStatus = (status) => {
  let options = "登录失效";
  let stpt = localStorage.getItem("stpt");

  switch (status) {
    case 400:
      options = "登录失效";
      messgeFn(options);
      window.location.href = VITE_STPT_SKIP_URL;
      break;
    case 401:
      options = "登录失效";
      messgeFn(options);
      if (stpt && stpt == 1) {
        logout().then((data) => {
          window.location.href = data;
        });
      } else {
        logout1().then(() => {
          window.location.href = VITE_LOGIN_URL;
        });
      }
      break;
    case 404:
      options = "登录失效";
      messgeFn(options);
      window.location.href = VITE_STPT_SKIP_URL;
      break;
    case 500:
      options = "后台错误,请联系后台人员查询";
      messgeFn(options);
      break;
    case 502:
      options = "登录失效";
      messgeFn(options);
      window.location.href = "/404";
      break;
    default:
      break;
  }
};

export default http;
