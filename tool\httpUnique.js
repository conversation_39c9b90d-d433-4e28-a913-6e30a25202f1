import axios from 'axios';

// 解决拦截器里面调用的请求的方法会 循环调用问题
//  生成新的实例进行调用

// 创建一个实例
const http = axios.create({
	baseURL: ''
});
// 添加请求拦截器
http.interceptors.request.use(
	function (config) {
		// 登录的接口使用默认Authorization  其他的接口使用获取的token
		let token = localStorage.getItem('token');
		let Authorization = 'Basic ' + token;
		config.headers.Authorization = Authorization;
		return config;
	},
	function (error) {
		// 对请求错误做些什么
		// console.log(error);
		// alert(error)
		return Promise.reject(error);
	}
);

export default http;
