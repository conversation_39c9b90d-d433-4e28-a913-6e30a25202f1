/**
 * 数值格式化,将数值转出字符串输出
 * 小于10时，保留1位小数点，如果小数位是0，则去掉小数点 
 * 大于等于10且小于1万时，不保留小数且用千分位显示 
 * 大于等于1万且小于10万时，用万为单位，保留1位小数点 
 * 大于等于10万且小于1亿时，用万为单位，不保留小数且用千分位显示 
 * 大于等于1亿且小于10亿时，用亿为单位，保留1位小数点 
 * 大于等于10亿时，用亿为单位，不保留小数且用千分位显示 
 * <AUTHOR>
 * @since 2024-8-15
 * @param {*} number 数值
 * @returns 格式化后的字符串
 */
export function formatNumber(number) {  
    // 如果不是数值，直接返回原值  
    if (isNaN(number)) {  
        return '-';  
    }  

    let value = Math.abs(number); // 取绝对值处理负数  
    let sign = number < 0 ? '-' : ''; // 保留负号  
  
    // 小于10时，保留1位小数点 
    if (value < 10) {   
        value = value.toFixed(1);  
         // 如果小数位是0，则去掉小数点  
         if (value.endsWith('.0')) {  
            value = value.slice(0, -2);  
         }  
        return sign + value;
    }

    // 大于等于10且小于1万时，不保留小数且用千分位显示 
    else if (value < 10000) {    
        value = new Intl.NumberFormat('zh-CN', {  
            minimumFractionDigits: 0,  
            maximumFractionDigits: 0,  
        }).format(value);  
        return sign + value;  
    }

    // 大于等于1万且小于10万时，用万为单位，保留1位小数点  
    else if (value < 100000) {  
        const wanValue = value / 10000;  
        return sign + `${wanValue.toFixed(1)}万`;  
    } 

    // 大于等于10万且小于1亿时，用万为单位，不保留小数且用千分位显示 
    else if (value < 100000000) {  
        const wanValue = value / 10000;  
        value = new Intl.NumberFormat('zh-CN', {  
            minimumFractionDigits: 0,  
            maximumFractionDigits: 0,  
        }).format(wanValue);  
        return sign + `${value}万`;  
    } 

    // 大于等于1亿且小于10亿时，用亿为单位，保留1位小数点  
    else if (value < 1000000000) {  
        const yiValue = value / 100000000;  
        return sign + `${yiValue.toFixed(1)}亿`;  
    } 

    // 大于等于10亿时，用亿为单位，不保留小数且用千分位显示 
    else {  
        const yiValue = value / 100000000;  
        value = new Intl.NumberFormat('zh-CN', {  
            minimumFractionDigits: 0,  
            maximumFractionDigits: 0,  
        }).format(yiValue);  
        return sign + `${value}亿`;  
    }  
}