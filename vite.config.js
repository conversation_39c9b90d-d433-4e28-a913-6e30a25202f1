import { fileURLToPath, URL } from 'node:url';
import path from 'node:path';
import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
// 全局自定义引入组件
import Components from 'unplugin-vue-components/vite';
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
import postcsspxtoviewport from 'postcss-px-to-viewport-8-plugin';
const loader_pxtovw = postcsspxtoviewport({
	viewportWidth: 1920,
	viewportUnit: 'vw',
	unitPrecision: 5, // 单位转换后保留的精度
	propList: ['*'], // 能转化为vw的属性列表
	fontViewportUnit: 'vw', // 字体使用的视口单位
	selectorBlackList: ['ignore-'], // 需要忽略的CSS选择器，不会转为视口单位，使用原有的px等单位。
	minPixelValue: 1, // 设置最小的转换数值，如果为1的话，只有大于1的值会被转换
	mediaQuery: true, // 媒体查询里的单位是否需要转换单位
	replace: true, //  是否直接更换属性值，而不添加备用属性
	exclude: [/pages\/homeIndex\/assets\/mainStructure.less/], // 忽略某些文件夹下的文件或特定文件，例如 'node_modules' 下的文件
	include: [], // 如果设置了include，那将只有匹配到的文件才会被转换
	landscape: false, // 是否添加根据 landscapeWidth 生成的媒体查询条件 @media (orientation: landscape)
	landscapeUnit: 'vw', // 横屏时使用的单位
	landscapeWidth: 1628 // 横屏时使用的视口宽度
});
// 具体和详情配置参考  https://vitejs.dev/config/
export default ({ command, mode }) => {
	// console.log(mode);
	// console.log(command);
	let allEnv = loadEnv(mode, process.cwd());
	// console.log(allEnv.VITE_API_URL);
	return defineConfig({
		plugins: [
			vue(),
			Components({
				resolvers: [
					AntDesignVueResolver({
						importStyle: false // css in js
					})
				]
			})
		],
		resolve: {
			alias: {
				'@': fileURLToPath(new URL('/src', import.meta.url))
			}
		},
		css: {
			preprocessorOptions: {
				less: {
					javascriptEnabled: true
				}
			},
			postcss: {
				// plugins: [loader_pxtovw]
			}
		},
		server: {
			hmr: true,
			host: '0.0.0.0', //本地的IP地址 后者 localhost
			port: 9001,
			open: true,
			proxy: {
				'/api': {
					target: allEnv.VITE_API_URL,
					changeOrigin: true,
					rewrite: (path) => {
						return path.replace(/^\/api/, '');
					}
				},
				'/dev1': {
					target: 'http://*************:8005',
					changeOrigin: true,
					rewrite: (path) => path.replace(/^\/dev1/, '')
				},
				build: {
					chunkSizeWarningLimit: 1500
				}
			}
		},
		build: {
			chunkSizeWarningLimit: 1600,
			rollupOptions: {
				input: {
					index: path.resolve(__dirname, 'index.html'),
					homeIndex: path.resolve(__dirname, 'pages/homeIndex/index.html'),
					operationCenter: path.resolve(__dirname, 'pages/operationCenter/index.html'),
					configurationManagement: path.resolve(
						__dirname,
						'pages/configurationManagement/index.html'
					),
					operationMonitoring: path.resolve(__dirname, 'pages/operationMonitoring/index.html'),
					incomeAnalysis: path.resolve(__dirname, 'pages/incomeAnalysis/index.html'),
					algorithm: path.resolve(__dirname, 'pages/algorithm/index.html')
				},
				output: {
					manualChunks(id) {
						if (id.includes('node_modules')) {
							return id.toString().split('node_modules/')[1].split('/')[0].toString();
						}
					}
				}
			}
		}
	});
};
